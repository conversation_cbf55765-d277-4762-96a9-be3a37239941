#!/bin/bash

# =============================================================================
# Test Script for Cache Cleaners
# =============================================================================
# This script tests the functionality of the cache cleaning scripts
# =============================================================================

echo "🧪 Testing Cache Cleaner Scripts"
echo "================================="

# Check if scripts exist and are executable
echo "📁 Checking script files..."

if [ -x "clean_dev_caches.sh" ]; then
    echo "✅ clean_dev_caches.sh - Found and executable"
else
    echo "❌ clean_dev_caches.sh - Missing or not executable"
    exit 1
fi

if [ -x "clean_system_advanced.sh" ]; then
    echo "✅ clean_system_advanced.sh - Found and executable"
else
    echo "❌ clean_system_advanced.sh - Missing or not executable"
    exit 1
fi

if [ -f "README_Cache_Cleaner.md" ]; then
    echo "✅ README_Cache_Cleaner.md - Found"
else
    echo "❌ README_Cache_Cleaner.md - Missing"
fi

echo ""

# Test script syntax
echo "🔍 Testing script syntax..."

if bash -n clean_dev_caches.sh; then
    echo "✅ clean_dev_caches.sh - Syntax OK"
else
    echo "❌ clean_dev_caches.sh - Syntax Error"
    exit 1
fi

if bash -n clean_system_advanced.sh; then
    echo "✅ clean_system_advanced.sh - Syntax OK"
else
    echo "❌ clean_system_advanced.sh - Syntax Error"
    exit 1
fi

echo ""

# Show current cache sizes
echo "📊 Current cache directory sizes:"
echo "================================="

if [ -d "$HOME/Library/Caches" ]; then
    echo "Total cache size: $(du -sh "$HOME/Library/Caches" | cut -f1)"
    echo ""
    echo "Top 10 largest caches:"
    du -sh "$HOME/Library/Caches"/* 2>/dev/null | sort -hr | head -10 | while read size dir; do
        echo "  $size - $(basename "$dir")"
    done
else
    echo "❌ Cache directory not found"
fi

echo ""

# Show potential space savings
echo "💾 Potential space savings:"
echo "=========================="

TOTAL_POTENTIAL=0

# Check each cache type
check_cache() {
    local name="$1"
    local path="$2"
    
    if [ -d "$path" ]; then
        local size=$(du -sb "$path" 2>/dev/null | cut -f1)
        local human_size=$(du -sh "$path" 2>/dev/null | cut -f1)
        echo "  $name: $human_size"
        TOTAL_POTENTIAL=$((TOTAL_POTENTIAL + size))
    else
        echo "  $name: Not found"
    fi
}

check_cache "Google Caches" "$HOME/Library/Caches/Google"
check_cache "Xcode Caches" "$HOME/Library/Caches/com.apple.dt.Xcode"
check_cache "CocoaPods" "$HOME/Library/Caches/CocoaPods"
check_cache "pip" "$HOME/Library/Caches/pip"
check_cache "Homebrew" "$HOME/Library/Caches/Homebrew"
check_cache "Poetry" "$HOME/Library/Caches/pypoetry"
check_cache "Composer" "$HOME/Library/Caches/composer"
check_cache "TypeScript" "$HOME/Library/Caches/typescript"

echo ""
echo "🎯 Estimated total cache cleaning potential: $(echo "scale=1; $TOTAL_POTENTIAL/1073741824" | bc 2>/dev/null || echo "Unknown")GB"

echo ""

# Check advanced cleaning potential
echo "🚀 Advanced cleaning potential:"
echo "=============================="

check_cache "Android SDK" "$HOME/Library/Android/sdk"
check_cache "iOS Simulators (User)" "$HOME/Library/Developer/CoreSimulator"
check_cache "iOS Simulators (System)" "/Library/Developer/CoreSimulator"
check_cache "Unity" "$HOME/Library/Unity"
check_cache "Xcode Archives" "$HOME/Library/Developer/Xcode/Archives"
check_cache "Xcode Derived Data" "$HOME/Library/Developer/Xcode/DerivedData"

echo ""

# Usage instructions
echo "📖 Usage Instructions:"
echo "====================="
echo "1. For safe cache cleaning (20-40GB):"
echo "   ./clean_dev_caches.sh"
echo ""
echo "2. For advanced system cleaning (50-100GB+):"
echo "   ./clean_system_advanced.sh"
echo ""
echo "3. Read the documentation:"
echo "   cat README_Cache_Cleaner.md"
echo ""

echo "✅ All tests passed! Scripts are ready to use."
echo ""
echo "⚠️  Remember to:"
echo "   • Close development applications before running"
echo "   • Backup important work before advanced cleaning"
echo "   • Check storage in System Preferences after cleaning"
