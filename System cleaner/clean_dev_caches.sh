#!/bin/bash

# =============================================================================
# Development Cache Cleaner Script
# =============================================================================
# This script cleans various development-related caches to free up disk space
# Created: $(date)
# Author: Augment Agent
# =============================================================================

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_header() {
    echo -e "${PURPLE}============================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}============================================${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_size() {
    echo -e "${CYAN}📊 $1${NC}"
}

# Function to convert bytes to human readable format
bytes_to_human() {
    local bytes=$1
    if [ $bytes -gt 1073741824 ]; then
        echo "$(echo "scale=1; $bytes/1073741824" | bc)GB"
    elif [ $bytes -gt 1048576 ]; then
        echo "$(echo "scale=1; $bytes/1048576" | bc)MB"
    elif [ $bytes -gt 1024 ]; then
        echo "$(echo "scale=1; $bytes/1024" | bc)KB"
    else
        echo "${bytes}B"
    fi
}

# Function to get directory size in bytes
get_size_bytes() {
    local dir="$1"
    if [ -d "$dir" ]; then
        du -sb "$dir" 2>/dev/null | cut -f1
    else
        echo "0"
    fi
}

# Function to get directory size in human readable format
get_size_human() {
    local dir="$1"
    if [ -d "$dir" ]; then
        du -sh "$dir" 2>/dev/null | cut -f1
    else
        echo "0B"
    fi
}

# Function to clean a cache directory
clean_cache() {
    local cache_name="$1"
    local cache_path="$2"
    local use_sudo="$3"
    
    print_info "Cleaning $cache_name cache..."
    
    if [ ! -d "$cache_path" ]; then
        print_warning "$cache_name cache directory not found: $cache_path"
        return 0
    fi
    
    # Get size before cleaning
    local size_before_bytes=$(get_size_bytes "$cache_path")
    local size_before_human=$(get_size_human "$cache_path")
    
    print_size "Before: $cache_name = $size_before_human"
    
    # Clean the cache
    if [ "$use_sudo" = "true" ]; then
        print_info "Using sudo to clean $cache_name (some files may be locked)..."
        sudo rm -rf "$cache_path"/* 2>/dev/null
    else
        rm -rf "$cache_path"/* 2>/dev/null
    fi
    
    # Get size after cleaning
    local size_after_bytes=$(get_size_bytes "$cache_path")
    local size_after_human=$(get_size_human "$cache_path")
    
    # Calculate space freed
    local space_freed_bytes=$((size_before_bytes - size_after_bytes))
    local space_freed_human=$(bytes_to_human $space_freed_bytes)
    
    print_size "After:  $cache_name = $size_after_human"
    print_success "Freed:  $space_freed_human from $cache_name"
    
    # Add to total
    TOTAL_FREED_BYTES=$((TOTAL_FREED_BYTES + space_freed_bytes))
    
    echo ""
    return $space_freed_bytes
}

# Function to safely kill processes
kill_process_safely() {
    local process_name="$1"
    print_info "Checking if $process_name is running..."
    
    if pgrep -f "$process_name" > /dev/null; then
        print_warning "$process_name is running. Attempting to close it..."
        pkill -f "$process_name" 2>/dev/null
        sleep 3
        
        if pgrep -f "$process_name" > /dev/null; then
            print_warning "$process_name is still running. You may need to close it manually."
            read -p "Press Enter when you've closed $process_name, or Ctrl+C to abort..."
        else
            print_success "$process_name closed successfully"
        fi
    else
        print_info "$process_name is not running"
    fi
}

# Initialize variables
TOTAL_FREED_BYTES=0
START_TIME=$(date)

# Main script starts here
print_header "Development Cache Cleaner"
echo -e "${CYAN}Started at: $START_TIME${NC}"
echo ""

print_info "This script will clean the following development caches:"
echo "  • Google (Chrome, Android Studio)"
echo "  • Xcode"
echo "  • CocoaPods"
echo "  • pip (Python)"
echo "  • Homebrew"
echo "  • Poetry (Python)"
echo "  • Composer (PHP)"
echo "  • TypeScript"
echo "  • Visual Studio"
echo "  • Unity"
echo ""

# Ask for confirmation
read -p "Do you want to proceed? (y/N): " -n 1 -r
echo ""
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_warning "Operation cancelled by user"
    exit 0
fi

echo ""

# Get initial total cache size
INITIAL_CACHE_SIZE_BYTES=$(get_size_bytes "$HOME/Library/Caches")
INITIAL_CACHE_SIZE_HUMAN=$(get_size_human "$HOME/Library/Caches")

print_header "Initial Cache Analysis"
print_size "Total cache size: $INITIAL_CACHE_SIZE_HUMAN"
echo ""

# Show top cache directories
print_info "Top 10 largest cache directories:"
du -sh "$HOME/Library/Caches"/* 2>/dev/null | sort -hr | head -10 | while read size dir; do
    echo -e "${CYAN}  $size${NC} - $(basename "$dir")"
done
echo ""

# Close applications that might lock cache files
print_header "Preparing for Cache Cleaning"
kill_process_safely "Google Chrome"
kill_process_safely "Android Studio"
kill_process_safely "Xcode"
echo ""

# Start cleaning caches
print_header "Cache Cleaning in Progress"

# Clean Google caches (Chrome, Android Studio)
print_info "Cleaning Google-related caches..."

# Android Studio caches (usually the largest)
clean_cache "Android Studio 2023.1" "$HOME/Library/Caches/Google/AndroidStudio2023.1" "true"
clean_cache "Android Studio 2023.3" "$HOME/Library/Caches/Google/AndroidStudio2023.3" "true"
clean_cache "Android Studio 2024.1" "$HOME/Library/Caches/Google/AndroidStudio2024.1" "true"
clean_cache "Android Studio 2024.2" "$HOME/Library/Caches/Google/AndroidStudio2024.2" "true"

# Chrome cache
clean_cache "Google Chrome" "$HOME/Library/Caches/Google/Chrome" "false"

# Xcode caches
clean_cache "Xcode" "$HOME/Library/Caches/com.apple.dt.Xcode" "false"

# Development tool caches
clean_cache "CocoaPods" "$HOME/Library/Caches/CocoaPods" "true"
clean_cache "pip (Python)" "$HOME/Library/Caches/pip" "false"
clean_cache "Homebrew" "$HOME/Library/Caches/Homebrew" "false"
clean_cache "Poetry (Python)" "$HOME/Library/Caches/pypoetry" "false"
clean_cache "Composer (PHP)" "$HOME/Library/Caches/composer" "false"
clean_cache "TypeScript" "$HOME/Library/Caches/typescript" "false"

# Additional development caches
clean_cache "Visual Studio" "$HOME/Library/Caches/VisualStudio" "false"
clean_cache "Unity Editor" "$HOME/Library/Caches/com.unity3d.UnityEditor" "false"
clean_cache "Apple Python" "$HOME/Library/Caches/com.apple.python" "false"
clean_cache "Node.js gyp" "$HOME/Library/Caches/node-gyp" "false"

# Optional: Clean some system caches (be careful with these)
print_info "Checking optional system caches..."
if [ -d "$HOME/Library/Caches/com.todesktop.230313mzl4w4u92.ShipIt" ]; then
    read -p "Clean ShipIt cache (639MB)? (y/N): " -n 1 -r
    echo ""
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        clean_cache "ShipIt" "$HOME/Library/Caches/com.todesktop.230313mzl4w4u92.ShipIt" "false"
    fi
fi

# Final summary
print_header "Cleaning Complete - Summary"

# Get final cache size
FINAL_CACHE_SIZE_BYTES=$(get_size_bytes "$HOME/Library/Caches")
FINAL_CACHE_SIZE_HUMAN=$(get_size_human "$HOME/Library/Caches")

# Calculate total space freed
TOTAL_FREED_HUMAN=$(bytes_to_human $TOTAL_FREED_BYTES)
ACTUAL_FREED_BYTES=$((INITIAL_CACHE_SIZE_BYTES - FINAL_CACHE_SIZE_BYTES))
ACTUAL_FREED_HUMAN=$(bytes_to_human $ACTUAL_FREED_BYTES)

END_TIME=$(date)

echo -e "${GREEN}🎉 Cache cleaning completed successfully!${NC}"
echo ""
print_size "Initial cache size: $INITIAL_CACHE_SIZE_HUMAN"
print_size "Final cache size:   $FINAL_CACHE_SIZE_HUMAN"
print_size "Total space freed:  $ACTUAL_FREED_HUMAN"
echo ""

# Show remaining large cache directories
print_info "Remaining large cache directories:"
du -sh "$HOME/Library/Caches"/* 2>/dev/null | sort -hr | head -5 | while read size dir; do
    if [[ "$size" =~ [0-9]+[GM] ]]; then
        echo -e "${YELLOW}  $size${NC} - $(basename "$dir")"
    fi
done

echo ""
echo -e "${CYAN}Started:  $START_TIME${NC}"
echo -e "${CYAN}Finished: $END_TIME${NC}"
echo ""

# Recommendations
print_header "Recommendations"
print_info "To free up even more space, consider:"
echo "  • Clean Android SDK (~/Library/Android/sdk) - potentially 30-50GB"
echo "  • Clean iOS Simulators (/Library/Developer/CoreSimulator) - potentially 40GB+"
echo "  • Empty Trash"
echo "  • Use Storage Management in System Preferences"
echo ""

print_success "Script completed! Check your storage in System Preferences to see the impact."
