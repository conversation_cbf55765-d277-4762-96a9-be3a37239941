import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.49.8';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface ChatRequest {
  content: string;
  threadId?: string;
}

interface ChatResponse {
  success: boolean;
  message?: string;
  error?: string;
  threadId?: string;
  citations?: Citation[];
  sources?: Source[];
}

interface Citation {
  id: string;
  text: string;
  sourceId: string;
  position: number;
}

interface Source {
  id: string;
  title: string;
  url?: string;
  type: 'document' | 'web' | 'research' | 'knowledge';
  description?: string;
  relevance?: number;
}

// Helper function to extract citations and sources from AI response
function extractCitationsAndSources(text: string): { 
  cleanText: string; 
  citations: Citation[]; 
  sources: Source[]; 
} {
  const citations: Citation[] = [];
  const sources: Source[] = [];
  const sourceMap = new Map<number, Source>();
  let cleanText = text;
  
  // Handle 【4:1†source】 format citations - extract unique positions
  const openAICitationMatches = Array.from(text.matchAll(/【(\d+):(\d+)†([^】]*)】/g));
  const uniquePositions = new Set<number>();
  
  if (openAICitationMatches.length > 0) {
    openAICitationMatches.forEach((match) => {
      const position = parseInt(match[1]);
      const sourceTitle = match[3] ? match[3].trim() : `Source ${position}`;
      
      // Only create one source per position
      if (!uniquePositions.has(position)) {
        uniquePositions.add(position);
        const citationId = `citation-${position}`;
        const sourceId = `source-${position}`;
        
        citations.push({
          id: citationId,
          text: `[${position}]`,
          sourceId: sourceId,
          position: position
        });
        
        const source: Source = {
          id: sourceId,
          title: sourceTitle || `Reference ${position}`,
          type: 'knowledge',
          description: `AI knowledge base reference`,
          relevance: 0.85 + (Math.random() * 0.15)
        };
        
        sources.push(source);
        sourceMap.set(position, source);
      }
    });
    
    // Clean all 【4:1†source】 patterns from text
    cleanText = cleanText.replace(/【\d+:\d+†[^】]*】/g, '');
  } else {
    // Fallback: Look for standard [1], [2], etc. patterns
    const citationMatches = Array.from(text.matchAll(/\[(\d+)\]/g));
    const seenPositions = new Set<number>();
    
    citationMatches.forEach((match) => {
      const position = parseInt(match[1]);
      
      if (!seenPositions.has(position)) {
        seenPositions.add(position);
        const citationId = `citation-${position}`;
        const sourceId = `source-${position}`;
        
        citations.push({
          id: citationId,
          text: match[0],
          sourceId: sourceId,
          position: position
        });
        
        sources.push({
          id: sourceId,
          title: `Reference ${position}`,
          type: 'knowledge',
          description: `Source reference from AI knowledge base`,
          relevance: 0.8 + (Math.random() * 0.2)
        });
      }
    });
  }
  
  // Clean up any remaining citation markers that appear in isolation
  cleanText = cleanText.replace(/\[\d+\]\s*$/gm, ''); // Remove citations at end of lines
  cleanText = cleanText.replace(/\n\s*\[\d+\]\s*\n/g, '\n'); // Remove standalone citation lines
  cleanText = cleanText.replace(/\[\d+\]\s*\[\d+\]/g, ''); // Remove consecutive citations
  
  // Clean up extra whitespace and normalize formatting
  cleanText = cleanText.replace(/\n{3,}/g, '\n\n'); // Reduce multiple newlines
  cleanText = cleanText.trim();
  
  return { cleanText, citations, sources };
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Get the authorization header
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      return new Response(
        JSON.stringify({ success: false, error: 'Authorization required' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Verify the user is authenticated
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: authHeader },
        },
      }
    );

    const { data: { user }, error: authError } = await supabaseClient.auth.getUser();
    if (authError || !user) {
      return new Response(
        JSON.stringify({ success: false, error: 'Authentication failed' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    const openAIApiKey = Deno.env.get('OPENAI_API_KEY');
    if (!openAIApiKey) {
      console.error('OpenAI API key not configured');
      return new Response(
        JSON.stringify({ success: false, error: 'OpenAI API key not configured' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    const assistantId = Deno.env.get('OPENAI_ASSISTANT_ID');
    if (!assistantId) {
      console.error('OpenAI Assistant ID not configured');
      return new Response(
        JSON.stringify({ success: false, error: 'OpenAI Assistant ID not configured. Please set the OPENAI_ASSISTANT_ID secret.' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Validate assistant ID format
    if (!assistantId.startsWith('asst_')) {
      console.error('Invalid Assistant ID format:', assistantId);
      return new Response(
        JSON.stringify({ success: false, error: 'Invalid Assistant ID format. It should start with "asst_".' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    const { content, threadId }: ChatRequest = await req.json();
    
    if (!content?.trim()) {
      return new Response(
        JSON.stringify({ success: false, error: 'Message content is required' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    console.log('Processing chat request for user:', user.id);
    console.log('Using Assistant ID:', assistantId);

    // Step 1: Create or use existing thread
    let thread_id = threadId;
    if (!thread_id) {
      const threadResponse = await fetch('https://api.openai.com/v1/threads', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${openAIApiKey}`,
          'Content-Type': 'application/json',
          'OpenAI-Beta': 'assistants=v2',
        },
      });

      if (!threadResponse.ok) {
        const error = await threadResponse.text();
        console.error('Failed to create thread:', error);
        return new Response(
          JSON.stringify({ success: false, error: 'Failed to create conversation thread' }),
          { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
      }

      const thread = await threadResponse.json();
      thread_id = thread.id;
      console.log('Created new thread:', thread_id);
    }

    // Step 2: Add user message to thread
    const messageResponse = await fetch(`https://api.openai.com/v1/threads/${thread_id}/messages`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openAIApiKey}`,
        'Content-Type': 'application/json',
        'OpenAI-Beta': 'assistants=v2',
      },
      body: JSON.stringify({
        role: 'user',
        content: content,
      }),
    });

    if (!messageResponse.ok) {
      const error = await messageResponse.text();
      console.error('Failed to add message:', error);
      return new Response(
        JSON.stringify({ success: false, error: 'Failed to add message to conversation' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Step 3: Run the assistant
    const runResponse = await fetch(`https://api.openai.com/v1/threads/${thread_id}/runs`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openAIApiKey}`,
        'Content-Type': 'application/json',
        'OpenAI-Beta': 'assistants=v2',
      },
      body: JSON.stringify({
        assistant_id: assistantId,
      }),
    });

    if (!runResponse.ok) {
      const error = await runResponse.text();
      console.error('Failed to run assistant:', error);
      return new Response(
        JSON.stringify({ success: false, error: 'Failed to process request with AI assistant' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    const run = await runResponse.json();

    // Step 4: Poll for completion
    let attempts = 0;
    const maxAttempts = 60;
    let runStatus;

    do {
      await new Promise((resolve) => setTimeout(resolve, 1000));
      
      const statusResponse = await fetch(`https://api.openai.com/v1/threads/${thread_id}/runs/${run.id}`, {
        headers: {
          'Authorization': `Bearer ${openAIApiKey}`,
          'OpenAI-Beta': 'assistants=v2',
        },
      });

      if (!statusResponse.ok) {
        console.error('Failed to check run status');
        break;
      }

      runStatus = await statusResponse.json();
      attempts++;
    } while (
      runStatus.status !== 'completed' &&
      runStatus.status !== 'failed' &&
      runStatus.status !== 'cancelled' &&
      attempts < maxAttempts
    );

    if (runStatus.status !== 'completed') {
      console.error(`Run failed or timed out: ${runStatus.status}`);
      return new Response(
        JSON.stringify({ success: false, error: `AI assistant ${runStatus.status}. Please try again.` }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Step 5: Retrieve assistant response
    const messagesResponse = await fetch(`https://api.openai.com/v1/threads/${thread_id}/messages`, {
      headers: {
        'Authorization': `Bearer ${openAIApiKey}`,
        'OpenAI-Beta': 'assistants=v2',
      },
    });

    if (!messagesResponse.ok) {
      console.error('Failed to retrieve messages');
      return new Response(
        JSON.stringify({ success: false, error: 'Failed to retrieve AI response' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    const messages = await messagesResponse.json();
    const assistantMessages = messages.data.filter((msg: any) => msg.role === 'assistant');
    const latest = assistantMessages[0];

    if (!latest || !latest.content.length) {
      return new Response(
        JSON.stringify({ success: false, error: 'No assistant response found' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    let message = '';
    for (const part of latest.content) {
      if (part.type === 'text') {
        message += part.text.value;
      }
    }

    // Extract citations and sources from the message
    const { cleanText, citations, sources } = extractCitationsAndSources(message);

    const response: ChatResponse = {
      success: true,
      message: cleanText,
      threadId: thread_id,
      citations: citations.length > 0 ? citations : undefined,
      sources: sources.length > 0 ? sources : undefined,
    };

    console.log('Chat request completed successfully for user:', user.id);

    return new Response(JSON.stringify(response), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });

  } catch (error: any) {
    console.error('Error in chat-with-ai function:', error);
    return new Response(
      JSON.stringify({ success: false, error: 'Internal server error' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
});
