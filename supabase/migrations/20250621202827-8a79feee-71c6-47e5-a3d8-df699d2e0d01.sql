
-- Add photo storage and goals to pets table
ALTER TABLE public.pets 
ADD COLUMN photo_url TEXT,
ADD COLUMN goals TEXT[] DEFAULT '{}';

-- Create daily health check-ins table
CREATE TABLE public.daily_health_checkins (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  pet_id UUID REFERENCES public.pets(id) ON DELETE CASCADE NOT NULL,
  user_id UUID NOT NULL,
  tenant_id UUID REFERENCES public.tenants(id) NOT NULL,
  date DATE NOT NULL,
  
  -- Digestive health
  stool_type INTEGER, -- 1-7 Bristol stool scale
  stool_consistency TEXT,
  stool_color TEXT,
  
  -- Overall health indicators
  appetite_level INTEGER CHECK (appetite_level >= 1 AND appetite_level <= 5), -- 1=poor, 5=excellent
  energy_level INTEGER CHECK (energy_level >= 1 AND energy_level <= 5),
  scratching_frequency INTEGER CHECK (scratching_frequency >= 1 AND scratching_frequency <= 5), -- 1=none, 5=constant
  vomiting BOOLEAN DEFAULT FALSE,
  
  -- Notes and AI response
  notes TEXT,
  ai_response TEXT,
  ai_thread_id TEXT,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  
  -- Ensure one check-in per pet per day
  UNIQUE(pet_id, date)
);

-- Enable RLS
ALTER TABLE public.daily_health_checkins ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view their pet's health check-ins" 
  ON public.daily_health_checkins 
  FOR SELECT 
  USING (user_id = auth.uid());

CREATE POLICY "Users can create health check-ins for their pets" 
  ON public.daily_health_checkins 
  FOR INSERT 
  WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their pet's health check-ins" 
  ON public.daily_health_checkins 
  FOR UPDATE 
  USING (user_id = auth.uid());

CREATE POLICY "Users can delete their pet's health check-ins" 
  ON public.daily_health_checkins 
  FOR DELETE 
  USING (user_id = auth.uid());

-- Create supplement tracking table
CREATE TABLE public.supplement_events (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  pet_id UUID REFERENCES public.pets(id) ON DELETE CASCADE NOT NULL,
  user_id UUID NOT NULL,
  tenant_id UUID REFERENCES public.tenants(id) NOT NULL,
  
  supplement_name TEXT NOT NULL,
  event_type TEXT NOT NULL CHECK (event_type IN ('started', 'stopped', 'modified')),
  dosage TEXT,
  frequency TEXT,
  notes TEXT,
  
  event_date DATE NOT NULL DEFAULT CURRENT_DATE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable RLS for supplement events
ALTER TABLE public.supplement_events ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage their pet's supplement events" 
  ON public.supplement_events 
  FOR ALL 
  USING (user_id = auth.uid());

-- Create storage bucket for pet photos
INSERT INTO storage.buckets (id, name, public) 
VALUES ('pet-photos', 'pet-photos', true);

-- Create storage policy for pet photos
CREATE POLICY "Users can upload pet photos" 
  ON storage.objects 
  FOR INSERT 
  WITH CHECK (bucket_id = 'pet-photos' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Anyone can view pet photos" 
  ON storage.objects 
  FOR SELECT 
  USING (bucket_id = 'pet-photos');

CREATE POLICY "Users can update their pet photos" 
  ON storage.objects 
  FOR UPDATE 
  USING (bucket_id = 'pet-photos' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can delete their pet photos" 
  ON storage.objects 
  FOR DELETE 
  USING (bucket_id = 'pet-photos' AND auth.uid()::text = (storage.foldername(name))[1]);
