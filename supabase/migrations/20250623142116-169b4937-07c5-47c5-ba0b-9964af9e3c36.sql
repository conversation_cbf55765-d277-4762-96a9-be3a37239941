
-- Extend pets table with additional fields for comprehensive pet portal
ALTER TABLE public.pets 
ADD COLUMN body_condition_score INTEGER CHECK (body_condition_score >= 1 AND body_condition_score <= 9),
ADD COLUMN microchip_id TEXT,
ADD COLUMN vet_clinic_id UUID,
ADD COLUMN dietary_preferences TEXT[] DEFAULT '{}',
ADD COLUMN current_health_status TEXT DEFAULT 'healthy' CHECK (current_health_status IN ('healthy', 'chronic_conditions', 'recently_ill'));

-- Create vet clinics table
CREATE TABLE public.vet_clinics (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  address TEXT,
  phone TEXT,
  email TEXT,
  tenant_id UUID REFERENCES public.tenants(id) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create pet medical records table
CREATE TABLE public.pet_medical_records (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  pet_id UUID REFERENCES public.pets(id) ON DELETE CASCADE NOT NULL,
  user_id UUID NOT NULL,
  tenant_id UUID REFERENCES public.tenants(id) NOT NULL,
  record_type TEXT NOT NULL CHECK (record_type IN ('vaccine', 'medication', 'condition', 'treatment')),
  name TEXT NOT NULL,
  status TEXT,
  dosage TEXT,
  frequency TEXT,
  start_date DATE,
  end_date DATE,
  due_date DATE,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create pet weight history table
CREATE TABLE public.pet_weight_history (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  pet_id UUID REFERENCES public.pets(id) ON DELETE CASCADE NOT NULL,
  user_id UUID NOT NULL,
  tenant_id UUID REFERENCES public.tenants(id) NOT NULL,
  weight_lbs NUMERIC NOT NULL,
  recorded_date DATE NOT NULL,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create pet documents table
CREATE TABLE public.pet_documents (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  pet_id UUID REFERENCES public.pets(id) ON DELETE CASCADE NOT NULL,
  user_id UUID NOT NULL,
  tenant_id UUID REFERENCES public.tenants(id) NOT NULL,
  file_name TEXT NOT NULL,
  file_url TEXT NOT NULL,
  file_type TEXT NOT NULL,
  document_category TEXT NOT NULL CHECK (document_category IN ('vet_report', 'lab_test', 'microbiome_result', 'vaccination_proof', 'other')),
  vet_name TEXT,
  service_date DATE,
  tags TEXT[] DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable RLS on all new tables
ALTER TABLE public.vet_clinics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.pet_medical_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.pet_weight_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.pet_documents ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for vet_clinics
CREATE POLICY "Users can view vet clinics in their tenant" 
  ON public.vet_clinics 
  FOR SELECT 
  USING (tenant_id = public.get_user_tenant());

CREATE POLICY "Users can create vet clinics in their tenant" 
  ON public.vet_clinics 
  FOR INSERT 
  WITH CHECK (tenant_id = public.get_user_tenant());

CREATE POLICY "Users can update vet clinics in their tenant" 
  ON public.vet_clinics 
  FOR UPDATE 
  USING (tenant_id = public.get_user_tenant());

-- Create RLS policies for pet_medical_records
CREATE POLICY "Users can manage their pet's medical records" 
  ON public.pet_medical_records 
  FOR ALL 
  USING (user_id = auth.uid());

-- Create RLS policies for pet_weight_history
CREATE POLICY "Users can manage their pet's weight history" 
  ON public.pet_weight_history 
  FOR ALL 
  USING (user_id = auth.uid());

-- Create RLS policies for pet_documents
CREATE POLICY "Users can manage their pet's documents" 
  ON public.pet_documents 
  FOR ALL 
  USING (user_id = auth.uid());

-- Create storage bucket for pet documents
INSERT INTO storage.buckets (id, name, public) 
VALUES ('pet-documents', 'pet-documents', false);

-- Create storage policies for pet documents
CREATE POLICY "Users can upload pet documents" 
  ON storage.objects 
  FOR INSERT 
  WITH CHECK (bucket_id = 'pet-documents' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can view their pet documents" 
  ON storage.objects 
  FOR SELECT 
  USING (bucket_id = 'pet-documents' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can update their pet documents" 
  ON storage.objects 
  FOR UPDATE 
  USING (bucket_id = 'pet-documents' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can delete their pet documents" 
  ON storage.objects 
  FOR DELETE 
  USING (bucket_id = 'pet-documents' AND auth.uid()::text = (storage.foldername(name))[1]);
