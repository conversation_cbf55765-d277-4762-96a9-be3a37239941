
-- Create supplement adherence tracking table
CREATE TABLE public.daily_supplement_adherence (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  pet_id UUID REFERENCES public.pets(id) ON DELETE CASCADE NOT NULL,
  user_id UUID NOT NULL,
  tenant_id UUID REFERENCES public.tenants(id) NOT NULL,
  date DATE NOT NULL,
  supplement_name TEXT NOT NULL,
  supplement_type TEXT NOT NULL CHECK (supplement_type IN ('supplement', 'medication')),
  dosage TEXT,
  timing TEXT,
  given BOOLEAN NOT NULL DEFAULT FALSE,
  given_at TIMESTAMP WITH TIME ZONE,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  
  -- Ensure one record per supplement per pet per day
  UNIQUE(pet_id, date, supplement_name)
);

-- Enable RLS
ALTER TABLE public.daily_supplement_adherence ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view their pet's supplement adherence" 
  ON public.daily_supplement_adherence 
  FOR SELECT 
  USING (user_id = auth.uid());

CREATE POLICY "Users can create supplement adherence records for their pets" 
  ON public.daily_supplement_adherence 
  FOR INSERT 
  WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their pet's supplement adherence" 
  ON public.daily_supplement_adherence 
  FOR UPDATE 
  USING (user_id = auth.uid());

CREATE POLICY "Users can delete their pet's supplement adherence" 
  ON public.daily_supplement_adherence 
  FOR DELETE 
  USING (user_id = auth.uid());
