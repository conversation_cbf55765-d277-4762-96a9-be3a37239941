
import React from 'react';
import { BrowserRouter as Router, Route, Routes } from 'react-router-dom';
import Index from "@/pages/Index";
import Login from "@/pages/Login";
import Onboarding from "@/pages/Onboarding";
import Dashboard from "@/pages/Dashboard";
import AICoach from "@/pages/AICoach";
import Supplements from "@/pages/Supplements";
import TestHistory from "@/pages/TestHistory";
import TestReport from "@/pages/TestReport";
import Settings from "@/pages/Settings";
import MembershipDemo from "@/pages/MembershipDemo";
import Community from "@/pages/Community";
import NotFound from "@/pages/NotFound";
import ScrollToTop from "@/components/ScrollToTop";
import ProtectedRoute from "@/components/ProtectedRoute";
import PetPortal from "@/pages/PetPortal";

function App() {
  return (
    <Router>
      <ScrollToTop />
      <Routes>
        <Route path="/" element={<Index />} />
        <Route path="/login" element={<Login />} />
        <Route path="/onboarding" element={<Onboarding />} />
        <Route 
          path="/dashboard" 
          element={
            <ProtectedRoute>
              <Dashboard />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="/coach" 
          element={
            <ProtectedRoute>
              <AICoach />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="/pet-portal" 
          element={
            <ProtectedRoute>
              <PetPortal />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="/supplements" 
          element={
            <ProtectedRoute>
              <Supplements />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="/tests" 
          element={
            <ProtectedRoute>
              <TestHistory />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="/test-report" 
          element={
            <ProtectedRoute>
              <TestReport />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="/settings" 
          element={
            <ProtectedRoute>
              <Settings />
            </ProtectedRoute>
          } 
        />
        <Route path="/membership-demo" element={<MembershipDemo />} />
        <Route path="/community" element={<Community />} />
        <Route path="*" element={<NotFound />} />
      </Routes>
    </Router>
  );
}

export default App;
