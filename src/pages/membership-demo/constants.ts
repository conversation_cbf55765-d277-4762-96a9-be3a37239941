
// Full features with SENSE
export const SENSE_FEATURES = [
  "Initial Health Assessment Call",
  "1 Microbiome Test Kit",
  "SENSE Score",
  "Pet Health Portal",
  "Personalized Health Protocol",
  "Lifestyle coaching",
  "Monthly health advisor calls",
  "Daily check-ins and streaks",
  "Daily reminders and recommendations",
  "24/7 Smart Health Coach Access", 
  "Quarterly Health Reviews",
  "Free Shipping on all AutoShip shipments",
  "20% member only discount on all products",
  "Priority Customer Support"
];

// Basic features without SENSE
export const BASIC_FEATURES = [
  "Initial Health Assessment Call",
  "1 Microbiome Test Kit",
  "Monthly health advisor calls",
  "Quarterly Health Reviews",
  "Free Shipping on all AutoShip shipments",
  "20% member only discount on all products",
  "Priority Customer Support"
];

// Complete Plan Pricing (previously SENSE)
export const completeAnnualPrice = 299;
export const completeMonthlyBase = completeAnnualPrice / 9;
export const completeMonthlyPrice = Math.round(completeMonthlyBase);
export const completeSavingsPercentage = Math.round(((completeMonthlyPrice * 12) - completeAnnualPrice) / (completeMonthlyPrice * 12) * 100);

// Essential Plan Pricing (previously Basic) - Annual $215 with 25% savings
export const essentialAnnualPrice = 215;
export const essentialMonthlyPrice = Math.round(essentialAnnualPrice / 0.75 / 12);
export const essentialSavingsPercentage = Math.round(((essentialMonthlyPrice * 12) - essentialAnnualPrice) / (essentialMonthlyPrice * 12) * 100);
