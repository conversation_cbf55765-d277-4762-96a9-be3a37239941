
import React from "react";

type MembershipType = "annual" | "monthly";

interface CompactMembershipCardProps {
  selectedMembership: MembershipType;
  annualPrice: number;
  monthlyPrice: number;
  savingsPercentage: number;
  features: string[];
  isPremium: boolean;
}

const CompactMembershipCard: React.FC<CompactMembershipCardProps> = ({
  selectedMembership,
  annualPrice,
  monthlyPrice,
  savingsPercentage,
  features,
  isPremium
}) => {
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  // Filter out specific features for separate display
  const testKitFeature = features.find(feature => feature.includes("Microbiome Test Kit"));
  const assessmentCallFeature = features.find(feature => feature.includes("Initial Health Assessment"));
  const otherFeatures = features.filter(
    feature => !feature.includes("Microbiome Test Kit") && !feature.includes("Initial Health Assessment")
  );

  const borderColor = isPremium ? "border-brand-primary" : "border-gray-300";
  const accentColor = isPremium ? "bg-brand-accent/20" : "bg-gray-50";
  const textColor = isPremium ? "text-brand-primary" : "text-gray-700";
  const badgeColor = isPremium ? "bg-brand-primary/10 text-brand-primary" : "bg-gray-200 text-gray-700";
  const checkmarkBg = isPremium ? "bg-brand-primary" : "bg-gray-500";

  return (
    <div className={`relative rounded-xl border overflow-hidden transition-all duration-300 shadow-lg ${borderColor} max-w-lg w-full`}>
      {/* Best Value Banner - only show for annual premium */}
      {selectedMembership === "annual" && isPremium && (
        <div className="bg-brand-accent text-brand-primary font-bold text-xs text-center py-1">
          BEST VALUE PLAN
        </div>
      )}
      
      {/* Header - More Compact */}
      <div className={`p-4 bg-gradient-to-b from-${isPremium ? 'brand-accent/20' : 'gray-100'} to-transparent border-b`}>
        <h3 className={`text-lg font-serif ${textColor}`}>
          {selectedMembership === "annual" ? "Annual" : "Monthly"} {isPremium ? "Complete" : "Essential"}
        </h3>
        <div className="flex flex-col mt-1">
          <div className="flex items-baseline">
            <span className="text-2xl font-bold">
              {selectedMembership === "annual" 
                ? formatCurrency(annualPrice) 
                : formatCurrency(monthlyPrice)
              }
            </span>
            <span className="text-muted-foreground ml-2 text-xs">
              {selectedMembership === "annual" ? "/year" : "/month"}
            </span>
          </div>
          {selectedMembership === "annual" ? (
            <div className={`mt-1 ${isPremium ? 'text-brand-primary' : 'text-gray-600'} font-medium`}>
              <span className="text-xs">{formatCurrency(annualPrice / 12)}/mo</span> 
              <span className={`ml-2 inline-block ${badgeColor} text-xs px-2 py-0.5 rounded-full`}>
                Save {savingsPercentage}%
              </span>
            </div>
          ) : (
            <div className="mt-1 text-muted-foreground">
              <span className="text-xs">{formatCurrency(monthlyPrice * 12)}/year</span>
            </div>
          )}
        </div>
        <p className="text-xs text-muted-foreground mt-1">
          Billed {selectedMembership === "annual" ? "annually in one payment" : "monthly"}
        </p>
      </div>
      
      {/* Features Section - More Compact */}
      <div className="p-4">
        <div className={`text-xs font-medium mb-2 ${textColor}`}>
          {isPremium ? "Expert-guided care with AI insights" : "Essential care for your pet's health"}
        </div>
        
        {/* Test Kit Feature */}
        {testKitFeature && (
          <div className={`mb-2 ${accentColor} p-2 rounded-lg border ${isPremium ? 'border-brand-primary/20' : 'border-gray-200'} shadow-sm`}>
            <div className="flex items-center">
              <span className="font-medium text-xs">{testKitFeature}</span>
              <span className={`ml-2 ${badgeColor} text-[10px] py-0 h-3 px-2 rounded-full inline-block text-xs`}>INCLUDED</span>
            </div>
            <span className={`text-xs font-bold ${isPremium ? 'text-brand-primary' : 'text-gray-600'}`}>$125 value</span>
          </div>
        )}
        
        {/* Assessment Call Feature */}
        {assessmentCallFeature && (
          <div className={`mb-3 ${accentColor} p-2 rounded-lg border ${isPremium ? 'border-brand-primary/20' : 'border-gray-200'} shadow-sm`}>
            <div className="flex items-center">
              <span className="font-medium text-xs">{assessmentCallFeature}</span>
              <span className={`ml-2 ${badgeColor} text-[10px] py-0 h-3 px-2 rounded-full inline-block`}>INCLUDED</span>
            </div>
            <span className={`text-xs font-bold ${isPremium ? 'text-brand-primary' : 'text-gray-600'}`}>$90 value</span>
          </div>
        )}
        
        {/* Other Features - More Compact */}
        <div className="space-y-2">
          {otherFeatures.map((feature, idx) => (
            <div key={idx} className="flex items-start">
              <div className={`rounded-full flex items-center justify-center w-4 h-4 min-w-4 min-h-4 text-white ${checkmarkBg} mt-0.5`}>
                <svg className="w-2 h-2" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M10 3L4.5 8.5L2 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                </svg>
              </div>
              <span className="ml-2 text-xs">{feature}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default CompactMembershipCard;
