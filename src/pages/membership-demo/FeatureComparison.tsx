
import React from "react";
import { useIsMobile } from "@/hooks/use-mobile";

const FeatureComparison: React.FC = () => {
  const isMobile = useIsMobile();

  return (
    <div className="mt-8 bg-white rounded-xl p-4 shadow-lg">
      <h3 className="text-lg font-serif text-center mb-4">What's Included in Complete vs Essential</h3>
      <div className={`grid ${isMobile ? "grid-cols-1 gap-4" : "grid-cols-2 gap-6"}`}>
        <div>
          <h4 className="font-semibold text-brand-primary mb-2 text-sm">Complete Exclusive Features</h4>
          <ul className="space-y-1 text-xs">
            <li className="flex items-center"><span className="text-green-500 mr-2">✓</span>SENSE Score</li>
            <li className="flex items-center"><span className="text-green-500 mr-2">✓</span>Pet Health Portal</li>
            <li className="flex items-center"><span className="text-green-500 mr-2">✓</span>Personalized Health Protocol</li>
            <li className="flex items-center"><span className="text-green-500 mr-2">✓</span>Lifestyle coaching</li>
            <li className="flex items-center"><span className="text-green-500 mr-2">✓</span>Daily check-ins and streaks</li>
            <li className="flex items-center"><span className="text-green-500 mr-2">✓</span>Daily reminders and recommendations</li>
            <li className="flex items-center"><span className="text-green-500 mr-2">✓</span>24/7 Smart Health Coach Access</li>
          </ul>
        </div>
        <div>
          <h4 className="font-semibold text-gray-600 mb-2 text-sm">Essential Plan Features</h4>
          <ul className="space-y-1 text-xs text-gray-600">
            <li className="flex items-center"><span className="text-red-500 mr-2">✗</span>SENSE Score</li>
            <li className="flex items-center"><span className="text-red-500 mr-2">✗</span>Pet Health Portal</li>
            <li className="flex items-center"><span className="text-red-500 mr-2">✗</span>Personalized Health Protocol</li>
            <li className="flex items-center"><span className="text-red-500 mr-2">✗</span>Lifestyle coaching</li>
            <li className="flex items-center"><span className="text-red-500 mr-2">✗</span>Daily check-ins and streaks</li>
            <li className="flex items-center"><span className="text-red-500 mr-2">✗</span>Daily reminders and recommendations</li>
            <li className="flex items-center"><span className="text-red-500 mr-2">✗</span>24/7 Smart Health Coach Access</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default FeatureComparison;
