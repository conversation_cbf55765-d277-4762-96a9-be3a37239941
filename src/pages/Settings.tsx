import React from "react";
import { Layout } from "@/components/Layout";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { ProfileTab } from "@/components/settings/ProfileTab";
import { AccountTab } from "@/components/settings/AccountTab";
import { NotificationsTab } from "@/components/settings/NotificationsTab";
import { BillingTab } from "@/components/settings/BillingTab";
import { MembershipTab } from "@/components/settings/MembershipTab";
import { Button } from "@/components/ui/button";
import { Heart, ArrowRight } from "lucide-react";
import { Link } from "react-router-dom";

const Settings = () => {
  return (
    <Layout>
      <div className="w-full max-w-4xl mx-auto">
        <div className="space-y-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
            <p className="text-muted-foreground">
              Manage your account settings and preferences.
            </p>
          </div>

          {/* Pet Portal Notice */}
          <div className="bg-brand-light/30 border border-brand-primary/20 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Heart className="w-5 h-5 text-brand-primary" />
                <div>
                  <h3 className="font-medium text-gray-900">Pet Information</h3>
                  <p className="text-sm text-gray-600">
                    Pet settings have moved to the dedicated Pet Portal
                  </p>
                </div>
              </div>
              <Button asChild>
                <Link to="/pet-portal" className="flex items-center gap-2">
                  Go to Pet Portal
                  <ArrowRight className="w-4 h-4" />
                </Link>
              </Button>
            </div>
          </div>

          <Tabs defaultValue="profile" className="w-full">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="profile">Profile</TabsTrigger>
              <TabsTrigger value="account">Account</TabsTrigger>
              <TabsTrigger value="notifications">Notifications</TabsTrigger>
              <TabsTrigger value="billing">Billing</TabsTrigger>
              <TabsTrigger value="membership">Membership</TabsTrigger>
            </TabsList>
            
            <TabsContent value="profile" className="mt-6">
              <ProfileTab />
            </TabsContent>
            
            <TabsContent value="account" className="mt-6">
              <AccountTab />
            </TabsContent>
            
            <TabsContent value="notifications" className="mt-6">
              <NotificationsTab />
            </TabsContent>
            
            <TabsContent value="billing" className="mt-6">
              <BillingTab />
            </TabsContent>
            
            <TabsContent value="membership" className="mt-6">
              <MembershipTab />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </Layout>
  );
};

export default Settings;
