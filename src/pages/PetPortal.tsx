
import React, { useState } from "react";
import { Layout } from "@/components/Layout";
import { PetDataProvider, usePetData } from "@/context/PetContext";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  Heart, 
  FileText, 
  Settings, 
  Stethoscope,
  Upload,
  Calendar
} from "lucide-react";
import { HealthSummaryTab } from "@/components/pet-portal/HealthSummaryTab";
import { MedicalRecordsTab } from "@/components/pet-portal/MedicalRecordsTab";
import { PetSettingsTab } from "@/components/pet-portal/PetSettingsTab";

const PetPortalContent = () => {
  const { petData } = usePetData();
  const [activeTab, setActiveTab] = useState("health-summary");

  return (
    <Layout>
      <div className="w-full max-w-5xl mx-auto px-4 py-4 md:py-6 space-y-4 md:space-y-6">
        {/* Simplified Pet Profile Header */}
        <Card className="relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-brand-primary/10 to-brand-secondary/10" />
          <CardContent className="relative p-4 md:p-6">
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
              <div className="flex items-center gap-3 md:gap-4">
                {/* Pet Avatar */}
                <div className="w-12 h-12 md:w-16 md:h-16 rounded-full bg-brand-primary/20 flex items-center justify-center flex-shrink-0">
                  <span className="text-lg md:text-2xl font-bold text-brand-primary">
                    {petData.name.charAt(0).toUpperCase()}
                  </span>
                </div>
                
                <div>
                  <h1 className="text-xl md:text-2xl font-bold text-gray-900">{petData.name}</h1>
                  <p className="text-sm md:text-base text-gray-600">
                    {petData.age} month{petData.age !== 1 ? 's' : ''} old {petData.breed}
                  </p>
                  <div className="flex items-center gap-2 mt-1">
                    <Badge variant="secondary" className="text-xs capitalize">
                      {petData.type}
                    </Badge>
                    <Badge variant="outline" className="text-xs">
                      <Heart className="w-3 h-3 mr-1" />
                      Healthy
                    </Badge>
                  </div>
                </div>
              </div>
              
              {/* Quick Actions */}
              <div className="flex gap-2 w-full sm:w-auto">
                <Button variant="outline" size="sm" className="flex-1 sm:flex-none">
                  <Upload className="w-4 h-4 mr-2" />
                  <span className="hidden sm:inline">Upload Report</span>
                  <span className="sm:hidden">Upload</span>
                </Button>
                <Button variant="outline" size="sm" className="flex-1 sm:flex-none">
                  <Calendar className="w-4 h-4 mr-2" />
                  <span className="hidden sm:inline">Schedule Vet</span>
                  <span className="sm:hidden">Schedule</span>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Simplified Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3 mb-4 md:mb-6 h-auto">
            <TabsTrigger value="health-summary" className="flex flex-col sm:flex-row items-center gap-1 sm:gap-2 py-3">
              <Heart className="w-4 h-4" />
              <span className="text-xs sm:text-sm">Health Summary</span>
            </TabsTrigger>
            <TabsTrigger value="medical-records" className="flex flex-col sm:flex-row items-center gap-1 sm:gap-2 py-3">
              <Stethoscope className="w-4 h-4" />
              <span className="text-xs sm:text-sm">Medical Records</span>
            </TabsTrigger>
            <TabsTrigger value="pet-settings" className="flex flex-col sm:flex-row items-center gap-1 sm:gap-2 py-3">
              <Settings className="w-4 h-4" />
              <span className="text-xs sm:text-sm">Pet Settings</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="health-summary" className="mt-0">
            <HealthSummaryTab />
          </TabsContent>

          <TabsContent value="medical-records" className="mt-0">
            <MedicalRecordsTab />
          </TabsContent>

          <TabsContent value="pet-settings" className="mt-0">
            <PetSettingsTab />
          </TabsContent>
        </Tabs>
      </div>
    </Layout>
  );
};

const PetPortal = () => {
  return (
    <PetDataProvider>
      <PetPortalContent />
    </PetDataProvider>
  );
};

export default PetPortal;
