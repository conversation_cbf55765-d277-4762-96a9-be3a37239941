
import React from "react";
import OnboardingLayout from "@/features/onboarding/OnboardingLayout";
import OnboardingProgress from "@/features/onboarding/OnboardingProgress";
import Step1PetType from "@/features/onboarding/Step1PetType";
import Step2PetDetails from "@/features/onboarding/Step2PetDetails";
import Step3Membership from "@/features/onboarding/Step3Membership";
import Step4Account from "@/features/onboarding/Step4Account";
import Step5Success from "@/features/onboarding/Step5Success";
import { Step } from "@/features/onboarding/types";
import { useOnboardingState } from "@/features/onboarding/hooks/useOnboardingState";
import { useOnboardingNavigation } from "@/features/onboarding/hooks/useOnboardingNavigation";

const Onboarding: React.FC = () => {
  const {
    currentStep,
    setCurrentStep,
    petType,
    setPetType,
    petName,
    setPetName,
    petBreed,
    setPetBreed,
    petAge,
    petAgeInMonths,
    petDateOfBirth,
    handleAgeSliderChange,
    handleDateOfBirthChange,
    petSex,
    setPetSex,
    petWeight,
    setPetWeight,
    healthIssues,
    ownerName,
    setOwnerName,
    ownerEmail,
    setOwnerEmail,
    phoneNumber,
    setPhoneNumber,
    addressLine1,
    setAddressLine1,
    addressLine2,
    setAddressLine2,
    city,
    setCity,
    state,
    setState,
    zipCode,
    setZipCode,
    isSubmitting,
    setIsSubmitting,
    handleCheckboxChange
  } = useOnboardingState();
  
  const { handleNextStep, handlePreviousStep } = useOnboardingNavigation({
    currentStep,
    setCurrentStep,
    petType,
    petName,
    petAge,
    petSex,
    setIsSubmitting,
    ownerName
  });

  return (
    <OnboardingLayout currentStep={currentStep}>
      {/* Progress bar */}
      <OnboardingProgress currentStep={currentStep} />

      {/* Step 1: Pet Type Selection */}
      {currentStep === 1 && (
        <Step1PetType
          petType={petType}
          petName={petName}
          petBreed={petBreed}
          setPetType={setPetType}
          setPetName={setPetName}
          setPetBreed={setPetBreed}
          onNext={handleNextStep}
        />
      )}

      {/* Step 2: Pet Details */}
      {currentStep === 2 && (
        <Step2PetDetails
          petName={petName}
          petType={petType}
          petAge={petAgeInMonths}
          petAgeInMonths={petAgeInMonths}
          petDateOfBirth={petDateOfBirth}
          petSex={petSex}
          petWeight={parseInt(petWeight) || 0}
          healthIssues={healthIssues}
          handleAgeSliderChange={handleAgeSliderChange}
          handleDateOfBirthChange={handleDateOfBirthChange}
          setPetSex={(sex: string) => setPetSex(sex as any)}
          setPetWeight={(weight: number) => setPetWeight(weight.toString())}
          handleCheckboxChange={handleCheckboxChange}
          onNext={handleNextStep}
          onPrevious={handlePreviousStep}
        />
      )}

      {/* Step 3: Membership Selection */}
      {currentStep === 3 && (
        <Step3Membership
          onNext={handleNextStep}
          onPrevious={handlePreviousStep}
        />
      )}

      {/* Step 4: Account Creation */}
      {currentStep === 4 && (
        <Step4Account
          ownerName={ownerName}
          ownerEmail={ownerEmail}
          phoneNumber={phoneNumber}
          addressLine1={addressLine1}
          addressLine2={addressLine2}
          city={city}
          state={state}
          zipCode={zipCode}
          setOwnerName={setOwnerName}
          setOwnerEmail={setOwnerEmail}
          setPhoneNumber={setPhoneNumber}
          setAddressLine1={setAddressLine1}
          setAddressLine2={setAddressLine2}
          setCity={setCity}
          setState={setState}
          setZipCode={setZipCode}
          onComplete={handleNextStep}
          onPrevious={handlePreviousStep}
          isSubmitting={isSubmitting}
        />
      )}

      {/* Step 5: Success/Congratulations */}
      {currentStep === 5 && (
        <Step5Success
          petName={petName}
          petType={petType}
          healthIssues={healthIssues}
        />
      )}
    </OnboardingLayout>
  );
};

export default Onboarding;
