
import React, { useState } from "react";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";
import { Calendar } from "@/components/ui/calendar";
import { CalendarIcon, Upload, Save, Plus, X } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { COMMON_HEALTH_CONCERNS } from "./types";

export const PetTab: React.FC = () => {
  const [healthConcerns, setHealthConcerns] = useState([
    "Digestive Issues",
    "Food Sensitivities", 
    "Joint Issues"
  ]);
  const [popoverOpen, setPopoverOpen] = useState(false);
  const [dateOfBirth, setDateOfBirth] = useState<Date>();

  const addHealthConcern = (concern: string) => {
    if (!healthConcerns.includes(concern)) {
      setHealthConcerns([...healthConcerns, concern]);
    }
    setPopoverOpen(false);
  };

  const removeHealthConcern = (concernToRemove: string) => {
    setHealthConcerns(healthConcerns.filter(concern => concern !== concernToRemove));
  };

  const availableConcerns = COMMON_HEALTH_CONCERNS.filter(
    concern => !healthConcerns.includes(concern)
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle>Bella's Profile</CardTitle>
        <CardDescription>
          Manage your pet's information and health details.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-center space-x-4">
          <Avatar className="h-20 w-20">
            <AvatarImage
              src="https://images.unsplash.com/photo-1535268647677-300dbf3d78d1"
              alt="Bella profile picture"
            />
            <AvatarFallback>B</AvatarFallback>
          </Avatar>
          <div>
            <Button variant="outline" size="sm" className="mb-2">
              <Upload className="mr-2 h-4 w-4" />
              Update Photo
            </Button>
            <p className="text-xs text-muted-foreground">
              JPG, GIF or PNG. Max size 2MB.
            </p>
          </div>
        </div>

        <Separator />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <Label htmlFor="pet-name">Name</Label>
            <Input id="pet-name" defaultValue="Bella" />
          </div>
          <div className="space-y-2">
            <Label htmlFor="pet-type">Pet Type</Label>
            <Select defaultValue="dog">
              <SelectTrigger>
                <SelectValue placeholder="Select pet type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="dog">Dog</SelectItem>
                <SelectItem value="cat">Cat</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label htmlFor="pet-breed">Breed</Label>
            <Select defaultValue="labrador">
              <SelectTrigger>
                <SelectValue placeholder="Select breed" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="labrador">Labrador Retriever</SelectItem>
                <SelectItem value="german-shepherd">German Shepherd</SelectItem>
                <SelectItem value="golden">Golden Retriever</SelectItem>
                <SelectItem value="beagle">Beagle</SelectItem>
                <SelectItem value="poodle">Poodle</SelectItem>
                <SelectItem value="mixed">Mixed Breed</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label>Date of Birth</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !dateOfBirth && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {dateOfBirth ? format(dateOfBirth, "PPP") : <span>Pick a date</span>}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={dateOfBirth}
                  onSelect={setDateOfBirth}
                  disabled={(date) =>
                    date > new Date() || date < new Date("1900-01-01")
                  }
                  initialFocus
                  className={cn("p-3 pointer-events-auto")}
                />
              </PopoverContent>
            </Popover>
          </div>
          <div className="space-y-2">
            <Label htmlFor="pet-weight">Weight (lbs)</Label>
            <Input id="pet-weight" type="number" defaultValue="65" min="1" max="200" />
          </div>
          <div className="space-y-2">
            <Label htmlFor="pet-gender">Gender</Label>
            <Select defaultValue="female">
              <SelectTrigger>
                <SelectValue placeholder="Select gender" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="female">Female</SelectItem>
                <SelectItem value="male">Male</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Separator />

        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label>Health Concerns</Label>
            <Popover open={popoverOpen} onOpenChange={setPopoverOpen}>
              <PopoverTrigger asChild>
                <Button variant="outline" size="sm" className="h-8 px-3">
                  <Plus className="mr-1 h-3 w-3" />
                  Add Concern
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80 p-0" align="end">
                <Command>
                  <CommandInput placeholder="Search health concerns..." />
                  <CommandList>
                    <CommandEmpty>No concerns found.</CommandEmpty>
                    <CommandGroup>
                      {availableConcerns.map((concern) => (
                        <CommandItem
                          key={concern}
                          onSelect={() => addHealthConcern(concern)}
                        >
                          {concern}
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </CommandList>
                </Command>
              </PopoverContent>
            </Popover>
          </div>
          <div className="flex flex-wrap gap-2 mt-2">
            {healthConcerns.map((concern, index) => (
              <Badge 
                key={index} 
                className="bg-brand-light text-brand-primary hover:bg-brand-light cursor-pointer group"
                onClick={() => removeHealthConcern(concern)}
              >
                {concern}
                <X className="ml-1 h-3 w-3 opacity-60 group-hover:opacity-100" />
              </Badge>
            ))}
            {healthConcerns.length === 0 && (
              <p className="text-sm text-muted-foreground">No health concerns added yet.</p>
            )}
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="pet-notes">Special Notes</Label>
          <textarea
            id="pet-notes"
            className="w-full min-h-[100px] rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring"
            placeholder="Any additional information about your pet..."
            defaultValue="Bella has a slight sensitivity to chicken. She prefers lamb-based foods and treats."
          ></textarea>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline">Cancel</Button>
        <Button className="bg-brand-primary hover:bg-brand-dark">
          <Save className="mr-2 h-4 w-4" />
          Save Pet Details
        </Button>
      </CardFooter>
    </Card>
  );
};
