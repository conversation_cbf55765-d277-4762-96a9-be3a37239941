import React from "react";
import { Layout } from "@/components/Layout";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { ArrowRight, Calendar, Check, ChevronRight, Clock, Package, AlertTriangle, RefreshCw } from "lucide-react";
import { Progress } from "@/components/ui/progress";
import { useToast } from "@/hooks/use-toast";
const Supplements = () => {
  const {
    toast
  } = useToast();
  return <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Sophie's Supplements</h1>
            <p className="text-muted-foreground">Personalized protocol based on diagnostic results</p>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline" size="sm" className="gap-2">
              <Calendar className="h-4 w-4" />
              View History
            </Button>
            <Button size="sm" className="gap-2 bg-brand-primary hover:bg-brand-dark">
              <Package className="h-4 w-4" />
              Track Shipment
            </Button>
          </div>
        </div>

        {/* Current Protocol */}
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle>Current Protocol</CardTitle>
              <Badge variant="outline" className="flex items-center gap-1.5 text-xs px-2.5 py-1 bg-brand-light text-brand-primary">
                <RefreshCw className="h-3 w-3" />
                <span>Updated 2 weeks ago</span>
              </Badge>
            </div>
            <CardDescription>Personalized based on Bella's latest test results</CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="morning">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="morning">Morning</TabsTrigger>
                <TabsTrigger value="mealtime">Daytime</TabsTrigger>
                <TabsTrigger value="evening">Evening</TabsTrigger>
              </TabsList>

              <TabsContent value="morning" className="pt-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="bg-white rounded-xl border p-6 relative overflow-hidden">
                    <div className="absolute top-0 right-0 w-24 h-24">
                      
                    </div>

                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="text-xl font-bold">Gut Restore</h3>
                        <p className="text-muted-foreground mt-1">FMT Capsules for total gut restoration</p>
                      </div>
                      <div className="w-16 h-16 bg-brand-light rounded-full flex items-center justify-center flex-shrink-0">
                        <img alt="Gut Restore" className="w-10 h-10 object-cover rounded-full" src="/lovable-uploads/4b06a86f-51c6-4f90-a32a-0528ff05cccb.png" />
                      </div>
                    </div>

                    <div className="mt-6">
                      <h4 className="text-sm font-medium">Benefits</h4>
                      <ul className="mt-2 space-y-2">
                        <li className="flex items-center text-sm">
                          <Check className="h-4 w-4 text-green-500 mr-2" />
                          <span>Increases beneficial bacteria</span>
                        </li>
                        <li className="flex items-center text-sm">
                          <Check className="h-4 w-4 text-green-500 mr-2" />
                          <span>Supports digestive balance</span>
                        </li>
                        <li className="flex items-center text-sm">
                          <Check className="h-4 w-4 text-green-500 mr-2" />
                          <span>Enhances nutrient absorption</span>
                        </li>
                      </ul>
                    </div>

                    <div className="mt-6">
                      <h4 className="text-sm font-medium mb-2">Dosage</h4>
                      <div className="bg-muted p-3 rounded-lg">
                        <p className="text-sm">2 large capsules with breakfast daily</p>
                      </div>
                    </div>

                    <Button className="w-full mt-6" variant="outline">
                      View Details
                      <ChevronRight className="h-4 w-4 ml-2" />
                    </Button>
                  </div>

                  <div className="bg-white rounded-xl border p-6">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="text-xl font-bold">S. boulardii + FOS</h3>
                        <p className="text-muted-foreground mt-1">Probiotic & Prebiotic powder</p>
                      </div>
                      <div className="w-16 h-16 bg-brand-light rounded-full flex items-center justify-center flex-shrink-0">
                        <img alt="Immune Support" className="w-10 h-10 object-cover rounded-full" src="/lovable-uploads/5d9cf290-ffc1-4095-963d-b2d694a6e6e0.png" />
                      </div>
                    </div>

                    <div className="mt-6">
                      <h4 className="text-sm font-medium">Benefits</h4>
                      <ul className="mt-2 space-y-2">
                        <li className="flex items-center text-sm">
                          <Check className="h-4 w-4 text-green-500 mr-2" />
                          <span>Supports immune function</span>
                        </li>
                        <li className="flex items-center text-sm">
                          <Check className="h-4 w-4 text-green-500 mr-2" />
                          <span>Provides antioxidant protection</span>
                        </li>
                        <li className="flex items-center text-sm">
                          <Check className="h-4 w-4 text-green-500 mr-2" />
                          <span>Enhances overall vitality</span>
                        </li>
                      </ul>
                    </div>

                    <div className="mt-6">
                      <h4 className="text-sm font-medium mb-2">Dosage</h4>
                      <div className="bg-muted p-3 rounded-lg">
                        <p className="text-sm">1 scoop with breakfast daily</p>
                      </div>
                    </div>

                    <Button className="w-full mt-6" variant="outline">
                      View Details
                      <ChevronRight className="h-4 w-4 ml-2" />
                    </Button>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="mealtime" className="pt-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="bg-white rounded-xl border p-6">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="text-xl font-bold">Digestive Enzymes</h3>
                        <p className="text-muted-foreground mt-1">Enhances nutrient absorption</p>
                      </div>
                      <div className="w-16 h-16 bg-brand-light rounded-full flex items-center justify-center flex-shrink-0">
                        <img src="https://images.unsplash.com/photo-1565557623262-b51c2513a641?auto=format&fit=crop&w=100&h=100" alt="Digestive Enzymes" className="w-10 h-10 object-cover rounded-full" />
                      </div>
                    </div>

                    <div className="mt-6">
                      <h4 className="text-sm font-medium">Benefits</h4>
                      <ul className="mt-2 space-y-2">
                        <li className="flex items-center text-sm">
                          <Check className="h-4 w-4 text-green-500 mr-2" />
                          <span>Improves digestion of proteins, fats, and carbs</span>
                        </li>
                        <li className="flex items-center text-sm">
                          <Check className="h-4 w-4 text-green-500 mr-2" />
                          <span>Reduces digestive discomfort</span>
                        </li>
                        <li className="flex items-center text-sm">
                          <Check className="h-4 w-4 text-green-500 mr-2" />
                          <span>Maximizes nutrient absorption</span>
                        </li>
                      </ul>
                    </div>

                    <div className="mt-6">
                      <h4 className="text-sm font-medium mb-2">Dosage</h4>
                      <div className="bg-muted p-3 rounded-lg">
                        <p className="text-sm">1 capsule with each meal (2x daily)</p>
                      </div>
                    </div>

                    <Button className="w-full mt-6" variant="outline">
                      View Details
                      <ChevronRight className="h-4 w-4 ml-2" />
                    </Button>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="evening" className="pt-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="bg-white rounded-xl border p-6">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="text-xl font-bold">Omega Support</h3>
                        <p className="text-muted-foreground mt-1">Essential fatty acids blend</p>
                      </div>
                      <div className="w-16 h-16 bg-brand-light rounded-full flex items-center justify-center flex-shrink-0">
                        <img src="https://images.unsplash.com/photo-1535185384036-28bbc8035f28?auto=format&fit=crop&w=100&h=100" alt="Omega Support" className="w-10 h-10 object-cover rounded-full" />
                      </div>
                    </div>

                    <div className="mt-6">
                      <h4 className="text-sm font-medium">Benefits</h4>
                      <ul className="mt-2 space-y-2">
                        <li className="flex items-center text-sm">
                          <Check className="h-4 w-4 text-green-500 mr-2" />
                          <span>Supports skin and coat health</span>
                        </li>
                        <li className="flex items-center text-sm">
                          <Check className="h-4 w-4 text-green-500 mr-2" />
                          <span>Reduces inflammation</span>
                        </li>
                        <li className="flex items-center text-sm">
                          <Check className="h-4 w-4 text-green-500 mr-2" />
                          <span>Promotes heart and brain health</span>
                        </li>
                      </ul>
                    </div>

                    <div className="mt-6">
                      <h4 className="text-sm font-medium mb-2">Dosage</h4>
                      <div className="bg-muted p-3 rounded-lg">
                        <p className="text-sm">1 teaspoon (5ml) with dinner daily</p>
                      </div>
                    </div>

                    <Button className="w-full mt-6" variant="outline">
                      View Details
                      <ChevronRight className="h-4 w-4 ml-2" />
                    </Button>
                  </div>

                  <div className="bg-white rounded-xl border p-6">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="text-xl font-bold">Calm & Rest</h3>
                        <p className="text-muted-foreground mt-1">Supports relaxation and sleep</p>
                      </div>
                      <div className="w-16 h-16 bg-brand-light rounded-full flex items-center justify-center flex-shrink-0">
                        <img src="https://images.unsplash.com/photo-1589553416260-f586c8f1514f?auto=format&fit=crop&w=100&h=100" alt="Calm & Rest" className="w-10 h-10 object-cover rounded-full" />
                      </div>
                    </div>

                    <div className="mt-6">
                      <h4 className="text-sm font-medium">Benefits</h4>
                      <ul className="mt-2 space-y-2">
                        <li className="flex items-center text-sm">
                          <Check className="h-4 w-4 text-green-500 mr-2" />
                          <span>Promotes relaxation</span>
                        </li>
                        <li className="flex items-center text-sm">
                          <Check className="h-4 w-4 text-green-500 mr-2" />
                          <span>Supports quality sleep</span>
                        </li>
                        <li className="flex items-center text-sm">
                          <Check className="h-4 w-4 text-green-500 mr-2" />
                          <span>Reduces stress and anxiety</span>
                        </li>
                      </ul>
                    </div>

                    <div className="mt-6">
                      <h4 className="text-sm font-medium mb-2">Dosage</h4>
                      <div className="bg-muted p-3 rounded-lg">
                        <p className="text-sm">1 chew before bedtime as needed</p>
                      </div>
                    </div>

                    <Button className="w-full mt-6" variant="outline">
                      View Details
                      <ChevronRight className="h-4 w-4 ml-2" />
                    </Button>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
          <CardFooter className="border-t pt-4 flex flex-col sm:flex-row gap-4 justify-between">
            <Button variant="outline">View Full Protocol</Button>
            <Button className="bg-brand-primary hover:bg-brand-dark">
              Purchase Individual Supplements
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </CardFooter>
        </Card>

        {/* Delivery Status */}
        <Card>
          <CardHeader>
            <CardTitle>Subscription Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-6">
              <div className="flex flex-col">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium">Current Shipment</h3>
                  <Badge className="bg-green-500">In Transit</Badge>
                </div>
                <Card className="bg-muted/50">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-4 mb-4">
                      <div className="h-12 w-12 bg-brand-light rounded-full flex items-center justify-center">
                        <Package className="h-6 w-6 text-brand-primary" />
                      </div>
                      <div>
                        <h4 className="font-medium">July 2025 Supply</h4>
                        <p className="text-xs text-muted-foreground">Tracking #: AB12345678</p>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Shipped:</span>
                        <span>July 26, 2025</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Estimated Delivery:</span>
                        <span>July 29, 2025</span>
                      </div>
                    </div>

                    <div className="mt-4">
                      <div className="flex justify-between text-xs mb-1">
                        <span>Shipping Progress</span>
                        <span>75%</span>
                      </div>
                      <Progress value={75} className="h-2" />
                      <div className="flex justify-between text-xs mt-2 text-muted-foreground">
                        <span>Shipped</span>
                        <span>Out for Delivery</span>
                        <span>Delivered</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="flex flex-col">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium">Next Shipment</h3>
                  <Badge variant="outline">Preparing</Badge>
                </div>
                <Card className="bg-muted/50">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-4 mb-4">
                      <div className="h-12 w-12 bg-brand-light rounded-full flex items-center justify-center">
                        <Clock className="h-6 w-6 text-brand-primary" />
                      </div>
                      <div>
                        <h4 className="font-medium">August 2025 Supply</h4>
                        <p className="text-xs text-muted-foreground">Subscription renewal</p>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Scheduled Processing:</span>
                        <span>August 24, 2025</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Estimated Shipping:</span>
                        <span>August 25, 2025</span>
                      </div>
                    </div>

                    <div className="mt-4 flex justify-center">
                      <Button variant="outline" size="sm" className="w-full">
                        Modify Next Shipment
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="flex flex-col">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium">Membership Status</h3>
                  <Badge variant="outline" className="bg-brand-primary text-white">Premium</Badge>
                </div>
                <Card className="bg-muted/50">
                  <CardContent className="p-4">
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Membership:</span>
                        <span>Premium Plan</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Monthly Cost:</span>
                        <span>$49.99/month</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Next Billing:</span>
                        <span>August 15, 2025</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Active Until:</span>
                        <span>May 15, 2026</span>
                      </div>
                    </div>

                    <div className="mt-4 space-y-2">
                      <Button className="w-full bg-brand-light text-brand-primary hover:bg-brand-light/80">
                        Manage Subscription
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Recommendations */}
        <div className="grid md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>AI Recommendations</CardTitle>
              <CardDescription>Based on Bella's latest health data</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-4 p-3 border rounded-lg">
                <div className="h-10 w-10 bg-yellow-100 rounded-full flex items-center justify-center flex-shrink-0">
                  <AlertTriangle className="h-5 w-5 text-yellow-500" />
                </div>
                <div>
                  <h4 className="font-medium">Consider Adding Joint Support</h4>
                  <p className="text-sm text-muted-foreground mt-1">
                    Bella's recent activity data shows she might benefit from early joint support to maintain her active lifestyle.
                  </p>
                  <Button variant="link" className="p-0 h-auto text-brand-primary mt-2">
                    View suggested supplements
                  </Button>
                </div>
              </div>
              
              <div className="flex gap-4 p-3 border rounded-lg">
                <div className="h-10 w-10 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                  <Check className="h-5 w-5 text-green-500" />
                </div>
                <div>
                  <h4 className="font-medium">Gut Restore Dosage is Optimal</h4>
                  <p className="text-sm text-muted-foreground mt-1">
                    Current dosage is showing excellent results based on latest microbiome test. No change needed.
                  </p>
                </div>
              </div>
              
              <div className="flex gap-4 p-3 border rounded-lg">
                <div className="h-10 w-10 bg-brand-light rounded-full flex items-center justify-center flex-shrink-0">
                  <ArrowRight className="h-5 w-5 text-brand-primary" />
                </div>
                <div>
                  <h4 className="font-medium">Increase Water Intake</h4>
                  <p className="text-sm text-muted-foreground mt-1">
                    To maximize supplement effectiveness, ensure Bella has access to fresh water throughout the day.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle>Protocol Adherence</CardTitle>
              <CardDescription>Tracking Bella's supplement schedule</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-5">
                <div>
                  <div className="flex justify-between mb-1">
                    <h4 className="text-sm font-medium">Morning Protocol</h4>
                    <span className="text-sm text-green-500 font-medium">90% Adherence</span>
                  </div>
                  <Progress value={90} className="h-2" />
                </div>
                
                <div>
                  <div className="flex justify-between mb-1">
                    <h4 className="text-sm font-medium">Mealtime Protocol</h4>
                    <span className="text-sm text-amber-500 font-medium">75% Adherence</span>
                  </div>
                  <Progress value={75} className="h-2" />
                </div>
                
                <div>
                  <div className="flex justify-between mb-1">
                    <h4 className="text-sm font-medium">Evening Protocol</h4>
                    <span className="text-sm text-green-500 font-medium">95% Adherence</span>
                  </div>
                  <Progress value={95} className="h-2" />
                </div>
              </div>
              
              <div className="mt-6 bg-muted p-4 rounded-lg">
                <h4 className="font-medium mb-2">Improvement Tips</h4>
                <ul className="space-y-2">
                  <li className="flex items-start gap-2 text-sm">
                    <Check className="h-4 w-4 text-green-500 mt-0.5" />
                    <span>Set a daily reminder for mealtime supplements</span>
                  </li>
                  <li className="flex items-start gap-2 text-sm">
                    <Check className="h-4 w-4 text-green-500 mt-0.5" />
                    <span>Keep supplements in a visible location near food preparation area</span>
                  </li>
                  <li className="flex items-start gap-2 text-sm">
                    <Check className="h-4 w-4 text-green-500 mt-0.5" />
                    <span>Use the app's tracking feature to mark each dose</span>
                  </li>
                </ul>
              </div>
            </CardContent>
            <CardFooter className="pt-0">
              <Button variant="link" className="text-brand-primary p-0">
                View detailed compliance report
                <ArrowRight className="ml-1 h-4 w-4" />
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    </Layout>;
};
export default Supplements;