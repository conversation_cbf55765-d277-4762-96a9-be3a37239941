
import React from "react";
import { Layout } from "@/components/Layout";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { FileTextIcon, LayersIcon } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { ProgressOverviewCard } from "@/components/test-history/ProgressOverviewCard";
import { TestHistoryTable } from "@/components/test-history/TestHistoryTable";
import { LatestTestDetails } from "@/components/test-history/LatestTestDetails";
import { ChatModal } from "@/components/ai-coach/ChatModal";
import { FloatingChatButton } from "@/components/ai-coach/FloatingChatButton";
import { PetDataProvider, usePetData } from "@/context/PetContext";
import { useAICoachModal } from "@/hooks/useAICoachModal";
import { useIsMobile } from "@/hooks/use-mobile";

const TestHistoryContent = () => {
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const { petData } = usePetData();
  const {
    isOpen: isAICoachOpen,
    setIsOpen: setIsAICoachOpen,
    messages,
    isTyping,
    handleSendMessage
  } = useAICoachModal();

  // Sample test history data
  const testHistory = [
    {
      id: "TH-5783",
      date: "July 25, 2025",
      status: "Completed",
      type: "Microbiome & GI",
      score: 82,
      change: "+5",
      changeType: "positive",
    },
    {
      id: "TH-5432",
      date: "April 18, 2025",
      status: "Completed",
      type: "Microbiome & GI",
      score: 77,
      change: "+12",
      changeType: "positive",
    },
    {
      id: "TH-5021",
      date: "January 10, 2025",
      status: "Completed",
      type: "Microbiome & GI",
      score: 65,
      change: "-3",
      changeType: "negative",
    },
    {
      id: "TH-4820",
      date: "October 5, 2024",
      status: "Completed",
      type: "Microbiome & GI",
      score: 68,
      change: "+8",
      changeType: "positive",
    },
    {
      id: "TH-4562",
      date: "July 15, 2024",
      status: "Completed",
      type: "Baseline Assessment",
      score: 60,
      change: "--",
      changeType: "neutral",
    },
  ];

  // Sample chart data for microbiome health over time
  const microbiomeData = [
    { date: "Jul 2024", score: 60, gi: 55, inflammation: 62 },
    { date: "Oct 2024", score: 68, gi: 63, inflammation: 70 },
    { date: "Jan 2025", score: 65, gi: 67, inflammation: 72 },
    { date: "Apr 2025", score: 77, gi: 75, inflammation: 80 },
    { date: "Jul 2025", score: 82, gi: 76, inflammation: 85 },
  ];

  const viewTestReport = (testId: string) => {
    navigate(`/tests/report/${testId}`);
  };

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{petData.name}'s Test History</h1>
            <p className="text-muted-foreground">Track progress through diagnostic testing</p>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline" size="sm" className="gap-2">
              <LayersIcon className="h-4 w-4" />
              Filter Tests
            </Button>
            <Button size="sm" className="gap-2 bg-brand-primary hover:bg-brand-dark">
              <FileTextIcon className="h-4 w-4" />
              Schedule New Test
            </Button>
          </div>
        </div>

        {/* Progress Overview Card */}
        <ProgressOverviewCard chartData={microbiomeData} />

        {/* Test History Table */}
        <Card>
          <CardHeader>
            <CardTitle>Test Records</CardTitle>
            <CardDescription>Complete history of {petData.name}'s diagnostic tests</CardDescription>
          </CardHeader>
          <CardContent>
            <TestHistoryTable tests={testHistory} onViewReport={viewTestReport} />
          </CardContent>
        </Card>

        {/* Latest Test Details */}
        <LatestTestDetails onViewReport={viewTestReport} testId={testHistory[0].id} />

        {/* Floating Chat Button - Only on mobile */}
        {isMobile && (
          <FloatingChatButton onClick={() => setIsAICoachOpen(true)} />
        )}

        {/* AI Coach Modal */}
        <ChatModal
          open={isAICoachOpen}
          onOpenChange={setIsAICoachOpen}
          messages={messages}
          isTyping={isTyping}
          onSendMessage={handleSendMessage}
        />
      </div>
    </Layout>
  );
};

const TestHistory = () => {
  return (
    <PetDataProvider>
      <TestHistoryContent />
    </PetDataProvider>
  );
};

export default TestHistory;
