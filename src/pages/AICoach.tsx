
import React, { useState, useEffect } from "react";
import { Layout } from "@/components/Layout";
import { CoachHeader } from "@/components/ai-coach/CoachHeader";
import { ChatArea } from "@/components/ai-coach/ChatArea";
import { ChatModal } from "@/components/ai-coach/ChatModal";
import { Message } from "@/types/chat";
import { useIsMobile } from "@/hooks/use-mobile";
import { PetDataProvider, usePetData } from "@/context/PetContext";
import { chatApi } from "@/services/chatApi";
import { useNavigate } from "react-router-dom";

const AICoachContent = () => {
  const isMobile = useIsMobile();
  const navigate = useNavigate();
  const { petData } = usePetData();
  const [currentThreadId, setCurrentThreadId] = useState<string | undefined>();
  const [messages, setMessages] = useState<Message[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const [isChatOpen, setIsChatOpen] = useState(isMobile); // Auto-open on mobile

  // Redirect mobile users to dashboard with modal open
  useEffect(() => {
    if (isMobile) {
      navigate("/dashboard", { replace: true });
    }
  }, [isMobile, navigate]);

  // Initialize welcome message when component mounts
  useEffect(() => {
    if (messages.length === 0) {
      setMessages([
        {
          id: 1,
          sender: "ai",
          text: `Hey, I'm ${petData.name}'s AI coach! I'm here to help you with any questions about ${petData.name}'s health, nutrition, behavior, and wellness. What would you like to know today?`,
          timestamp: new Date(),
        },
      ]);
    }
  }, [petData.name, messages.length]);

  // Function to handle sending a message
  const handleSendMessage = async (text: string) => {
    if (text.trim() === "") return;

    // Add user message
    const userMessage: Message = {
      id: messages.length + 1,
      sender: "user",
      text: text,
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setIsTyping(true);

    try {
      // Send message to the edge function
      const response = await chatApi.sendMessage(text, currentThreadId);
      
      if (response.success && response.message) {
        // Update thread ID if new thread was created
        if (response.threadId && !currentThreadId) {
          setCurrentThreadId(response.threadId);
        }

        const aiMessage: Message = {
          id: messages.length + 2,
          sender: "ai",
          text: response.message,
          timestamp: new Date(),
          threadId: response.threadId,
          citations: response.citations,
          sources: response.sources,
        };

        setMessages((prev) => [...prev, aiMessage]);
      } else {
        // Handle error
        const errorMessage: Message = {
          id: messages.length + 2,
          sender: "ai",
          text: response.error || "I'm sorry, I encountered an error. Please try again.",
          timestamp: new Date(),
          error: true,
        };

        setMessages((prev) => [...prev, errorMessage]);
      }
    } catch (error) {
      console.error('Error sending message:', error);
      const errorMessage: Message = {
        id: messages.length + 2,
        sender: "ai",
        text: "I'm sorry, I'm having trouble connecting right now. Please try again.",
        timestamp: new Date(),
        error: true,
      };

      setMessages((prev) => [...prev, errorMessage]);
    } finally {
      setIsTyping(false);
    }
  };

  // Mobile users will be redirected, but this modal will be used by other components
  if (isMobile) {
    return (
      <ChatModal
        open={isChatOpen}
        onOpenChange={setIsChatOpen}
        messages={messages}
        isTyping={isTyping}
        onSendMessage={handleSendMessage}
      />
    );
  }

  // Desktop layout with Layout wrapper
  return (
    <Layout>
      <div className="container py-4 md:py-6 max-w-4xl mx-auto">
        <div className="flex flex-col gap-4 md:gap-6">
          <CoachHeader />

          <ChatArea 
            messages={messages}
            isTyping={isTyping}
            suggestions={[]}
            onSendMessage={handleSendMessage}
            onSelectSuggestion={() => {}}
          />
        </div>
      </div>
    </Layout>
  );
};

const AICoach = () => {
  return (
    <PetDataProvider>
      <AICoachContent />
    </PetDataProvider>
  );
};

export default AICoach;
