
import React, { useState } from "react";
import MembershipToggle from "@/features/onboarding/components/MembershipToggle";
import CompactMembershipCard from "./membership-demo/CompactMembershipCard";
import FeatureComparison from "./membership-demo/FeatureComparison";
import { useIsMobile } from "@/hooks/use-mobile";
import {
  SENSE_FEATURES,
  BASIC_FEATURES,
  completeAnnualPrice,
  completeMonthlyPrice,
  completeSavingsPercentage,
  essentialAnnualPrice,
  essentialMonthlyPrice,
  essentialSavingsPercentage
} from "./membership-demo/constants";

type MembershipType = "annual" | "monthly";

const MembershipDemo: React.FC = () => {
  const [selectedMembership, setSelectedMembership] = useState<MembershipType>("annual");
  const isMobile = useIsMobile();

  const handleMembershipChange = (value: MembershipType) => {
    setSelectedMembership(value);
  };

  return (
    <div className="min-h-screen bg-brand-light py-6 px-4">
      <div className="container max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-6">
          <h1 className="text-3xl font-serif mb-3">Membership Plans Comparison</h1>
          <p className="text-lg text-muted-foreground mb-4">
            Compare our Complete and Essential membership options
          </p>
          
          {/* Toggle for Annual/Monthly */}
          <MembershipToggle 
            selectedMembership={selectedMembership} 
            onMembershipChange={handleMembershipChange} 
          />
        </div>

        {/* Complete Plans Section */}
        <div className="mb-8">
          <div className="text-center mb-4">
            <h2 className="text-xl font-serif text-brand-primary mb-1">Animal Biome Complete</h2>
            <p className="text-sm text-muted-foreground">Expert care. Personalized recommendations. Real-time guidance.</p>
          </div>
          
          <div className={`grid ${isMobile ? "grid-cols-1 gap-4" : "grid-cols-2 gap-6"} max-w-5xl mx-auto`}>
            {/* Annual Complete Plan */}
            <div className={selectedMembership === "annual" ? "ring-2 ring-brand-primary rounded-xl" : ""}>
              <CompactMembershipCard
                selectedMembership="annual"
                annualPrice={completeAnnualPrice}
                monthlyPrice={completeMonthlyPrice}
                savingsPercentage={completeSavingsPercentage}
                features={SENSE_FEATURES}
                isPremium={true}
              />
            </div>
            
            {/* Monthly Complete Plan */}
            <div className={selectedMembership === "monthly" ? "ring-2 ring-brand-primary rounded-xl" : ""}>
              <CompactMembershipCard
                selectedMembership="monthly"
                annualPrice={completeAnnualPrice}
                monthlyPrice={completeMonthlyPrice}
                savingsPercentage={completeSavingsPercentage}
                features={SENSE_FEATURES}
                isPremium={true}
              />
            </div>
          </div>
        </div>

        {/* Essential Plans Section */}
        <div className="mb-6">
          <div className="text-center mb-4">
            <h2 className="text-xl font-serif text-brand-primary mb-1">Animal Biome Essential</h2>
            <p className="text-sm text-muted-foreground">Essential health monitoring without advanced AI features</p>
          </div>
          
          <div className={`grid ${isMobile ? "grid-cols-1 gap-4" : "grid-cols-2 gap-6"} max-w-5xl mx-auto`}>
            {/* Annual Essential Plan */}
            <div className={selectedMembership === "annual" ? "ring-2 ring-gray-400 rounded-xl" : ""}>
              <CompactMembershipCard
                selectedMembership="annual"
                annualPrice={essentialAnnualPrice}
                monthlyPrice={essentialMonthlyPrice}
                savingsPercentage={essentialSavingsPercentage}
                features={BASIC_FEATURES}
                isPremium={false}
              />
            </div>
            
            {/* Monthly Essential Plan */}
            <div className={selectedMembership === "monthly" ? "ring-2 ring-gray-400 rounded-xl" : ""}>
              <CompactMembershipCard
                selectedMembership="monthly"
                annualPrice={essentialAnnualPrice}
                monthlyPrice={essentialMonthlyPrice}
                savingsPercentage={essentialSavingsPercentage}
                features={BASIC_FEATURES}
                isPremium={false}
              />
            </div>
          </div>
        </div>

        {/* Feature Comparison */}
        <FeatureComparison />
      </div>
    </div>
  );
};

export default MembershipDemo;
