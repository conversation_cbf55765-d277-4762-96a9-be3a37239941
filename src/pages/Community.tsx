
import React, { useState } from "react";
import { Layout } from "@/components/Layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Heart, MessageCircle, Share2, TrendingUp, Users, Award, Plus } from "lucide-react";
import { ChatModal } from "@/components/ai-coach/ChatModal";
import { FloatingChatButton } from "@/components/ai-coach/FloatingChatButton";
import { PetDataProvider, usePetData } from "@/context/PetContext";
import { useAICoachModal } from "@/hooks/useAICoachModal";
import { useIsMobile } from "@/hooks/use-mobile";

const CommunityContent = () => {
  const { petData } = usePetData();
  const isMobile = useIsMobile();
  const [likedPosts, setLikedPosts] = useState<string[]>([]);
  const {
    isOpen: isAICoachOpen,
    setIsOpen: setIsAICoachOpen,
    messages,
    isTyping,
    handleSendMessage
  } = useAICoachModal();

  const communityPosts = [
    {
      id: "1",
      author: "Sarah M.",
      petName: "Max",
      petBreed: "Golden Retriever",
      timeAgo: "2 hours ago",
      content: "Max hit a new SHAPE streak today! 🎉 7 days of consistent tracking. The AI coach suggestions for his sleep routine have been game-changing.",
      likes: 24,
      comments: 8,
      category: "Success Story",
      hasImage: false
    },
    {
      id: "2", 
      author: "Mike R.",
      petName: "Bella",
      petBreed: "Labrador Mix",
      timeAgo: "4 hours ago",
      content: "Question for the community: Has anyone else noticed improved stool quality after switching to the recommended probiotic? Bella's been on it for 3 weeks now.",
      likes: 12,
      comments: 15,
      category: "Question",
      hasImage: false
    },
    {
      id: "3",
      author: "Jennifer L.",
      petName: "Charlie",
      petBreed: "Border Collie", 
      timeAgo: "6 hours ago",
      content: "Charlie's energy levels have been amazing since we started the new exercise plan! Here's his latest activity tracking data. Thank you to this amazing community for all the support! 🐕",
      likes: 18,
      comments: 6,
      category: "Update",
      hasImage: true
    },
    {
      id: "4",
      author: "David K.",
      petName: "Ruby",
      petBreed: "Australian Shepherd",
      timeAgo: "8 hours ago", 
      content: "Ruby just completed her 30-day wellness journey! Her health score improved from 65 to 89. The personalized supplement plan made all the difference.",
      likes: 31,
      comments: 12,
      category: "Milestone",
      hasImage: false
    },
    {
      id: "5",
      author: "You",
      petName: petData.name,
      petBreed: petData.breed || "Mixed Breed",
      timeAgo: "1 day ago",
      content: `${petData.name} had an awesome day today! Completed all SHAPE categories and the poop score was excellent. This tracking really helps me stay on top of their health.`,
      likes: 8,
      comments: 4,
      category: "Daily Update",
      hasImage: false
    }
  ];

  const toggleLike = (postId: string) => {
    setLikedPosts(prev => 
      prev.includes(postId) 
        ? prev.filter(id => id !== postId)
        : [...prev, postId]
    );
  };

  const getCategoryColor = (category: string) => {
    const colors = {
      "Success Story": "bg-green-100 text-green-700",
      "Question": "bg-blue-100 text-blue-700", 
      "Update": "bg-purple-100 text-purple-700",
      "Milestone": "bg-orange-100 text-orange-700",
      "Daily Update": "bg-gray-100 text-gray-700"
    };
    return colors[category as keyof typeof colors] || "bg-gray-100 text-gray-700";
  };

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Pet Parent Community</h1>
            <p className="text-muted-foreground">Connect with other pet parents on their wellness journey</p>
          </div>
          <Button className="gap-2 bg-brand-primary hover:bg-brand-dark">
            <Plus className="h-4 w-4" />
            Share Update
          </Button>
        </div>

        {/* Community Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Users className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Active Members</p>
                  <p className="text-xl font-bold">2,847</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-100 rounded-lg">
                  <TrendingUp className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Success Stories</p>
                  <p className="text-xl font-bold">1,234</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Award className="h-5 w-5 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">This Week</p>
                  <p className="text-xl font-bold">89 Posts</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Community Feed */}
        <Tabs defaultValue="feed" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="feed">Community Feed</TabsTrigger>
            <TabsTrigger value="questions">Questions</TabsTrigger>
            <TabsTrigger value="success">Success Stories</TabsTrigger>
          </TabsList>

          <TabsContent value="feed" className="space-y-4">
            {communityPosts.map((post) => (
              <Card key={post.id}>
                <CardContent className="p-6">
                  <div className="flex gap-4">
                    <Avatar>
                      <AvatarFallback>{post.author.charAt(0)}</AvatarFallback>
                    </Avatar>
                    
                    <div className="flex-1 space-y-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div>
                            <h4 className="font-medium">{post.author}</h4>
                            <p className="text-sm text-muted-foreground">{post.petName} • {post.petBreed}</p>
                          </div>
                          <Badge variant="secondary" className={getCategoryColor(post.category)}>
                            {post.category}
                          </Badge>
                        </div>
                        <span className="text-sm text-muted-foreground">{post.timeAgo}</span>
                      </div>
                      
                      <p className="text-gray-700">{post.content}</p>
                      
                      {post.hasImage && (
                        <div className="w-full h-48 bg-gray-100 rounded-lg flex items-center justify-center">
                          <span className="text-gray-500">Activity Chart Image</span>
                        </div>
                      )}
                      
                      <div className="flex items-center gap-6 pt-2">
                        <button 
                          onClick={() => toggleLike(post.id)}
                          className="flex items-center gap-2 text-sm text-gray-600 hover:text-red-500 transition-colors"
                        >
                          <Heart className={`h-4 w-4 ${likedPosts.includes(post.id) ? 'fill-red-500 text-red-500' : ''}`} />
                          {post.likes + (likedPosts.includes(post.id) ? 1 : 0)}
                        </button>
                        
                        <button className="flex items-center gap-2 text-sm text-gray-600 hover:text-blue-500 transition-colors">
                          <MessageCircle className="h-4 w-4" />
                          {post.comments}
                        </button>
                        
                        <button className="flex items-center gap-2 text-sm text-gray-600 hover:text-green-500 transition-colors">
                          <Share2 className="h-4 w-4" />
                          Share
                        </button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </TabsContent>

          <TabsContent value="questions" className="space-y-4">
            <Card>
              <CardContent className="p-6 text-center">
                <h3 className="text-lg font-semibold mb-2">Questions & Answers</h3>
                <p className="text-muted-foreground">Get advice from experienced pet parents and wellness experts.</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="success" className="space-y-4">
            <Card>
              <CardContent className="p-6 text-center">
                <h3 className="text-lg font-semibold mb-2">Success Stories</h3>
                <p className="text-muted-foreground">Celebrate amazing transformations and wellness achievements.</p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Floating Chat Button - Only on mobile */}
        {isMobile && (
          <FloatingChatButton onClick={() => setIsAICoachOpen(true)} />
        )}

        {/* AI Coach Modal */}
        <ChatModal
          open={isAICoachOpen}
          onOpenChange={setIsAICoachOpen}
          messages={messages}
          isTyping={isTyping}
          onSendMessage={handleSendMessage}
        />
      </div>
    </Layout>
  );
};

const Community = () => {
  return (
    <PetDataProvider>
      <CommunityContent />
    </PetDataProvider>
  );
};

export default Community;
