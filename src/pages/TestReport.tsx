
import React from "react";
import { useParams } from "react-router-dom";
import { Layout } from "@/components/Layout";
import { TestReportHero } from "@/components/microbiome/TestReportHero";
import { HealthStatusCards } from "@/components/microbiome/HealthStatusCards";
import { EnhancedRecommendations } from "@/components/microbiome/EnhancedRecommendations";
import { TestReportHeader } from "@/components/microbiome/TestReportHeader";
import { DetailedAnalysisTabs } from "@/components/microbiome/DetailedAnalysisTabs";
import { TestReportFooter } from "@/components/microbiome/TestReportFooter";

const TestReport = () => {
  const { testId } = useParams();
  
  // Mock test data - this would come from an API in a real implementation
  const testData = {
    id: testId || "TH-5783",
    date: "July 25, 2025",
    status: "Completed",
    type: "Microbiome & GI",
    score: 82,
    previousScore: 77,
    change: "+5",
    changeType: "positive" as const,
    pet: {
      name: "<PERSON>",
      age: 4,
      breed: "Labrador Retriever",
      gender: "Female",
    }
  };

  return (
    <Layout>
      <div className="space-y-8">
        {/* Header */}
        <TestReportHeader testData={testData} />

        {/* Hero Section */}
        <TestReportHero testData={testData} />

        {/* Health Status Overview */}
        <HealthStatusCards />

        {/* Detailed Analysis Tabs */}
        <DetailedAnalysisTabs />

        {/* Enhanced Recommendations */}
        <EnhancedRecommendations />

        {/* Footer */}
        <TestReportFooter />
      </div>
    </Layout>
  );
};

export default TestReport;
