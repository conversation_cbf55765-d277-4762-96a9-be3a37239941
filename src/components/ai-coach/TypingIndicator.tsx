
import React from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { usePetData } from "@/context/PetContext";
import { useIsMobile } from "@/hooks/use-mobile";

export const TypingIndicator = () => {
  const { petData } = usePetData();
  const isMobile = useIsMobile();
  
  return (
    <div className="flex justify-start animate-fade-in">
      <div className="flex items-start gap-3">
        <Avatar className={`${isMobile ? 'h-6 w-6 mt-1' : 'h-8 w-8'} border border-border shrink-0`}>
          <AvatarImage alt={`${petData.name}'s AI Coach`} src="/lovable-uploads/70dc7a3b-9b4d-4309-a5cf-1c2f5b1b512c.png" />
          <AvatarFallback className="text-xs">{petData.name.charAt(0)}</AvatarFallback>
        </Avatar>
        <div className={`bg-muted/60 backdrop-blur-sm rounded-2xl rounded-tl-md ${
          isMobile ? 'px-3 py-2' : 'px-4 py-3'
        } max-w-[80%] border border-border/50`}>
          <div className="flex items-center gap-1">
            <div className="flex gap-1">
              <span className="w-2 h-2 rounded-full bg-muted-foreground/60 animate-bounce" style={{animationDelay: "0ms", animationDuration: "1.4s"}}></span>
              <span className="w-2 h-2 rounded-full bg-muted-foreground/60 animate-bounce" style={{animationDelay: "160ms", animationDuration: "1.4s"}}></span>
              <span className="w-2 h-2 rounded-full bg-muted-foreground/60 animate-bounce" style={{animationDelay: "320ms", animationDuration: "1.4s"}}></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
