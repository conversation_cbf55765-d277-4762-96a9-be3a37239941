
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { useIsMobile } from "@/hooks/use-mobile";
import { ChevronDown, ChevronUp } from "lucide-react";
import { cn } from "@/lib/utils";

interface SuggestedQuestionsProps {
  suggestions: string[];
  onSelectSuggestion: (suggestion: string) => void;
  className?: string;
}

export const SuggestedQuestions: React.FC<SuggestedQuestionsProps> = ({
  suggestions,
  onSelectSuggestion,
  className
}) => {
  const isMobile = useIsMobile();
  const [isExpanded, setIsExpanded] = React.useState(!isMobile);
  
  return (
    <div className={cn("border-t bg-background/80 backdrop-blur-sm", className)}>
      <button 
        onClick={() => setIsExpanded(!isExpanded)}
        className={`flex items-center justify-center w-full ${isMobile ? 'py-2' : 'py-2'} text-sm text-muted-foreground hover:bg-muted/50 transition-colors touch-manipulation`}
      >
        <span className="font-medium text-xs">Suggested Questions</span>
        {isExpanded ? (
          <ChevronUp className="ml-2 h-3 w-3" />
        ) : (
          <ChevronDown className="ml-2 h-3 w-3" />
        )}
      </button>
      
      {isExpanded && (
        <div className={`${isMobile ? 'p-3 space-y-2' : 'p-3 space-y-2'} bg-muted/10`}>
          {suggestions.map((suggestion, i) => (
            <Button
              key={i}
              variant="outline"
              size="sm"
              className={`${isMobile ? 'text-xs h-auto py-2 px-3' : 'text-xs'} w-full justify-start text-left hover:bg-brand-primary/10 hover:border-brand-primary/20 transition-colors touch-manipulation`}
              onClick={() => {
                onSelectSuggestion(suggestion);
                if (isMobile) setIsExpanded(false);
              }}
            >
              <span className="line-clamp-2">{suggestion}</span>
            </Button>
          ))}
        </div>
      )}
    </div>
  );
};
