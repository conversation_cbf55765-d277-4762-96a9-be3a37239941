
import React from "react";
import { Citation } from "@/types/chat";

interface SuperscriptCitationProps {
  citation: Citation;
  onClick: (citation: Citation) => void;
}

export const SuperscriptCitation: React.FC<SuperscriptCitationProps> = ({ citation, onClick }) => {
  return (
    <sup 
      className="cursor-pointer text-brand-primary hover:text-brand-primary/80 font-medium text-xs ml-0.5 transition-colors"
      onClick={() => onClick(citation)}
      title={`View source ${citation.position}`}
    >
      {citation.position}
    </sup>
  );
};
