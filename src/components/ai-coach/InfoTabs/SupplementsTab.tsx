
import React from "react";
import { Button } from "@/components/ui/button";

export const SupplementsTab: React.FC = () => {
  return (
    <div className="p-4">
      <h3 className="text-sm font-medium mb-3">Current Supplements</h3>
      <div className="space-y-2">
        <div className="text-xs border rounded-md p-3">
          <div className="font-medium">Gut Restore</div>
          <div className="text-muted-foreground mt-1">Probiotic blend to increase beneficial bacteria</div>
        </div>
        <div className="text-xs border rounded-md p-3">
          <div className="font-medium">Digestive Enzymes</div>
          <div className="text-muted-foreground mt-1">Supports nutrient absorption and digestion</div>
        </div>
        <div className="text-xs border rounded-md p-3">
          <div className="font-medium">Omega Support</div>
          <div className="text-muted-foreground mt-1">Anti-inflammatory and coat health support</div>
        </div>
      </div>
      <Button variant="link" className="p-0 h-auto text-xs mt-3" size="sm">
        Manage supplements
      </Button>
    </div>
  );
};
