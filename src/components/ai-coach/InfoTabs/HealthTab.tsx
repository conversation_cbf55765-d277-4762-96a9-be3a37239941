
import React from "react";
import { Button } from "@/components/ui/button";
import { HEALTH_CATEGORIES } from "@/features/onboarding/types";
import { Badge } from "@/components/ui/badge";

export const HealthTab: React.FC = () => {
  return (
    <div className="p-4">
      <h3 className="text-sm font-medium mb-3">Latest Test Results</h3>
      <div className="space-y-3">
        <div>
          <div className="flex justify-between text-xs mb-1">
            <span>Microbiome Health</span>
            <span className="font-medium">82/100</span>
          </div>
          <div className="h-1.5 bg-gray-100 rounded-full overflow-hidden">
            <div className="h-full rounded-full bg-green-500" style={{ width: "82%" }}></div>
          </div>
        </div>
        <div>
          <div className="flex justify-between text-xs mb-1">
            <span>GI Function</span>
            <span className="font-medium">75/100</span>
          </div>
          <div className="h-1.5 bg-gray-100 rounded-full overflow-hidden">
            <div className="h-full rounded-full bg-green-500" style={{ width: "75%" }}></div>
          </div>
        </div>
        <div>
          <div className="flex justify-between text-xs mb-1">
            <span>Inflammation</span>
            <span className="font-medium">88/100</span>
          </div>
          <div className="h-1.5 bg-gray-100 rounded-full overflow-hidden">
            <div className="h-full rounded-full bg-green-500" style={{ width: "88%" }}></div>
          </div>
        </div>
      </div>
      
      <h3 className="text-sm font-medium mb-2 mt-6">Health Issues</h3>
      <div className="space-y-3 max-h-[250px] overflow-y-auto pr-1">
        {HEALTH_CATEGORIES.map((category) => {
          const issueCount = 1; // This would be dynamic based on actual pet data
          if (issueCount > 0) {
            return (
              <div key={category.category} className="border rounded-md p-3">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-xs font-medium">{category.category}</span>
                  <Badge variant="outline" className="text-xs h-5">
                    {issueCount} issue{issueCount !== 1 ? 's' : ''}
                  </Badge>
                </div>
                <div className="flex flex-wrap gap-1">
                  {/* Example symptoms - would be populated from actual pet data */}
                  {category.category === "Digestive" && (
                    <Badge className="bg-muted text-muted-foreground">Loose stool</Badge>
                  )}
                  {category.category === "Skin & Coat" && (
                    <Badge className="bg-muted text-muted-foreground">Itchy skin</Badge>
                  )}
                  {category.category === "Behavior" && (
                    <Badge className="bg-muted text-muted-foreground">Anxiety</Badge>
                  )}
                </div>
              </div>
            );
          }
          return null;
        }).filter(Boolean)}
      </div>
      
      <Button variant="link" className="p-0 h-auto text-xs mt-4" size="sm">
        View full health report
      </Button>
    </div>
  );
};
