
import React from "react";
import { Button } from "@/components/ui/button";

export const HistoryTab: React.FC = () => {
  return (
    <div className="p-4">
      <h3 className="text-sm font-medium mb-3">Health History</h3>
      <div className="space-y-3 relative pl-5 border-l border-gray-200">
        <div className="relative">
          <div className="absolute -left-[21px] mt-1 w-3 h-3 rounded-full bg-brand-primary"></div>
          <div>
            <div className="font-medium text-xs">Diagnosed with food sensitivity</div>
            <div className="text-xs text-muted-foreground">6 months ago</div>
          </div>
        </div>
        <div className="relative">
          <div className="absolute -left-[21px] mt-1 w-3 h-3 rounded-full bg-brand-primary"></div>
          <div>
            <div className="font-medium text-xs">Started supplement protocol</div>
            <div className="text-xs text-muted-foreground">5 months ago</div>
          </div>
        </div>
        <div className="relative">
          <div className="absolute -left-[21px] mt-1 w-3 h-3 rounded-full bg-brand-primary"></div>
          <div>
            <div className="font-medium text-xs">First microbiome test</div>
            <div className="text-xs text-muted-foreground">5 months ago</div>
          </div>
        </div>
        <div className="relative">
          <div className="absolute -left-[21px] mt-1 w-3 h-3 rounded-full bg-brand-primary"></div>
          <div>
            <div className="font-medium text-xs">Latest microbiome test</div>
            <div className="text-xs text-muted-foreground">2 days ago</div>
          </div>
        </div>
      </div>
      <Button variant="link" className="p-0 h-auto text-xs mt-3" size="sm">
        View full history
      </Button>
    </div>
  );
};
