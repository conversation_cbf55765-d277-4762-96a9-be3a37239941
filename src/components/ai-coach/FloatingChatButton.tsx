
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface FloatingChatButtonProps {
  onClick: () => void;
  className?: string;
}

export const FloatingChatButton: React.FC<FloatingChatButtonProps> = ({
  onClick,
  className
}) => {
  return (
    <Button
      onClick={onClick}
      size="lg"
      className={cn(
        "fixed z-40 h-16 px-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-200",
        "bg-brand-primary hover:bg-brand-primary/90 text-white",
        "hover:scale-105 active:scale-95 flex items-center gap-3",
        className
      )}
      style={{
        bottom: 'max(2rem, calc(env(safe-area-inset-bottom) + 1rem))',
        right: '1.5rem'
      }}
    >
      <img 
        src="/lovable-uploads/2a4a4886-f7a3-4ea1-bb04-91843c2b09d8.png" 
        alt="AnimalBiome" 
        className="w-8 h-8 object-contain flex-shrink-0"
      />
      <span className="text-sm font-medium whitespace-nowrap">AI Health Coach</span>
    </Button>
  );
};
