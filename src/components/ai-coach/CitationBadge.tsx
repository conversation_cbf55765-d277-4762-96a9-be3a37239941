
import React from "react";
import { Badge } from "@/components/ui/badge";
import { Citation } from "@/types/chat";

interface CitationBadgeProps {
  citation: Citation;
  onClick: (citation: Citation) => void;
}

export const CitationBadge: React.FC<CitationBadgeProps> = ({ citation, onClick }) => {
  return (
    <Badge 
      variant="outline" 
      className="ml-1 cursor-pointer hover:bg-brand-primary/10 transition-colors text-xs"
      onClick={() => onClick(citation)}
    >
      [{citation.position}]
    </Badge>
  );
};
