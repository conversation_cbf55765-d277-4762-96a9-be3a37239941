
import React from "react";
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { BarChart3, Pill, History, ArrowLeft } from "lucide-react";
import { HealthTab } from "./InfoTabs/HealthTab"; 
import { SupplementsTab } from "./InfoTabs/SupplementsTab";
import { HistoryTab } from "./InfoTabs/HistoryTab";
import { Button } from "@/components/ui/button";
import { useIsMobile } from "@/hooks/use-mobile";
import { cn } from "@/lib/utils";

interface InfoSidebarProps {
  className?: string;
  onClose?: () => void;
}

export const InfoSidebar: React.FC<InfoSidebarProps> = ({ 
  className,
  onClose 
}) => {
  const isMobile = useIsMobile();
  
  return (
    <Card className={cn(
      "overflow-hidden h-[calc(100vh-240px)] md:h-[calc(100vh-220px)] flex flex-col",
      className
    )}>
      <Tabs defaultValue="health" className="flex flex-col h-full">
        <div className="border-b flex items-center">
          {isMobile && (
            <Button 
              variant="ghost" 
              size="icon" 
              className="ml-2" 
              onClick={onClose}
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
          )}
          <TabsList className="w-full justify-start rounded-none bg-transparent h-auto p-0">
            <TabsTrigger 
              value="health" 
              className="data-[state=active]:bg-transparent data-[state=active]:shadow-none data-[state=active]:border-b-2 data-[state=active]:border-brand-primary rounded-none flex-1"
            >
              <BarChart3 className="h-4 w-4 mr-2" />
              {!isMobile && "Test Results"}
            </TabsTrigger>
            <TabsTrigger 
              value="supplements" 
              className="data-[state=active]:bg-transparent data-[state=active]:shadow-none data-[state=active]:border-b-2 data-[state=active]:border-brand-primary rounded-none flex-1"
            >
              <Pill className="h-4 w-4 mr-2" />
              {!isMobile && "Supplements"}
            </TabsTrigger>
            <TabsTrigger 
              value="history" 
              className="data-[state=active]:bg-transparent data-[state=active]:shadow-none data-[state=active]:border-b-2 data-[state=active]:border-brand-primary rounded-none flex-1"
            >
              <History className="h-4 w-4 mr-2" />
              {!isMobile && "History"}
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="health" className="flex-1 overflow-y-auto m-0 p-0">
          <HealthTab />
        </TabsContent>

        <TabsContent value="supplements" className="flex-1 overflow-y-auto m-0 p-0">
          <SupplementsTab />
        </TabsContent>

        <TabsContent value="history" className="flex-1 overflow-y-auto m-0 p-0">
          <HistoryTab />
        </TabsContent>
      </Tabs>
    </Card>
  );
};
