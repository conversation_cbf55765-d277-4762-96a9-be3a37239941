
import React from "react";
import { Citation, Source } from "@/types/chat";
import { ExternalLink, FileText, Globe, BookOpen } from "lucide-react";
import { Card } from "@/components/ui/card";

interface CitationFootnotesProps {
  citations: Citation[];
  sources: Source[];
  onSourceClick: (source: Source) => void;
}

export const CitationFootnotes: React.FC<CitationFootnotesProps> = ({ 
  citations, 
  sources, 
  onSourceClick 
}) => {
  const getSourceIcon = (type: Source['type']) => {
    switch (type) {
      case 'web': return <Globe className="h-3 w-3" />;
      case 'document': return <FileText className="h-3 w-3" />;
      case 'research': return <BookOpen className="h-3 w-3" />;
      default: return <FileText className="h-3 w-3" />;
    }
  };

  if (!citations.length) return null;

  return (
    <Card className="mt-4 p-3 bg-muted/30 border-muted">
      <div className="text-xs font-medium text-muted-foreground mb-2">Sources:</div>
      <div className="space-y-2">
        {citations.map((citation) => {
          const source = sources.find(s => s.id === citation.sourceId);
          if (!source) return null;
          
          return (
            <div 
              key={citation.id} 
              className="flex items-start gap-2 text-xs cursor-pointer hover:bg-muted/50 p-1 rounded transition-colors"
              onClick={() => onSourceClick(source)}
            >
              <span className="font-medium text-brand-primary min-w-[20px]">
                [{citation.position}]
              </span>
              <div className="flex items-center gap-1 flex-1">
                {getSourceIcon(source.type)}
                <span className="font-medium">{source.title}</span>
                {source.url && <ExternalLink className="h-3 w-3 text-muted-foreground" />}
              </div>
            </div>
          );
        })}
      </div>
    </Card>
  );
};
