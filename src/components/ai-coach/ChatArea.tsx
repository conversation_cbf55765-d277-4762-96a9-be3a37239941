
import React from "react";
import { Message } from "@/types/chat";
import { useIsMobile } from "@/hooks/use-mobile";
import { ChatModalLayout } from "./ChatModalLayout";
import { ChatMobileLayout } from "./ChatMobileLayout";
import { ChatDesktopLayout } from "./ChatDesktopLayout";

interface ChatAreaProps {
  messages: Message[];
  isTyping: boolean;
  suggestions: string[];
  onSendMessage: (message: string) => void;
  onSelectSuggestion: (suggestion: string) => void;
  isModal?: boolean;
}

export const ChatArea: React.FC<ChatAreaProps> = ({
  messages,
  isTyping,
  suggestions,
  onSendMessage,
  onSelectSuggestion,
  isModal = false
}) => {
  const isMobile = useIsMobile();

  // Modal layout (mobile full-screen)
  if (isMobile && isModal) {
    return (
      <ChatModalLayout 
        messages={messages}
        isTyping={isTyping}
        onSendMessage={onSendMessage}
      />
    );
  }

  // Mobile layout without modal
  if (isMobile && !isModal) {
    return (
      <ChatMobileLayout 
        messages={messages}
        isTyping={isTyping}
        onSendMessage={onSendMessage}
      />
    );
  }

  // Desktop layout with Card wrapper
  return (
    <ChatDesktopLayout 
      messages={messages}
      isTyping={isTyping}
      onSendMessage={onSendMessage}
    />
  );
};
