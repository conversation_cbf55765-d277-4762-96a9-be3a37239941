
import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Copy, Check } from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";

interface MessageFooterProps {
  timestamp: Date;
  messageText: string;
}

export const MessageFooter: React.FC<MessageFooterProps> = ({
  timestamp,
  messageText
}) => {
  const isMobile = useIsMobile();
  const [copiedText, setCopiedText] = useState<string | null>(null);

  const handleCopy = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedText(text);
      setTimeout(() => setCopiedText(null), 2000);
    } catch (err) {
      console.error('Failed to copy text:', err);
    }
  };

  return (
    <div className={`flex items-center justify-between ${isMobile ? 'mt-1.5' : 'mt-2'} pt-1 border-t border-muted/20`}>
      <div className="text-xs text-muted-foreground/80">
        {timestamp.toLocaleTimeString([], {
          hour: "2-digit",
          minute: "2-digit"
        })}
      </div>
      
      <Button 
        variant="ghost" 
        size="sm" 
        onClick={() => handleCopy(messageText)} 
        className={`${isMobile ? 'h-6 px-1.5' : 'h-6 px-2'} text-xs hover:bg-muted-foreground/10 -mr-1`}
      >
        {copiedText === messageText ? (
          <>
            <Check className="h-3 w-3 mr-1" />
            {!isMobile && "Copied"}
          </>
        ) : (
          <>
            <Copy className="h-3 w-3 mr-1" />
            {!isMobile && "Copy"}
          </>
        )}
      </Button>
    </div>
  );
};
