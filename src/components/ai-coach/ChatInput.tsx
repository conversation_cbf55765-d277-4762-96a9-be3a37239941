
import React, { useState, useRef } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { SendHorizontal } from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";
import { usePetData } from "@/context/PetContext";

interface ChatInputProps {
  onSendMessage: (message: string) => void;
  onInputFocus?: () => void;
}

export const ChatInput: React.FC<ChatInputProps> = ({ onSendMessage, onInputFocus }) => {
  const [input, setInput] = useState("");
  const [isMultiline, setIsMultiline] = useState(false);
  const [hasUserInteracted, setHasUserInteracted] = useState(false);
  const isMobile = useIsMobile();
  const { petData } = usePetData();
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (input.trim()) {
      onSendMessage(input);
      setInput("");
      setIsMultiline(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const handleInputChange = (value: string) => {
    setInput(value);
    
    // Auto-expand to textarea on mobile for longer messages
    if (isMobile && value.length > 60 && !isMultiline) {
      setIsMultiline(true);
    } else if (value.length <= 60 && isMultiline) {
      setIsMultiline(false);
    }
  };

  const handleFocus = () => {
    if (!hasUserInteracted) {
      setHasUserInteracted(true);
    }
    onInputFocus?.();
  };

  const placeholderText = hasUserInteracted ? 
    `Ask me about ${petData.name}'s health...` : 
    `Tap here to ask about ${petData.name}...`;

  const InputComponent = isMultiline ? (
    <Textarea
      ref={textareaRef}
      placeholder={placeholderText}
      value={input}
      onChange={(e) => handleInputChange(e.target.value)}
      onKeyPress={handleKeyPress}
      onFocus={handleFocus}
      className={`flex-1 resize-none ${
        isMobile 
          ? 'min-h-[40px] max-h-28 text-base chat-input-mobile' 
          : 'min-h-[44px] max-h-24 text-sm'
      } rounded-2xl border-0 bg-muted/50 backdrop-blur-sm placeholder:text-muted-foreground/70 focus:bg-background focus:ring-2 focus:ring-brand-primary/20 focus:border-brand-primary/30 transition-all duration-200 shadow-sm`}
      autoComplete="off"
      autoCorrect="off"
      autoCapitalize="off"
      rows={isMobile ? 2 : 1}
      style={{ fontSize: '16px' }}
    />
  ) : (
    <Input
      ref={inputRef}
      type="text"
      placeholder={placeholderText}
      value={input}
      onChange={(e) => handleInputChange(e.target.value)}
      onKeyPress={handleKeyPress}
      onFocus={handleFocus}
      className={`flex-1 ${
        isMobile ? 'h-10 text-base chat-input-mobile' : 'h-11 text-sm'
      } rounded-2xl border-0 bg-muted/50 backdrop-blur-sm placeholder:text-muted-foreground/70 focus:bg-background focus:ring-2 focus:ring-brand-primary/20 focus:border-brand-primary/30 transition-all duration-200 shadow-sm`}
      autoComplete="off"
      autoCorrect="off"
      autoCapitalize="off"
      inputMode="text"
      style={{ fontSize: '16px' }}
    />
  );

  return (
    <div className={`${
      isMobile 
        ? 'p-3 border-t bg-background/95 backdrop-blur-sm' 
        : 'p-4 border-t bg-background/95 backdrop-blur-sm'
    } shrink-0`}>
      {!hasUserInteracted && isMobile && (
        <div className="text-center text-xs text-muted-foreground mb-2 animate-fade-in">
          👋 Welcome! Tap the input below to start chatting
        </div>
      )}
      
      <form onSubmit={handleSubmit} className="flex gap-2 items-end max-w-full">
        <div className="flex-1 relative">
          {InputComponent}
        </div>
        
        <Button 
          type="submit" 
          disabled={!input.trim()}
          size="icon"
          className={`${
            isMobile ? 'h-10 w-10' : 'h-11 w-11'
          } rounded-full shrink-0 transition-all duration-200 shadow-md ${
            input.trim() 
              ? 'bg-brand-primary hover:bg-brand-primary/90 text-white hover:shadow-lg hover:scale-105' 
              : 'bg-muted/50 text-muted-foreground hover:bg-muted cursor-not-allowed'
          }`}
        >
          <SendHorizontal className={`${isMobile ? 'h-4 w-4' : 'h-4 w-4'}`} />
        </Button>
      </form>
    </div>
  );
};
