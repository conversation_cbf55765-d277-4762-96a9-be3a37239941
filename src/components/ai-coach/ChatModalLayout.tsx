
import React, { useRef, useEffect, useState, useLayoutEffect } from "react";
import { UserMessage } from "./UserMessage";
import { AIMessage } from "./AIMessage";
import { ChatInput } from "./ChatInput";
import { TypingIndicator } from "./TypingIndicator";
import { Message } from "@/types/chat";
import { useKeyboardDetection } from "@/hooks/useKeyboardDetection";
import { useIsMobile } from "@/hooks/use-mobile";

interface ChatModalLayoutProps {
  messages: Message[];
  isTyping: boolean;
  onSendMessage: (message: string) => void;
}

export const ChatModalLayout: React.FC<ChatModalLayoutProps> = ({
  messages,
  isTyping,
  onSendMessage,
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [userHasScrolled, setUserHasScrolled] = useState(false);
  const { isKeyboardOpen } = useKeyboardDetection();
  const isMobile = useIsMobile();

  // Handle initial positioning - scroll to top for welcome message
  useLayoutEffect(() => {
    if (scrollAreaRef.current && isInitialLoad && messages.length <= 1) {
      // For initial welcome message, scroll to top
      scrollAreaRef.current.scrollTop = 0;
      setIsInitialLoad(false);
    }
  }, [isInitialLoad, messages.length]);

  // Only auto-scroll to bottom when user is actively chatting (more than 1 message)
  useEffect(() => {
    if (!isInitialLoad && (messages.length > 1 || isTyping)) {
      messagesEndRef.current?.scrollIntoView({
        behavior: "smooth"
      });
    }
  }, [messages, isTyping, isInitialLoad]);

  // Handle scroll detection
  useEffect(() => {
    const scrollArea = scrollAreaRef.current;
    if (!scrollArea) return;

    const handleScroll = () => {
      if (!userHasScrolled && scrollArea.scrollTop > 0) {
        setUserHasScrolled(true);
      }
    };

    scrollArea.addEventListener('scroll', handleScroll);
    return () => scrollArea.removeEventListener('scroll', handleScroll);
  }, [userHasScrolled]);

  const handleInputFocus = () => {
    // When keyboard opens and user hasn't scrolled, maintain welcome message visibility
    if (isMobile && !userHasScrolled && messages.length <= 1) {
      setTimeout(() => {
        if (scrollAreaRef.current) {
          scrollAreaRef.current.scrollTop = 0;
        }
      }, 300); // Small delay to account for keyboard animation
    }
  };

  return (
    <div 
      className="flex flex-col h-full min-h-0 bg-background"
      style={{
        // Use regular vh when keyboard is open to prevent layout jumping
        height: isMobile && isKeyboardOpen ? '100vh' : '100dvh'
      }}
    >
      {/* Messages area with keyboard-aware styling */}
      <div 
        ref={scrollAreaRef}
        className="flex-1 min-h-0 overflow-y-auto overscroll-contain bg-gradient-to-b from-background to-muted/20"
        style={{
          paddingTop: isKeyboardOpen ? '0.5rem' : 'max(1rem, env(safe-area-inset-top) + 0.5rem)',
          paddingBottom: '1rem',
          WebkitOverflowScrolling: 'touch',
          overscrollBehavior: 'contain'
        }}
      >
        <div className="px-4 py-4 space-y-4 pb-6">
          {messages.map(message => 
            message.sender === "user" ? (
              <UserMessage key={message.id} message={message} />
            ) : (
              <AIMessage key={message.id} message={message} />
            )
          )}

          {isTyping && <TypingIndicator />}

          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Input area with keyboard-aware safe area padding */}
      <div 
        className="shrink-0 border-t bg-background"
        style={{
          paddingBottom: isKeyboardOpen ? '0.5rem' : 'max(1rem, env(safe-area-inset-bottom))'
        }}
      >
        <ChatInput onSendMessage={onSendMessage} onInputFocus={handleInputFocus} />
      </div>
    </div>
  );
};
