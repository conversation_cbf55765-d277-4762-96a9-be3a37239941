
import React, { useRef, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { UserMessage } from "./UserMessage";
import { AIMessage } from "./AIMessage";
import { ChatInput } from "./ChatInput";
import { TypingIndicator } from "./TypingIndicator";
import { Message } from "@/types/chat";
import { usePetData } from "@/context/PetContext";

interface ChatDesktopLayoutProps {
  messages: Message[];
  isTyping: boolean;
  onSendMessage: (message: string) => void;
}

export const ChatDesktopLayout: React.FC<ChatDesktopLayoutProps> = ({
  messages,
  isTyping,
  onSendMessage,
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { petData } = usePetData();

  // Scroll to bottom when messages change or when typing status changes
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({
      behavior: "smooth"
    });
  }, [messages, isTyping]);

  return (
    <Card className="flex flex-col h-[calc(100vh-240px)] md:h-[calc(100vh-220px)] overflow-hidden relative shadow-sm border-muted">
      {/* Desktop header */}
      <div className="flex items-center justify-center p-4 border-b bg-background/80 backdrop-blur-sm shrink-0">
        <div className="flex items-center gap-3">
          <Avatar className="h-8 w-8 border border-border">
            <AvatarImage alt={`${petData.name}'s AI Coach`} src="/lovable-uploads/70dc7a3b-9b4d-4309-a5cf-1c2f5b1b512c.png" />
            <AvatarFallback>{petData.name.charAt(0)}</AvatarFallback>
          </Avatar>
          <div>
            <h2 className="font-semibold text-sm">{petData.name}'s AI Coach</h2>
            <p className="text-xs text-muted-foreground">Online</p>
          </div>
        </div>
      </div>

      {/* Messages area */}
      <div className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-muted scrollbar-track-transparent bg-gradient-to-b from-background to-muted/10">
        <div className="p-4 md:p-6 space-y-6">
          {messages.map(message => 
            message.sender === "user" ? (
              <UserMessage key={message.id} message={message} />
            ) : (
              <AIMessage key={message.id} message={message} />
            )
          )}

          {isTyping && <TypingIndicator />}

          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Input area */}
      <ChatInput onSendMessage={onSendMessage} />
    </Card>
  );
};
