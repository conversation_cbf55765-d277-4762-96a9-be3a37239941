
import React from "react";
import { AlertTriangle } from "lucide-react";

interface MessageHeaderProps {
  hasError: boolean;
}

export const MessageHeader: React.FC<MessageHeaderProps> = ({ hasError }) => {
  if (!hasError) return null;

  return (
    <div className="flex items-center gap-2 mb-2">
      <AlertTriangle className="h-4 w-4 text-red-500" />
      <span className="text-xs font-medium text-red-600">Error</span>
    </div>
  );
};
