
import React from "react";
import { Message } from "@/types/chat";
import { useIsMobile } from "@/hooks/use-mobile";

interface UserMessageProps {
  message: Message;
}

export const UserMessage: React.FC<UserMessageProps> = ({ message }) => {
  const isMobile = useIsMobile();
  
  return (
    <div className="flex justify-end animate-fade-in">
      <div className={`max-w-[85%] rounded-2xl rounded-br-md ${
        isMobile ? 'px-4 py-3' : 'px-4 py-3'
      } bg-brand-primary text-white shadow-md backdrop-blur-sm`}>
        <p className={`${isMobile ? 'text-sm' : 'text-sm'} leading-relaxed`}>
          {message.text}
        </p>
        <div className={`${
          isMobile ? 'text-xs mt-2' : 'text-xs mt-2'
        } text-brand-light/80 text-right`}>
          {message.timestamp.toLocaleTimeString([], {
            hour: "2-digit",
            minute: "2-digit",
          })}
        </div>
      </div>
    </div>
  );
};
