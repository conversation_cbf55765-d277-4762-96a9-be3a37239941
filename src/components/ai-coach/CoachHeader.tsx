
import React from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { usePetData } from "@/context/PetContext";

export const CoachHeader: React.FC = () => {
  const { petData } = usePetData();
  
  return (
    <div className="flex items-center justify-between flex-wrap gap-3">
      <div className="flex items-center gap-3">
        <Avatar className="h-10 w-10 md:h-12 md:w-12 border-2 border-brand-primary">
          <AvatarImage src="https://images.unsplash.com/photo-1535268647677-300dbf3d78d1" alt={`${petData.name}'s AI Coach`} />
          <AvatarFallback>{petData.name.charAt(0)}</AvatarFallback>
        </Avatar>
        <div>
          <h1 className="text-xl md:text-2xl font-semibold">{petData.name}'s AI Coach</h1>
          <div className="flex items-center gap-2 flex-wrap">
            <Badge variant="outline" className="text-xs font-normal rounded-full">
              <span className="w-2 h-2 rounded-full bg-green-500 mr-1"></span>
              Active
            </Badge>
            <span className="text-xs md:text-sm text-muted-foreground">Trained on {petData.name}'s health data</span>
          </div>
        </div>
      </div>
    </div>
  );
};
