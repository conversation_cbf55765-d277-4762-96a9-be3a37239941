
import React, { useRef, useEffect, useState, useLayoutEffect } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { UserMessage } from "./UserMessage";
import { AIMessage } from "./AIMessage";
import { ChatInput } from "./ChatInput";
import { TypingIndicator } from "./TypingIndicator";
import { Message } from "@/types/chat";
import { usePetData } from "@/context/PetContext";
import { useKeyboardDetection } from "@/hooks/useKeyboardDetection";

interface ChatMobileLayoutProps {
  messages: Message[];
  isTyping: boolean;
  onSendMessage: (message: string) => void;
}

export const ChatMobileLayout: React.FC<ChatMobileLayoutProps> = ({
  messages,
  isTyping,
  onSendMessage,
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { petData } = usePetData();
  const [headerVisible, setHeaderVisible] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [userHasScrolled, setUserHasScrolled] = useState(false);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const { isKeyboardOpen } = useKeyboardDetection();

  // Handle initial positioning - scroll to top for welcome message
  useLayoutEffect(() => {
    if (scrollAreaRef.current && isInitialLoad && messages.length <= 1) {
      // For initial welcome message, scroll to top
      scrollAreaRef.current.scrollTop = 0;
      setIsInitialLoad(false);
    }
  }, [isInitialLoad, messages.length]);

  // Only auto-scroll to bottom when user is actively chatting (more than 1 message)
  useEffect(() => {
    if (!isInitialLoad && (messages.length > 1 || isTyping)) {
      messagesEndRef.current?.scrollIntoView({
        behavior: "smooth"
      });
    }
  }, [messages, isTyping, isInitialLoad]);

  // Handle header auto-hide on mobile and scroll detection
  useEffect(() => {
    const handleScroll = () => {
      const scrollArea = scrollAreaRef.current;
      if (!scrollArea) return;

      const currentScrollY = scrollArea.scrollTop;
      
      // Track if user has scrolled
      if (!userHasScrolled && currentScrollY > 0) {
        setUserHasScrolled(true);
      }
      
      if (currentScrollY > lastScrollY && currentScrollY > 50) {
        // Scrolling down - hide header
        setHeaderVisible(false);
      } else if (currentScrollY < lastScrollY) {
        // Scrolling up - show header
        setHeaderVisible(true);
      }
      
      setLastScrollY(currentScrollY);
    };

    const scrollArea = scrollAreaRef.current;
    if (scrollArea) {
      scrollArea.addEventListener('scroll', handleScroll);
      return () => scrollArea.removeEventListener('scroll', handleScroll);
    }
  }, [lastScrollY, userHasScrolled]);

  const handleInputFocus = () => {
    // When keyboard opens and user hasn't scrolled, maintain welcome message visibility
    if (!userHasScrolled && messages.length <= 1) {
      setTimeout(() => {
        if (scrollAreaRef.current) {
          scrollAreaRef.current.scrollTop = 0;
        }
      }, 300); // Small delay to account for keyboard animation
    }
  };

  return (
    <div 
      className="flex flex-col h-full overflow-hidden bg-background"
      style={{
        // Use regular vh when keyboard is open to prevent layout jumping
        height: isKeyboardOpen ? '100vh' : '100dvh'
      }}
    >
      {/* Mobile conversation header with auto-hide and keyboard awareness */}
      <div className={`flex items-center justify-center px-3 border-b bg-background/95 backdrop-blur-md shrink-0 transition-transform duration-300 ${
        headerVisible && !isKeyboardOpen ? 'translate-y-0 py-2' : '-translate-y-full py-1'
      }`}>
        <div className="flex items-center gap-2">
          <Avatar className="h-6 w-6 border border-border">
            <AvatarImage alt={`${petData.name}'s AI Coach`} src="/lovable-uploads/70dc7a3b-9b4d-4309-a5cf-1c2f5b1b512c.png" />
            <AvatarFallback className="text-xs">{petData.name.charAt(0)}</AvatarFallback>
          </Avatar>
          <div>
            <h2 className="font-semibold text-sm">{petData.name}'s AI Coach</h2>
          </div>
        </div>
      </div>

      {/* Messages area with keyboard awareness */}
      <div 
        ref={scrollAreaRef}
        className="flex-1 overflow-y-auto overscroll-contain bg-gradient-to-b from-background to-muted/20"
        style={{
          WebkitOverflowScrolling: 'touch',
          overscrollBehavior: 'contain',
          paddingTop: isKeyboardOpen ? '0.5rem' : '1rem'
        }}
      >
        <div className="px-3 py-3 space-y-3">
          {messages.map(message => 
            message.sender === "user" ? (
              <UserMessage key={message.id} message={message} />
            ) : (
              <AIMessage key={message.id} message={message} />
            )
          )}

          {isTyping && <TypingIndicator />}

          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Input area with keyboard-aware safe area bottom padding */}
      <div 
        className="shrink-0"
        style={{
          paddingBottom: isKeyboardOpen ? '0.5rem' : 'max(1rem, env(safe-area-inset-bottom))'
        }}
      >
        <ChatInput onSendMessage={onSendMessage} onInputFocus={handleInputFocus} />
      </div>
    </div>
  );
};
