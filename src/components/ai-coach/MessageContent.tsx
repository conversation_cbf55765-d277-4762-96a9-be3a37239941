
import React from "react";
import { Message } from "@/types/chat";
import { SuperscriptCitation } from "./SuperscriptCitation";
import { Citation, Source } from "@/types/chat";

// Dynamic import for react-markdown with better error handling
let ReactMarkdown: any = null;
let remarkGfm: any = null;
try {
  ReactMarkdown = require('react-markdown').default || require('react-markdown');
  remarkGfm = require('remark-gfm').default || require('remark-gfm');
} catch (error) {
  console.warn('React Markdown not available, using fallback text rendering');
}

interface MessageContentProps {
  message: Message;
  onCitationClick: (citation: Citation) => void;
}

export const MessageContent: React.FC<MessageContentProps> = ({
  message,
  onCitationClick
}) => {
  // Enhanced citation processing
  const processTextWithCitations = (text: string) => {
    if (!message.citations?.length) return text;
    let processedText = text;
    const citationMap = new Map<number, Citation>();
    message.citations.forEach(citation => {
      citationMap.set(citation.position, citation);
    });
    processedText = processedText.replace(/\[(\d+)\]/g, (match, positionStr) => {
      const position = parseInt(positionStr);
      const citation = citationMap.get(position);
      if (citation) {
        return `<citation data-id="${citation.id}" data-position="${citation.position}"></citation>`;
      }
      return match;
    });
    return processedText;
  };

  // Custom component for rendering citations within markdown
  const CitationComponent = ({
    'data-id': dataId,
    'data-position': dataPosition,
    ...props
  }: any) => {
    const citation = message.citations?.find(c => c.id === dataId);
    if (!citation) return null;
    return <SuperscriptCitation citation={citation} onClick={onCitationClick} />;
  };

  // Enhanced fallback text renderer with basic markdown support
  const renderFallbackText = (text: string) => {
    const processedText = processTextWithCitations(text);

    // Basic markdown parsing for fallback
    let htmlContent = processedText.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>').replace(/\*(.*?)\*/g, '<em>$1</em>').replace(/`(.*?)`/g, '<code class="bg-muted px-1 py-0.5 rounded text-xs font-mono">$1</code>').replace(/\n\n/g, '</p><p class="mb-1.5">').replace(/\n/g, '<br/>');

    // Handle lists
    htmlContent = htmlContent.replace(/^- (.*$)/gim, '<li class="text-sm text-foreground ml-4">• $1</li>');
    htmlContent = htmlContent.replace(/(<li.*<\/li>)/s, '<ul class="mb-1.5 space-y-0.5">$1</ul>');

    // Wrap in paragraph if no paragraphs exist
    if (!htmlContent.includes('<p>')) {
      htmlContent = `<p class="mb-1.5">${htmlContent}</p>`;
    }

    // Handle citations
    const parts = htmlContent.split(/(<citation[^>]*><\/citation>)/);
    return <div className="prose prose-sm max-w-none">
        {parts.map((part, index) => {
        if (part.includes('<citation')) {
          const match = part.match(/data-id="([^"]*)" data-position="([^"]*)"/);
          if (match) {
            const citation = message.citations?.find(c => c.id === match[1]);
            if (citation) {
              return <SuperscriptCitation key={index} citation={citation} onClick={onCitationClick} />;
            }
          }
          return null;
        }
        return <div key={index} dangerouslySetInnerHTML={{
          __html: part
        }} />;
      })}
      </div>;
  };

  // Main text renderer with improved markdown support - reduced spacing
  const renderText = (text: string) => {
    const processedText = processTextWithCitations(text);
    if (ReactMarkdown && remarkGfm) {
      return <ReactMarkdown remarkPlugins={[remarkGfm]} components={{
        h1: ({
          children
        }) => <h1 className="text-lg font-semibold mb-2 text-foreground">{children}</h1>,
        h2: ({
          children
        }) => <h2 className="text-base font-semibold mb-1.5 text-foreground">{children}</h2>,
        h3: ({
          children
        }) => <h3 className="text-sm font-semibold mb-1.5 text-foreground">{children}</h3>,
        p: ({
          children
        }) => <p className="mb-2 last:mb-0 text-sm leading-relaxed text-foreground">{children}</p>,
        ul: ({
          children
        }) => <ul className="list-disc list-inside mb-2 space-y-0.5 pl-2">{children}</ul>,
        ol: ({
          children
        }) => <ol className="list-decimal list-inside mb-2 space-y-0.5 pl-2">{children}</ol>,
        li: ({
          children
        }) => <li className="text-sm text-foreground leading-relaxed">{children}</li>,
        blockquote: ({
          children
        }) => <blockquote className="border-l-4 border-brand-primary pl-4 italic my-2 text-muted-foreground bg-muted/30 py-2 rounded-r">
                {children}
              </blockquote>,
        code: ({
          children,
          className
        }) => {
          const isInline = !className;
          return isInline ? <code className="bg-muted px-1.5 py-0.5 rounded text-xs font-mono text-brand-primary">{children}</code> : <pre className="bg-muted p-4 rounded-lg overflow-x-auto my-2 text-xs">
                  <code className="font-mono">{children}</code>
                </pre>;
        },
        strong: ({
          children
        }) => <strong className="font-semibold text-foreground">{children}</strong>,
        em: ({
          children
        }) => <em className="italic text-foreground">{children}</em>,
        citation: CitationComponent
      }}>
          {processedText}
        </ReactMarkdown>;
    } else {
      // Use enhanced fallback renderer
      return renderFallbackText(text);
    }
  };

  return (
    <div className={`${message.error ? 'text-red-700' : ''}`}>
      {renderText(message.text)}
    </div>
  );
};
