
import React from "react";
import { Sheet, SheetContent } from "@/components/ui/sheet";
import { Chat<PERSON>rea } from "./ChatArea";
import { Message } from "@/types/chat";
import { Button } from "@/components/ui/button";
import { X } from "lucide-react";
import { useNavigate } from "react-router-dom";

interface ChatModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  messages: Message[];
  isTyping: boolean;
  onSendMessage: (message: string) => void;
}

export const ChatModal: React.FC<ChatModalProps> = ({
  open,
  onOpenChange,
  messages,
  isTyping,
  onSendMessage,
}) => {
  const navigate = useNavigate();

  const handleClose = () => {
    onOpenChange(false);
    // Navigate back to dashboard or previous page
    navigate("/dashboard");
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent 
        side="bottom" 
        className="h-full max-h-[100dvh] p-0 border-0 rounded-none bg-background flex flex-col"
        style={{
          height: '100dvh',
          maxHeight: '100dvh'
        }}
        data-chat-modal
      >
        {/* Simple close button */}
        <Button
          variant="ghost"
          size="icon"
          onClick={handleClose}
          className="absolute top-4 right-4 z-50 h-8 w-8 rounded-full bg-background/80 backdrop-blur-sm border shadow-sm hover:bg-background"
          style={{
            top: 'max(1rem, env(safe-area-inset-top))'
          }}
        >
          <X className="h-4 w-4" />
        </Button>

        <div className="flex flex-col h-full min-h-0" data-chat-input>
          <ChatArea 
            messages={messages}
            isTyping={isTyping}
            suggestions={[]}
            onSendMessage={onSendMessage}
            onSelectSuggestion={() => {}}
            isModal={true}
          />
        </div>
      </SheetContent>
    </Sheet>
  );
};
