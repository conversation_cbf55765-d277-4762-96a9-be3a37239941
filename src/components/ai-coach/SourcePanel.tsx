
import React from "react";
import { Source } from "@/types/chat";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ExternalLink, X, FileText, Globe, BookOpen } from "lucide-react";

interface SourcePanelProps {
  source: Source | null;
  onClose: () => void;
}

export const SourcePanel: React.FC<SourcePanelProps> = ({ source, onClose }) => {
  if (!source) return null;

  const getSourceIcon = (type: Source['type']) => {
    switch (type) {
      case 'web': return <Globe className="h-4 w-4" />;
      case 'document': return <FileText className="h-4 w-4" />;
      case 'research': return <BookOpen className="h-4 w-4" />;
      default: return <FileText className="h-4 w-4" />;
    }
  };

  const getSourceTypeLabel = (type: Source['type']) => {
    switch (type) {
      case 'web': return 'Web Source';
      case 'document': return 'Document';
      case 'research': return 'Research Paper';
      default: return 'Knowledge Base';
    }
  };

  return (
    <Card className="absolute top-0 right-0 w-80 z-10 p-4 shadow-lg border bg-background">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          {getSourceIcon(source.type)}
          <span className="text-sm font-medium text-muted-foreground">
            {getSourceTypeLabel(source.type)}
          </span>
        </div>
        <Button variant="ghost" size="icon" onClick={onClose} className="h-6 w-6">
          <X className="h-4 w-4" />
        </Button>
      </div>
      
      <div className="space-y-3">
        <div>
          <h4 className="font-medium text-sm mb-1">{source.title}</h4>
          {source.description && (
            <p className="text-xs text-muted-foreground">{source.description}</p>
          )}
        </div>
        
        {source.relevance && (
          <div className="flex items-center gap-2">
            <span className="text-xs text-muted-foreground">Relevance:</span>
            <div className="flex-1 bg-muted rounded-full h-2">
              <div 
                className="bg-brand-primary h-2 rounded-full transition-all"
                style={{ width: `${source.relevance * 100}%` }}
              />
            </div>
            <span className="text-xs font-medium">{Math.round(source.relevance * 100)}%</span>
          </div>
        )}
        
        {source.url && (
          <Button 
            variant="outline" 
            size="sm" 
            className="w-full justify-center gap-2"
            onClick={() => window.open(source.url, '_blank')}
          >
            <ExternalLink className="h-3 w-3" />
            View Source
          </Button>
        )}
      </div>
    </Card>
  );
};
