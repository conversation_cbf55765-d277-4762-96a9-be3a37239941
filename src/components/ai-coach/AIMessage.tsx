
import React, { useState } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Message } from "@/types/chat";
import { usePetData } from "@/context/PetContext";
import { CitationFootnotes } from "./CitationFootnotes";
import { SourcePanel } from "./SourcePanel";
import { Citation, Source } from "@/types/chat";
import { useIsMobile } from "@/hooks/use-mobile";
import { MessageContent } from "./MessageContent";
import { MessageHeader } from "./MessageHeader";

interface AIMessageProps {
  message: Message;
}

export const AIMessage: React.FC<AIMessageProps> = ({
  message
}) => {
  const { petData } = usePetData();
  const isMobile = useIsMobile();
  const [selectedSource, setSelectedSource] = useState<Source | null>(null);
  
  const handleCitationClick = (citation: Citation) => {
    const source = message.sources?.find(s => s.id === citation.sourceId);
    if (source) {
      setSelectedSource(source);
    }
  };
  
  const handleSourceClick = (source: Source) => {
    setSelectedSource(source);
  };
  
  return (
    <div className={`flex gap-3 relative ${isMobile ? 'gap-3' : 'gap-3'} animate-fade-in`}>
      <Avatar className={`${isMobile ? 'h-7 w-7 mt-1' : 'h-8 w-8'} border border-border shrink-0`}>
        <AvatarImage alt={`${petData.name}'s AI Coach`} src="/lovable-uploads/70dc7a3b-9b4d-4309-a5cf-1c2f5b1b512c.png" />
        <AvatarFallback className="text-xs">{petData.name.charAt(0)}</AvatarFallback>
      </Avatar>
      
      <div className={`max-w-[85%] rounded-2xl rounded-tl-md ${
        isMobile ? 'px-4 py-3' : 'px-4 py-3'
      } ${
        message.error 
          ? 'bg-red-50 border border-red-200' 
          : 'bg-muted/60 backdrop-blur-sm shadow-sm border border-border/50'
      }`}>
        <MessageHeader hasError={!!message.error} />
        
        <MessageContent message={message} onCitationClick={handleCitationClick} />

        {/* Citations */}
        {message.citations && message.citations.length > 0 && (
          <div className="mt-3 pt-2 border-t border-border/30">
            <CitationFootnotes 
              citations={message.citations} 
              sources={message.sources || []} 
              onSourceClick={handleSourceClick} 
            />
          </div>
        )}

        {/* Subtle timestamp */}
        <div className={`${
          isMobile ? 'text-xs mt-2' : 'text-xs mt-2'
        } text-muted-foreground/60`}>
          {message.timestamp.toLocaleTimeString([], {
            hour: "2-digit",
            minute: "2-digit",
          })}
        </div>
      </div>

      {/* Source Panel */}
      <SourcePanel 
        source={selectedSource} 
        onClose={() => setSelectedSource(null)} 
      />
    </div>
  );
};
