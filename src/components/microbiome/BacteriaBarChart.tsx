
import React from "react";
import { AspectRatio } from "@/components/ui/aspect-ratio";
import { InfoIcon } from "lucide-react";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import { useIsMobile } from "@/hooks/use-mobile";

export const BacteriaBarChart = () => {
  const isMobile = useIsMobile();
  
  // This is a simplified version - in a real app you would use recharts or a similar library
  // to create a proper stacked horizontal bar chart with the actual data
  return (
    <div className="w-full">
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center">
          <h4 className="text-sm font-medium">Bacterial Composition</h4>
          <Tooltip>
            <TooltipTrigger asChild>
              <span>
                <InfoIcon className="h-4 w-4 ml-1 text-muted-foreground cursor-help" />
              </span>
            </TooltipTrigger>
            <TooltipContent>
              <p className="max-w-xs text-xs">
                This chart shows the relative abundance of bacterial families in your pet's microbiome sample.
              </p>
            </TooltipContent>
          </Tooltip>
        </div>
      </div>
      
      <AspectRatio ratio={isMobile ? 16/4 : 16/3} className="bg-muted rounded-md overflow-hidden">
        <div className="flex h-full w-full">
          {/* This is a placeholder visualization - in a real app you would use actual data */}
          <div className="h-full bg-emerald-500" style={{ width: "28%" }}></div>
          <div className="h-full bg-green-400" style={{ width: "24%" }}></div>
          <div className="h-full bg-green-300" style={{ width: "20%" }}></div>
          <div className="h-full bg-blue-300" style={{ width: "12%" }}></div>
          <div className="h-full bg-blue-200" style={{ width: "10%" }}></div>
          <div className="h-full bg-yellow-300" style={{ width: "4%" }}></div>
          <div className="h-full bg-red-400" style={{ width: "2%" }}></div>
        </div>
      </AspectRatio>
      
      <div className="mt-2 grid grid-cols-2 md:flex md:flex-wrap gap-x-3 gap-y-2 text-xs">
        <div className="flex items-center">
          <div className="w-3 h-3 bg-emerald-500 rounded-sm mr-1"></div>
          <span>Firmicutes (28%)</span>
        </div>
        <div className="flex items-center">
          <div className="w-3 h-3 bg-green-400 rounded-sm mr-1"></div>
          <span>Bacteroidetes (24%)</span>
        </div>
        <div className="flex items-center">
          <div className="w-3 h-3 bg-green-300 rounded-sm mr-1"></div>
          <span>Lactobacillus (20%)</span>
        </div>
        <div className="flex items-center">
          <div className="w-3 h-3 bg-blue-300 rounded-sm mr-1"></div>
          <span>Bifidobacterium (12%)</span>
        </div>
        <div className="flex items-center">
          <div className="w-3 h-3 bg-blue-200 rounded-sm mr-1"></div>
          <span>Other (10%)</span>
        </div>
        <div className="flex items-center">
          <div className="w-3 h-3 bg-yellow-300 rounded-sm mr-1"></div>
          <span>Inflammatory (4%)</span>
        </div>
        <div className="flex items-center">
          <div className="w-3 h-3 bg-red-400 rounded-sm mr-1"></div>
          <span>Pathogenic (2%)</span>
        </div>
      </div>
    </div>
  );
};
