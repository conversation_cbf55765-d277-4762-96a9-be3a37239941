import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { CheckCircle, AlertTriangle, XCircle, TrendingUp, TrendingDown, ArrowRight, Heart, Shield, Zap, Activity } from "lucide-react";
interface HealthMetric {
  id: string;
  title: string;
  score: number;
  status: "excellent" | "good" | "fair" | "poor";
  trend: "up" | "down" | "stable";
  icon: React.ElementType;
  description: string;
  actionText: string;
}
export const HealthStatusCards: React.FC = () => {
  const healthMetrics: HealthMetric[] = [{
    id: "beneficial-bacteria",
    title: "Beneficial Bacteria",
    score: 85,
    status: "excellent",
    trend: "up",
    icon: Heart,
    description: "Your pet has excellent levels of beneficial bacteria that support digestive health.",
    actionText: "Maintain current diet"
  }, {
    id: "inflammation",
    title: "Inflammation Control",
    score: 78,
    status: "good",
    trend: "up",
    icon: Shield,
    description: "Inflammation markers are well controlled and showing improvement.",
    actionText: "Continue supplements"
  }, {
    id: "digestive-efficiency",
    title: "Digestive Efficiency",
    score: 65,
    status: "fair",
    trend: "stable",
    icon: Zap,
    description: "Digestive function is adequate but could benefit from dietary adjustments.",
    actionText: "Optimize nutrition"
  }, {
    id: "bacterial-diversity",
    title: "Bacterial Diversity",
    score: 45,
    status: "poor",
    trend: "down",
    icon: Activity,
    description: "Bacterial diversity is below optimal levels and needs attention.",
    actionText: "Add probiotics"
  }];
  const getStatusConfig = (status: string) => {
    switch (status) {
      case "excellent":
        return {
          bgColor: "bg-green-50",
          borderColor: "border-green-200",
          textColor: "text-green-700",
          badgeColor: "bg-green-100 text-green-800",
          icon: CheckCircle,
          iconColor: "text-green-500"
        };
      case "good":
        return {
          bgColor: "bg-blue-50",
          borderColor: "border-blue-200",
          textColor: "text-blue-700",
          badgeColor: "bg-blue-100 text-blue-800",
          icon: CheckCircle,
          iconColor: "text-blue-500"
        };
      case "fair":
        return {
          bgColor: "bg-yellow-50",
          borderColor: "border-yellow-200",
          textColor: "text-yellow-700",
          badgeColor: "bg-yellow-100 text-yellow-800",
          icon: AlertTriangle,
          iconColor: "text-yellow-500"
        };
      case "poor":
        return {
          bgColor: "bg-red-50",
          borderColor: "border-red-200",
          textColor: "text-red-700",
          badgeColor: "bg-red-100 text-red-800",
          icon: XCircle,
          iconColor: "text-red-500"
        };
      default:
        return {
          bgColor: "bg-gray-50",
          borderColor: "border-gray-200",
          textColor: "text-gray-700",
          badgeColor: "bg-gray-100 text-gray-800",
          icon: AlertTriangle,
          iconColor: "text-gray-500"
        };
    }
  };
  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case "up":
        return <TrendingUp className="h-4 w-4 text-green-500" />;
      case "down":
        return <TrendingDown className="h-4 w-4 text-red-500" />;
      default:
        return <div className="h-4 w-4" />;
    }
  };
  return <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Gut Microbiome Overview</h2>
        <Badge variant="outline" className="bg-brand-light text-brand-primary">
          4 Categories Analyzed
        </Badge>
      </div>
      
      <div className="grid gap-4">
        {healthMetrics.map(metric => {
        const config = getStatusConfig(metric.status);
        const StatusIcon = config.icon;
        const MetricIcon = metric.icon;
        return <Card key={metric.id} className={`${config.bgColor} ${config.borderColor} border-2 hover:shadow-md transition-shadow cursor-pointer`}>
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 bg-white rounded-lg flex items-center justify-center">
                      <MetricIcon className="h-6 w-6 text-brand-primary" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-lg">{metric.title}</h3>
                      <div className="flex items-center gap-2 mt-1">
                        <span className="text-2xl font-bold">{metric.score}</span>
                        <span className="text-sm text-muted-foreground">/100</span>
                        {getTrendIcon(metric.trend)}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className={config.badgeColor} variant="outline">
                      {metric.status.charAt(0).toUpperCase() + metric.status.slice(1)}
                    </Badge>
                    <StatusIcon className={`h-5 w-5 ${config.iconColor}`} />
                  </div>
                </div>
                
                <p className={`text-sm ${config.textColor} mb-4`}>
                  {metric.description}
                </p>
                
                <div className="flex justify-between items-center">
                  <Button variant="outline" size="sm" className="text-brand-primary border-brand-primary hover:bg-brand-primary hover:text-white">
                    {metric.actionText}
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                  <Button variant="link" size="sm" className="text-muted-foreground">
                    View Details
                  </Button>
                </div>
              </CardContent>
            </Card>;
      })}
      </div>
    </div>;
};