import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Calendar, Phone, Video, TrendingUp, AlertCircle } from "lucide-react";
interface TestReportHeroProps {
  testData: {
    id: string;
    date: string;
    score: number;
    previousScore: number;
    change: string;
    changeType: "positive" | "negative";
    pet: {
      name: string;
      age: number;
      breed: string;
      gender: string;
    };
  };
}
export const TestReportHero: React.FC<TestReportHeroProps> = ({
  testData
}) => {
  const getScoreStatus = (score: number) => {
    if (score >= 85) return {
      text: "Excellent",
      color: "bg-green-100 text-green-800"
    };
    if (score >= 70) return {
      text: "Good",
      color: "bg-blue-100 text-blue-800"
    };
    if (score >= 55) return {
      text: "Fair",
      color: "bg-yellow-100 text-yellow-800"
    };
    return {
      text: "Needs Attention",
      color: "bg-red-100 text-red-800"
    };
  };
  const scoreStatus = getScoreStatus(testData.score);
  return <div className="space-y-6">
      {/* Expert Consultation Banner */}
      <Card className="bg-gradient-to-r from-brand-primary to-brand-secondary text-white">
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row items-start md:items-center gap-4">
            
            <div className="flex-1">
              <h3 className="text-xl font-semibold mb-1">Expert Review Available</h3>
              <p className="text-white/90 mb-3">
                Get personalized insights from our veterinary microbiome specialists
              </p>
              <div className="flex flex-wrap gap-2">
                <Button variant="outline" size="sm" className="bg-white text-brand-primary border-white hover:bg-white/90">
                  <Video className="h-4 w-4 mr-2" />
                  Video Call
                </Button>
                <Button variant="outline" size="sm" className="bg-white text-brand-primary border-white hover:bg-white/90">
                  <Phone className="h-4 w-4 mr-2" />
                  Phone Call
                </Button>
                <Button variant="outline" size="sm" className="bg-white text-brand-primary border-white hover:bg-white/90">
                  <Calendar className="h-4 w-4 mr-2" />
                  Schedule
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Test Summary */}
      <Card>
        <CardContent className="p-6">
          <div className="grid md:grid-cols-3 gap-6">
            {/* Overall Score */}
            <div className="text-center">
              <div className="relative w-24 h-24 mx-auto mb-4">
                <div className="w-full h-full rounded-full bg-gray-100 flex items-center justify-center">
                  <div className="text-3xl font-bold text-brand-primary">{testData.score}</div>
                </div>
                <div className="absolute -top-2 -right-2">
                  {testData.changeType === "positive" ? <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                      <TrendingUp className="h-4 w-4 text-white" />
                    </div> : <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                      <AlertCircle className="h-4 w-4 text-white" />
                    </div>}
                </div>
              </div>
              <h3 className="font-semibold text-lg mb-1">Gut Microbiome Score</h3>
              <Badge className={scoreStatus.color} variant="outline">
                {scoreStatus.text}
              </Badge>
              <p className="text-sm text-muted-foreground mt-1">
                {testData.change} from last test
              </p>
            </div>

            {/* Pet Info */}
            

            {/* Test Info */}
            
          </div>
        </CardContent>
      </Card>
    </div>;
};