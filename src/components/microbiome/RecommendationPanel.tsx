
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";

export const RecommendationPanel = () => {
  const recommendations = [
    {
      id: "1",
      category: "Supplements",
      title: "Increase Probiotic Dosage",
      description: "Based on current bacterial levels, we recommend increasing the daily probiotic supplement by 25% to further boost beneficial bacterial populations.",
      priority: "high"
    },
    {
      id: "2", 
      category: "Nutrition",
      title: "Add Fermented Foods",
      description: "Introduce small amounts of pet-safe fermented foods like plain kefir (1 teaspoon daily) to increase diversity of beneficial bacteria.",
      priority: "medium"
    },
    {
      id: "3",
      category: "Exercise",
      title: "Increase Daily Exercise",
      description: "Add 10-15 minutes of additional physical activity daily to support gut motility and improve overall digestive function.",
      priority: "medium"
    },
    {
      id: "4",
      category: "Nutrition",
      title: "Fiber Supplementation",
      description: "Add 1-2 tablespoons of canned pumpkin daily to support beneficial bacteria growth and improve stool consistency.",
      priority: "high"
    }
  ];
  
  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case "high":
        return <Badge className="bg-red-100 text-red-700 border-red-200 whitespace-nowrap text-xs">High Priority</Badge>;
      case "medium":
        return <Badge className="bg-yellow-100 text-yellow-700 border-yellow-200 whitespace-nowrap text-xs">Medium Priority</Badge>;
      case "low":
        return <Badge className="bg-blue-100 text-blue-700 border-blue-200 whitespace-nowrap text-xs">Optional</Badge>;
      default:
        return <Badge variant="outline" className="whitespace-nowrap text-xs">Priority</Badge>;
    }
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Personalized Recommendations</CardTitle>
        <CardDescription>
          AI-generated suggestions based on your pet's specific microbiome profile
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {recommendations.map((rec) => (
            <div key={rec.id} className="border rounded-lg p-4">
              <div className="flex justify-between items-start mb-2 gap-2">
                <div className="flex-1 min-w-0">
                  <Badge variant="outline" className="mb-2">{rec.category}</Badge>
                  <h3 className="font-medium">{rec.title}</h3>
                </div>
                <div className="flex-shrink-0">
                  {getPriorityBadge(rec.priority)}
                </div>
              </div>
              <p className="text-sm text-muted-foreground mb-3">{rec.description}</p>
              <div className="flex justify-end">
                <Button variant="link" className="p-0 h-auto text-sm text-brand-primary">
                  Learn more
                  <ArrowRight className="ml-1 h-3 w-3" />
                </Button>
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-6 bg-brand-light rounded-lg p-4">
          <h3 className="text-brand-primary font-medium mb-2">Next Steps</h3>
          <p className="text-sm text-muted-foreground mb-4">
            Your pet's microbiome health is showing good improvement. Continue with the current protocol 
            with the adjustments recommended above. We suggest scheduling your next test in 3 months to
            track progress.
          </p>
          <Button className="bg-brand-primary hover:bg-brand-dark">
            Schedule Follow-up Test
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
