
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { InfoIcon, ArrowUpIcon, ArrowDownIcon, ArrowRightIcon } from "lucide-react";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";

interface TestMetricCardProps {
  title: string;
  score: number;
  previousScore?: number;
  description: string;
  insights: string;
  status: "good" | "moderate" | "poor";
}

export const TestMetricCard: React.FC<TestMetricCardProps> = ({
  title,
  score,
  previousScore,
  description,
  insights,
  status
}) => {
  const statusColors = {
    good: {
      bg: "bg-green-50",
      border: "border-green-200",
      text: "text-green-700",
      bar: "bg-green-500"
    },
    moderate: {
      bg: "bg-yellow-50",
      border: "border-yellow-200",
      text: "text-yellow-700",
      bar: "bg-yellow-500"
    },
    poor: {
      bg: "bg-red-50",
      border: "border-red-200",
      text: "text-red-700",
      bar: "bg-red-500"
    }
  };
  
  const colors = statusColors[status];
  const change = previousScore ? score - previousScore : 0;
  
  const renderChangeIndicator = () => {
    if (!previousScore) return null;
    
    if (change > 0) {
      return (
        <span className="inline-flex items-center text-green-500">
          <ArrowUpIcon className="h-4 w-4 mr-1" />
          +{change}
        </span>
      );
    } else if (change < 0) {
      return (
        <span className="inline-flex items-center text-red-500">
          <ArrowDownIcon className="h-4 w-4 mr-1" />
          {change}
        </span>
      );
    } else {
      return (
        <span className="inline-flex items-center text-gray-500">
          <ArrowRightIcon className="h-4 w-4 mr-1" />
          No change
        </span>
      );
    }
  };
  
  return (
    <Card className={`border ${colors.border}`}>
      <CardContent className="p-4">
        <div className="flex justify-between items-start mb-2">
          <div className="flex items-center">
            <h4 className="font-medium">{title}</h4>
            <Tooltip>
              <TooltipTrigger asChild>
                <span>
                  <InfoIcon className="h-4 w-4 ml-1 text-muted-foreground cursor-help" />
                </span>
              </TooltipTrigger>
              <TooltipContent>
                <p className="max-w-xs text-xs">{description}</p>
              </TooltipContent>
            </Tooltip>
          </div>
          <Badge className={`${colors.bg} ${colors.text} border-0`}>
            {score}/100
          </Badge>
        </div>
        
        <div className="health-progress mb-2">
          <div 
            className={`health-progress-bar ${colors.bar}`} 
            style={{ width: `${score}%` }}
          ></div>
        </div>
        
        <div className="mt-3 text-sm">
          <div className="flex justify-between items-center mb-1">
            <span className="text-muted-foreground">Previous:</span>
            <span className="font-medium">{previousScore || "N/A"}/100 {renderChangeIndicator()}</span>
          </div>
          
          <div className={`mt-2 p-2 rounded-md ${colors.bg} ${colors.text} text-xs`}>
            {insights}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
