
import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>Footer } from "@/components/ui/card";
import { ArrowRight } from "lucide-react";

export const TestReportFooter: React.FC = () => {
  return (
    <CardFooter className="flex flex-col sm:flex-row justify-between gap-3 border-t p-6 mt-6">
      <Button variant="outline" className="w-full sm:w-auto">Back to Test History</Button>
      <Button className="bg-brand-primary hover:bg-brand-dark w-full sm:w-auto">
        Schedule Next Test
        <ArrowRight className="ml-2 h-4 w-4" />
      </Button>
    </CardFooter>
  );
};
