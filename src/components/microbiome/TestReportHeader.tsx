
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Calendar, Download, Share } from "lucide-react";
import { usePetData } from "@/context/PetContext";

interface TestData {
  id: string;
  date: string;
  status: string;
  type: string;
  score: number;
  previousScore?: number;
  change: string;
  changeType: "positive" | "negative" | "neutral";
  pet: {
    name: string;
    age: number;
    breed: string;
    gender: string;
  };
}

interface TestReportHeaderProps {
  testData: TestData;
}

export const TestReportHeader: React.FC<TestReportHeaderProps> = ({ testData }) => {
  const { petData } = usePetData();
  
  return (
    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">{petData.name}'s Test Report</h1>
        <div className="flex items-center gap-3 mt-2">
          <Badge variant="outline" className="gap-1">
            <Calendar className="h-3 w-3" />
            {testData.date}
          </Badge>
          <Badge variant="secondary">
            {testData.type}
          </Badge>
          <Badge variant="outline">
            Test ID: {testData.id}
          </Badge>
        </div>
      </div>
      
      <div className="flex items-center gap-3">
        <Button variant="outline" size="sm" className="gap-2">
          <Share className="h-4 w-4" />
          Share Report
        </Button>
        <Button variant="outline" size="sm" className="gap-2">
          <Download className="h-4 w-4" />
          Download PDF
        </Button>
      </div>
    </div>
  );
};
