
import React from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { TestMetricCard } from "@/components/microbiome/TestMetricCard";
import { BacteriaTable } from "@/components/microbiome/BacteriaTable";

export const DetailedAnalysisTabs: React.FC = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Detailed Analysis</CardTitle>
        <CardDescription>Comprehensive breakdown of your pet's microbiome</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="bacteria">
          <TabsList className="inline-flex h-12 items-center justify-start rounded-md bg-muted p-1 text-muted-foreground overflow-x-auto max-w-full w-full">
            <TabsTrigger 
              value="bacteria" 
              className="inline-flex items-center justify-center whitespace-nowrap rounded-sm px-4 py-2 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm min-w-[120px] flex-shrink-0"
            >
              Bacteria Analysis
            </TabsTrigger>
            <TabsTrigger 
              value="digestion"
              className="inline-flex items-center justify-center whitespace-nowrap rounded-sm px-4 py-2 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm min-w-[120px] flex-shrink-0"
            >
              Digestion Markers
            </TabsTrigger>
            <TabsTrigger 
              value="inflammation"
              className="inline-flex items-center justify-center whitespace-nowrap rounded-sm px-4 py-2 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm min-w-[120px] flex-shrink-0"
            >
              Inflammation
            </TabsTrigger>
            <TabsTrigger 
              value="comparison"
              className="inline-flex items-center justify-center whitespace-nowrap rounded-sm px-4 py-2 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm min-w-[120px] flex-shrink-0"
            >
              Historical Data
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="bacteria" className="pt-6">
            <div className="mb-6">
              <h3 className="text-lg font-medium mb-4">Bacteria Analysis</h3>
              <p className="text-sm text-muted-foreground mb-6">
                This section shows the specific bacterial populations found in your pet's microbiome sample.
                The abundance of each bacteria type is compared to the healthy reference range.
              </p>
              
              <BacteriaTable />
            </div>
          </TabsContent>
          
          <TabsContent value="digestion" className="pt-6 space-y-6">
            <div className="grid md:grid-cols-2 gap-4">
              <TestMetricCard 
                title="Digestive Enzymes" 
                score={85} 
                previousScore={78}
                description="Enzymes needed to break down proteins, fats and carbohydrates"
                insights="Levels are optimal, indicating good digestive efficiency"
                status="good"
              />
              
              <TestMetricCard 
                title="Nutrient Absorption" 
                score={80} 
                previousScore={72}
                description="Ability to absorb and utilize nutrients from food"
                insights="Improving trend shows supplements are working effectively"
                status="good"
              />
              
              <TestMetricCard 
                title="Gut Motility" 
                score={76} 
                previousScore={70}
                description="Movement of food through the digestive tract"
                insights="Within normal range, but could be improved with fiber"
                status="good"
              />
              
              <TestMetricCard 
                title="Stool Consistency" 
                score={65} 
                previousScore={60}
                description="Quality and consistency of stool samples"
                insights="Showing improvement but still below optimal levels"
                status="moderate"
              />
            </div>
          </TabsContent>
          
          <TabsContent value="inflammation" className="pt-6 space-y-6">
            <div className="grid md:grid-cols-2 gap-4">
              <TestMetricCard 
                title="Inflammatory Markers" 
                score={88} 
                previousScore={75}
                description="Indicators of gut inflammation levels"
                insights="Significant improvement, showing reduced inflammation"
                status="good"
              />
              
              <TestMetricCard 
                title="Immune Response" 
                score={82} 
                previousScore={78}
                description="How well the immune system is functioning"
                insights="Strong, balanced immune response detected"
                status="good"
              />
              
              <TestMetricCard 
                title="Mucosal Health" 
                score={79} 
                previousScore={67}
                description="Condition of the protective gut lining"
                insights="Good improvement, supplements are effective"
                status="good"
              />
              
              <TestMetricCard 
                title="Anti-inflammatory Bacteria" 
                score={68} 
                previousScore={55}
                description="Bacteria that help reduce inflammation"
                insights="Improving but could benefit from probiotic support"
                status="moderate"
              />
            </div>
          </TabsContent>

          <TabsContent value="comparison" className="pt-6">
            <div className="mb-6">
              <h3 className="text-lg font-medium mb-4">Historical Trends</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Compare your pet's current results with previous tests to track progress over time.
              </p>
              
              <div className="aspect-ratio rounded-lg border p-4">
                <div className="text-center p-16 border border-dashed rounded-lg">
                  <p className="text-muted-foreground">Historical comparison chart will appear here</p>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};
