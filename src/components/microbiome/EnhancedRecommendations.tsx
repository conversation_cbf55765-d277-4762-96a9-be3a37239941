import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { 
  ArrowRight, 
  Pill, 
  Utensils, 
  Activity, 
  Clock,
  Star,
  CheckCircle
} from "lucide-react";

interface Recommendation {
  id: string;
  category: "supplement" | "nutrition" | "exercise" | "lifestyle";
  title: string;
  description: string;
  priority: "high" | "medium" | "low";
  timeframe: string;
  difficulty: "easy" | "moderate" | "challenging";
  expectedImprovement: string;
}

export const EnhancedRecommendations: React.FC = () => {
  const recommendations: Recommendation[] = [
    {
      id: "1",
      category: "supplement",
      title: "Increase Daily Probiotic",
      description: "Boost beneficial bacteria with an additional 25% increase in daily probiotic dosage to improve gut health balance.",
      priority: "high",
      timeframe: "Start immediately",
      difficulty: "easy",
      expectedImprovement: "15-20 point improvement expected"
    },
    {
      id: "2",
      category: "nutrition",
      title: "Add Fermented Foods",
      description: "Introduce pet-safe fermented foods like plain kefir (1 tsp daily) to naturally increase bacterial diversity.",
      priority: "high",
      timeframe: "Within 1 week",
      difficulty: "moderate",
      expectedImprovement: "10-15 point improvement expected"
    },
    {
      id: "3",
      category: "exercise",
      title: "Increase Physical Activity",
      description: "Add 10-15 minutes of daily exercise to support gut motility and overall digestive health.",
      priority: "medium",
      timeframe: "Start this week",
      difficulty: "easy",
      expectedImprovement: "5-10 point improvement expected"
    },
    {
      id: "4",
      category: "nutrition",
      title: "Fiber Supplementation",
      description: "Add 1-2 tablespoons of canned pumpkin daily to feed beneficial bacteria and improve stool consistency.",
      priority: "medium",
      timeframe: "Within 2 weeks",
      difficulty: "easy",
      expectedImprovement: "8-12 point improvement expected"
    }
  ];

  const getCategoryConfig = (category: string) => {
    switch (category) {
      case "supplement":
        return {
          icon: Pill,
          color: "bg-purple-100 text-purple-700",
          bgColor: "bg-purple-50"
        };
      case "nutrition":
        return {
          icon: Utensils,
          color: "bg-green-100 text-green-700",
          bgColor: "bg-green-50"
        };
      case "exercise":
        return {
          icon: Activity,
          color: "bg-blue-100 text-blue-700",
          bgColor: "bg-blue-50"
        };
      case "lifestyle":
        return {
          icon: Clock,
          color: "bg-orange-100 text-orange-700",
          bgColor: "bg-orange-50"
        };
      default:
        return {
          icon: Star,
          color: "bg-gray-100 text-gray-700",
          bgColor: "bg-gray-50"
        };
    }
  };

  const getPriorityConfig = (priority: string) => {
    switch (priority) {
      case "high":
        return "bg-red-100 text-red-700 border-red-200";
      case "medium":
        return "bg-yellow-100 text-yellow-700 border-yellow-200";
      case "low":
        return "bg-blue-100 text-blue-700 border-blue-200";
      default:
        return "bg-gray-100 text-gray-700 border-gray-200";
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "easy":
        return "text-green-600";
      case "moderate":
        return "text-yellow-600";
      case "challenging":
        return "text-red-600";
      default:
        return "text-gray-600";
    }
  };

  return (
    <Card>
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-2">
          <Star className="h-6 w-6 text-brand-primary" />
          Personalized Action Plan
        </CardTitle>
        <p className="text-muted-foreground">
          AI-generated recommendations tailored to your pet's specific microbiome profile
        </p>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-4">
          {recommendations.map((rec, index) => {
            const categoryConfig = getCategoryConfig(rec.category);
            const CategoryIcon = categoryConfig.icon;
            
            return (
              <div key={rec.id} className={`border rounded-xl p-5 ${categoryConfig.bgColor} border-gray-200 transition-all hover:shadow-md`}>
                <div className="flex items-start gap-4">
                  <div className="flex-shrink-0">
                    <div className={`w-12 h-12 rounded-xl ${categoryConfig.color} flex items-center justify-center`}>
                      <CategoryIcon className="h-6 w-6" />
                    </div>
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-3 mb-3">
                      <div className="min-w-0 flex-1">
                        <h3 className="font-semibold text-lg text-gray-900 mb-2">{rec.title}</h3>
                        <div className="flex flex-wrap gap-2">
                          <Badge className={categoryConfig.color} variant="outline">
                            {rec.category.charAt(0).toUpperCase() + rec.category.slice(1)}
                          </Badge>
                          <Badge className={getPriorityConfig(rec.priority)} variant="outline">
                            {rec.priority.charAt(0).toUpperCase() + rec.priority.slice(1)} Priority
                          </Badge>
                        </div>
                      </div>
                    </div>
                    
                    <p className="text-gray-700 text-sm leading-relaxed mb-4">{rec.description}</p>
                    
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-3 text-sm mb-4">
                      <div className="flex items-center gap-2 p-2 bg-white/50 rounded-lg">
                        <Clock className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                        <span className="font-medium text-gray-600">Timeline:</span>
                        <span className="text-gray-800 truncate">{rec.timeframe}</span>
                      </div>
                      <div className="flex items-center gap-2 p-2 bg-white/50 rounded-lg">
                        <Activity className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                        <span className="font-medium text-gray-600">Difficulty:</span>
                        <span className={`${getDifficultyColor(rec.difficulty)} font-medium`}>
                          {rec.difficulty.charAt(0).toUpperCase() + rec.difficulty.slice(1)}
                        </span>
                      </div>
                      <div className="flex items-center gap-2 p-2 bg-white/50 rounded-lg">
                        <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                        <span className="text-green-600 font-medium text-sm">{rec.expectedImprovement}</span>
                      </div>
                    </div>
                    
                    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 pt-2 border-t border-gray-200/50">
                      <Button size="sm" className="bg-brand-primary hover:bg-brand-dark w-full sm:w-auto">
                        Start Now
                        <ArrowRight className="h-4 w-4 ml-2" />
                      </Button>
                      <Button variant="link" size="sm" className="text-muted-foreground w-full sm:w-auto justify-center sm:justify-start">
                        Learn More
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
        
        <div className="mt-6 bg-brand-light rounded-xl p-6">
          <div className="flex items-start gap-4">
            <div className="w-12 h-12 bg-brand-primary rounded-xl flex items-center justify-center flex-shrink-0">
              <Star className="h-6 w-6 text-white" />
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="text-brand-primary font-semibold text-lg mb-3">
                Next Steps & Follow-up
              </h3>
              <p className="text-gray-700 mb-5 leading-relaxed">
                Your pet's microbiome health is showing positive trends. By implementing these recommendations, 
                you can expect to see significant improvements in the next 3 months. We recommend scheduling 
                a follow-up test to track progress and adjust the protocol as needed.
              </p>
              <div className="flex flex-col sm:flex-row flex-wrap gap-3">
                <Button className="bg-brand-primary hover:bg-brand-dark w-full sm:w-auto">
                  Schedule Follow-up Test
                </Button>
                <Button variant="outline" className="w-full sm:w-auto">
                  Download Action Plan
                </Button>
                <Button variant="outline" className="w-full sm:w-auto">
                  Book Expert Consultation
                </Button>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
