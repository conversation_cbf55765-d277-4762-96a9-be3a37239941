
import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { InfoIcon, ChevronDownIcon } from "lucide-react";
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

export const BacteriaTable = () => {
  // In a real app, this would be fetched from an API
  const bacteriaData = [
    {
      id: "1",
      name: "Lactobacillus acidophilus",
      abundance: 18.2,
      normalRange: "15-25%",
      status: "normal",
      change: "****%",
      changeType: "positive",
      type: "Beneficial",
      description: "Supports digestive health and helps maintain gut barrier function."
    },
    {
      id: "2",
      name: "Bifidobacterium longum",
      abundance: 12.5,
      normalRange: "10-20%",
      status: "normal",
      change: "****%",
      changeType: "positive",
      type: "Beneficial",
      description: "Helps metabolize fiber and produces beneficial short-chain fatty acids."
    },
    {
      id: "3",
      name: "Faecalibacterium pra<PERSON>nitzii",
      abundance: 7.8,
      normalRange: "8-15%",
      status: "low",
      change: "+1.2%",
      changeType: "positive",
      type: "Beneficial",
      description: "Anti-inflammatory bacteria that produces butyrate."
    },
    {
      id: "4",
      name: "Escherichia coli",
      abundance: 1.5,
      normalRange: "0.5-2%",
      status: "normal",
      change: "-0.3%",
      changeType: "positive",
      type: "Neutral",
      description: "Common gut bacteria that can be beneficial in normal amounts."
    },
    {
      id: "5",
      name: "Clostridium perfringens",
      abundance: 0.8,
      normalRange: "<1%",
      status: "normal",
      change: "-0.4%",
      changeType: "positive",
      type: "Inflammatory",
      description: "Can cause digestive upset when present in high numbers."
    },
    {
      id: "6",
      name: "Akkermansia muciniphila",
      abundance: 1.2,
      normalRange: "1-3%",
      status: "normal",
      change: "+0.7%",
      changeType: "positive",
      type: "Beneficial",
      description: "Associated with gut barrier health and metabolism."
    }
  ];

  const getStatusBadge = (status: string, type: string) => {
    if (status === "normal") {
      return (
        <Badge 
          variant="outline" 
          className="bg-green-50 text-green-700 border-green-200"
        >
          Normal
        </Badge>
      );
    } else if (status === "low") {
      return (
        <Badge 
          variant="outline" 
          className="bg-yellow-50 text-yellow-700 border-yellow-200"
        >
          Low
        </Badge>
      );
    } else if (status === "high" && (type === "Inflammatory" || type === "Pathogenic")) {
      return (
        <Badge 
          variant="outline" 
          className="bg-red-50 text-red-700 border-red-200"
        >
          High
        </Badge>
      );
    } else if (status === "high") {
      return (
        <Badge 
          variant="outline" 
          className="bg-blue-50 text-blue-700 border-blue-200"
        >
          High
        </Badge>
      );
    }
    
    return (
      <Badge variant="outline">
        {status}
      </Badge>
    );
  };

  const getTypeIndicator = (type: string) => {
    switch (type) {
      case "Beneficial":
        return <div className="w-3 h-3 rounded-full bg-green-500"></div>;
      case "Neutral":
        return <div className="w-3 h-3 rounded-full bg-blue-300"></div>;
      case "Inflammatory":
        return <div className="w-3 h-3 rounded-full bg-yellow-500"></div>;
      case "Pathogenic":
        return <div className="w-3 h-3 rounded-full bg-red-500"></div>;
      default:
        return <div className="w-3 h-3 rounded-full bg-gray-300"></div>;
    }
  };

  return (
    <div className="rounded-lg border overflow-hidden">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[250px]">Bacteria</TableHead>
            <TableHead className="w-[100px]">Type</TableHead>
            <TableHead className="w-[120px]">Abundance</TableHead>
            <TableHead className="w-[120px]">Normal Range</TableHead>
            <TableHead className="w-[100px]">Status</TableHead>
            <TableHead className="w-[100px]">Change</TableHead>
            <TableHead>Description</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {bacteriaData.map((bacteria) => (
            <TableRow key={bacteria.id}>
              <TableCell className="font-medium">{bacteria.name}</TableCell>
              <TableCell>
                <div className="flex items-center gap-2">
                  {getTypeIndicator(bacteria.type)}
                  <span>{bacteria.type}</span>
                </div>
              </TableCell>
              <TableCell>{bacteria.abundance}%</TableCell>
              <TableCell>{bacteria.normalRange}</TableCell>
              <TableCell>{getStatusBadge(bacteria.status, bacteria.type)}</TableCell>
              <TableCell className={bacteria.changeType === "positive" ? "text-green-600" : "text-red-600"}>
                {bacteria.change}
              </TableCell>
              <TableCell className="text-sm text-muted-foreground max-w-xs truncate">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <span className="cursor-help flex items-center">
                        {bacteria.description.substring(0, 30)}...
                        <InfoIcon className="h-3 w-3 ml-1 text-muted-foreground" />
                      </span>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs text-xs">{bacteria.description}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
      <div className="p-4 border-t bg-muted/30 text-center">
        <Button variant="outline" size="sm">
          Show All Bacteria <ChevronDownIcon className="h-4 w-4 ml-1" />
        </Button>
      </div>
    </div>
  );
};
