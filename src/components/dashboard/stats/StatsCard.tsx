
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { TrendingUp, TrendingDown, Flame, CheckCircle } from "lucide-react";
import { cn } from "@/lib/utils";

interface StatItemProps {
  label: string;
  value: string | number;
  trend?: "up" | "down" | "neutral";
  badge?: {
    text: string;
    icon?: React.ReactNode;
    variant?: "default" | "secondary" | "destructive" | "outline";
  };
}

const StatItem: React.FC<StatItemProps> = ({ label, value, trend, badge }) => {
  const getTrendIcon = () => {
    if (trend === "up") return <TrendingUp className="h-4 w-4 text-green-500" />;
    if (trend === "down") return <TrendingDown className="h-4 w-4 text-red-500" />;
    return null;
  };

  return (
    <div className="flex-1 text-center">
      <div className="flex items-center justify-center gap-1.5 mb-1">
        <div className="text-2xl md:text-3xl font-bold text-gray-900 tabular-nums">
          {value}
        </div>
        {getTrendIcon()}
      </div>
      <div className="text-xs font-medium text-gray-600 uppercase tracking-wide mb-2">
        {label}
      </div>
      {badge && (
        <Badge 
          variant={badge.variant || "default"}
          className={cn(
            "text-xs font-medium",
            badge.variant === "default" && "bg-orange-100 text-orange-700 border-orange-200 hover:bg-orange-100"
          )}
        >
          {badge.icon}
          {badge.text}
        </Badge>
      )}
    </div>
  );
};

interface StatsCardProps {
  shapeScore: number;
  streak: number;
  completedCategories: number;
  totalCategories: number;
  completionRate: number;
  hasAnyEngagement: boolean;
  motivationalMessage?: string | null;
  trend?: "up" | "down" | "neutral";
  showStreakBadge?: boolean;
}

export const StatsCard: React.FC<StatsCardProps> = ({
  shapeScore,
  streak,
  completedCategories,
  totalCategories,
  completionRate,
  hasAnyEngagement,
  motivationalMessage,
  trend = "up",
  showStreakBadge = true
}) => {
  return (
    <Card className="border-0 shadow-sm bg-gradient-to-r from-brand-light/30 to-brand-light/10">
      <CardContent className="p-4 space-y-4">
        {/* Stats Row */}
        <div className="flex items-center divide-x divide-gray-200">
          <StatItem
            label="Shape Score"
            value={shapeScore}
            trend={trend}
          />
          <StatItem
            label="Streak"
            value={`${streak} day${streak !== 1 ? 's' : ''}`}
            badge={showStreakBadge && streak > 0 ? {
              text: "On fire!",
              icon: <Flame className="h-3 w-3 mr-1" />,
              variant: "default"
            } : undefined}
          />
        </div>

        {/* Progress Section - Only show if user has engagement */}
        {hasAnyEngagement && (
          <div className="space-y-3">
            {/* Progress Bar */}
            <div className="flex items-center gap-3">
              <div className="flex-1">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700">
                    Today's Progress
                  </span>
                  <span className="text-sm text-gray-500">
                    {completedCategories}/{totalCategories}
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-gradient-to-r from-green-500 to-emerald-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${completionRate}%` }}
                  />
                </div>
              </div>
              {completionRate === 100 && (
                <CheckCircle className="h-5 w-5 text-green-500" />
              )}
            </div>

            {/* Motivational Message */}
            {motivationalMessage && (
              <div className="flex items-center justify-center gap-2 p-3 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl">
                <span className="text-sm font-medium text-green-700 text-center">
                  {motivationalMessage}
                </span>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
