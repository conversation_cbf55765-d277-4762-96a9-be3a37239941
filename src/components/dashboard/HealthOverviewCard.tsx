
import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ArrowR<PERSON>, Clock } from "lucide-react";
import { HealthMetricsTab } from "./HealthMetricsTab";
import { useIsMobile } from "@/hooks/use-mobile";

interface HealthOverviewCardProps {
  onViewFullReport: () => void;
}

export const HealthOverviewCard: React.FC<HealthOverviewCardProps> = ({ onViewFullReport }) => {
  const isMobile = useIsMobile();
  
  return (
    <Card className="mb-6">
      <CardHeader className="pb-3">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-0">
          <CardTitle>Health Overview</CardTitle>
          <Badge variant="outline" className="font-normal px-3 py-1 flex items-center gap-1.5 bg-brand-light border-brand-beige self-start sm:self-auto">
            <Clock className="h-3.5 w-3.5 text-brand-primary flex-shrink-0" />
            <span className="text-xs sm:text-sm whitespace-nowrap">Last updated: 2 days ago</span>
          </Badge>
        </div>
        <CardDescription>Based on your pet's latest diagnostic results</CardDescription>
      </CardHeader>
      <CardContent>
        <HealthMetricsTab />
      </CardContent>
      <CardFooter className="border-t pt-4 flex flex-col sm:flex-row justify-between gap-3 sm:gap-0">
        <Button variant="outline">View Full Health Profile</Button>
        <div className="flex flex-col sm:flex-row gap-3">
          <Button variant="link" className="text-brand-primary justify-start sm:justify-center">
            Track Changes Over Time
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
          <Button className="bg-brand-primary hover:bg-brand-dark" onClick={onViewFullReport}>
            View Test Report
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
};
