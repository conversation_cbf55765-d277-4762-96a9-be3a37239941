
import React from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ArrowUpRight, Calendar, MessageSquare } from "lucide-react";
import { useNavigate } from "react-router-dom";

export const DashboardCards: React.FC = () => {
  const navigate = useNavigate();
  
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {/* Current Protocol */}
      <Card>
        <CardHeader>
          <CardTitle>Current Protocol</CardTitle>
          <CardDescription>Updated 2 weeks ago</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="border rounded-lg p-3">
            <div className="flex items-center justify-between mb-2">
              <span className="font-medium">Gut <PERSON>ore</span>
              <Badge>Morning</Badge>
            </div>
            <p className="text-sm text-muted-foreground">
              Probiotic blend to increase beneficial bacteria
            </p>
          </div>
          
          <div className="border rounded-lg p-3">
            <div className="flex items-center justify-between mb-2">
              <span className="font-medium">Digestive Enzymes</span>
              <Badge>With Meals</Badge>
            </div>
            <p className="text-sm text-muted-foreground">
              Supports nutrient absorption and digestion
            </p>
          </div>
          
          <div className="border rounded-lg p-3">
            <div className="flex items-center justify-between mb-2">
              <span className="font-medium">Omega Support</span>
              <Badge>Evening</Badge>
            </div>
            <p className="text-sm text-muted-foreground">
              Anti-inflammatory and coat health support
            </p>
          </div>
        </CardContent>
        <CardFooter>
          <Button className="w-full" variant="outline">Manage Supplements</Button>
        </CardFooter>
      </Card>
      
      {/* Next Steps */}
      <Card>
        <CardHeader>
          <CardTitle>Next Steps</CardTitle>
          <CardDescription>Recommended actions for Sophie</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-start gap-3">
            <div className="bg-brand-light p-2 rounded-full">
              <Calendar className="h-5 w-5 text-brand-primary" />
            </div>
            <div>
              <h4 className="font-medium mb-1">Schedule Next Test</h4>
              <p className="text-sm text-muted-foreground">
                Due in 3 months (October 15)
              </p>
            </div>
          </div>
          
          <div className="flex items-start gap-3">
            <div className="bg-brand-light p-2 rounded-full">
              <MessageSquare className="h-5 w-5 text-brand-primary" />
            </div>
            <div>
              <h4 className="font-medium mb-1">Consult with AI Coach</h4>
              <p className="text-sm text-muted-foreground">
                Review recent progress and get personalized advice
              </p>
            </div>
          </div>
          
          <div className="flex items-start gap-3">
            <div className="bg-brand-light p-2 rounded-full">
              <ArrowUpRight className="h-5 w-5 text-brand-primary" />
            </div>
            <div>
              <h4 className="font-medium mb-1">Increase Exercise</h4>
              <p className="text-sm text-muted-foreground">
                AI recommends 10% more daily activity for optimal gut health
              </p>
            </div>
          </div>
        </CardContent>
        <CardFooter>
          <Button className="w-full bg-brand-primary hover:bg-brand-dark">Take Next Step</Button>
        </CardFooter>
      </Card>
    </div>
  );
};
