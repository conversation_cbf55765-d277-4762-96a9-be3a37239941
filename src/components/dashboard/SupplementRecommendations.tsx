
import React from "react";
import { SupplementRecommendationCard } from "@/components/dashboard/health/SupplementRecommendationCard";

interface SupplementRecommendation {
  id: string;
  name: string;
  reason: string;
  description: string;
  dosage: string;
  benefits: string[];
  urgency: 'low' | 'medium' | 'high';
  basedOn: string[];
}

interface SupplementRecommendationsProps {
  recommendations: SupplementRecommendation[];
  onStartSupplement: (supplementName: string) => void;
  onDismiss: (id: string) => void;
}

export const SupplementRecommendations: React.FC<SupplementRecommendationsProps> = ({
  recommendations,
  onStartSupplement,
  onDismiss
}) => {
  if (recommendations.length === 0) return null;

  return (
    <div className="space-y-3">
      <h3 className="text-lg font-semibold">AI Recommendations</h3>
      {recommendations.map((recommendation) => (
        <SupplementRecommendationCard
          key={recommendation.id}
          recommendation={recommendation}
          onStartSupplement={onStartSupplement}
          onDismiss={onDismiss}
        />
      ))}
    </div>
  );
};
