
import React, { useState } from "react";
import { AlertTriangle, InfoIcon, ChevronDown, ChevronUp } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { RiskFactor } from "@/types/health";
import { 
  Collapsible,
  CollapsibleTrigger,
  CollapsibleContent 
} from "@/components/ui/collapsible";

interface RiskFactorCardProps {
  risk: RiskFactor;
}

export const RiskFactorCard: React.FC<RiskFactorCardProps> = ({ risk }) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="border rounded-lg p-4">
      <Collapsible open={isOpen} onOpenChange={setIsOpen} className="w-full">
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-3">
            {risk.severity === "high" ? (
              <div className="mt-1">
                <AlertTriangle className="h-5 w-5 text-red-500" />
              </div>
            ) : risk.severity === "moderate" ? (
              <div className="mt-1">
                <AlertTriangle className="h-5 w-5 text-yellow-500" />
              </div>
            ) : (
              <div className="mt-1">
                <InfoIcon className="h-5 w-5 text-blue-500" />
              </div>
            )}
            
            <div>
              <h4 className="font-medium">{risk.name}</h4>
              <p className="text-sm text-muted-foreground">{risk.description}</p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Badge 
              className={`${
                risk.severity === "high" 
                  ? "bg-red-50 text-red-700 border-red-200" 
                  : risk.severity === "moderate"
                  ? "bg-yellow-50 text-yellow-700 border-yellow-200"
                  : "bg-blue-50 text-blue-700 border-blue-200"
              }`}
            >
              {risk.riskScore}/100
            </Badge>
            
            <CollapsibleTrigger asChild>
              <button className="p-1 hover:bg-muted rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-200">
                {isOpen ? (
                  <ChevronUp className="h-4 w-4 text-gray-500" />
                ) : (
                  <ChevronDown className="h-4 w-4 text-gray-500" />
                )}
              </button>
            </CollapsibleTrigger>
          </div>
        </div>
        
        <CollapsibleContent className="mt-3 pl-8 pt-2 transition-all">
          <h5 className="text-sm font-medium mb-1">Recommended Preventative Actions:</h5>
          <ul className="text-sm space-y-1">
            {risk.recommendations.map((rec, index) => (
              <li key={index} className="flex items-start gap-2">
                <span>•</span>
                <span>{rec}</span>
              </li>
            ))}
          </ul>
        </CollapsibleContent>
      </Collapsible>
    </div>
  );
};
