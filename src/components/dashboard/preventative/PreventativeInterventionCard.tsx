
import React from "react";
import { Badge } from "@/components/ui/badge";

interface InterventionProps {
  type: "supplement" | "nutrition" | "exercise" | "environment";
  name: string;
  description: string;
  frequency: string;
  duration: string;
}

export const PreventativeInterventionCard: React.FC<InterventionProps> = ({
  type,
  name,
  description,
  frequency,
  duration
}) => {
  return (
    <div className="bg-muted/50 rounded-lg p-3">
      <div className="flex items-center justify-between">
        <h6 className="font-medium">{name}</h6>
        <Badge variant="outline">
          {type.charAt(0).toUpperCase() + type.slice(1)}
        </Badge>
      </div>
      <p className="text-sm text-muted-foreground mt-1">
        {description}
      </p>
      <div className="flex justify-between text-xs mt-2">
        <span>Frequency: {frequency}</span>
        <span>Duration: {duration}</span>
      </div>
    </div>
  );
};
