
import { RiskFactor, PreventativePlan } from "@/types/health";

// Mock data for risk factors
export const mockRiskFactors: RiskFactor[] = [
  {
    id: "risk1",
    name: "Joint Deterioration",
    description: "Labradors are prone to hip dysplasia and joint issues as they age",
    riskScore: 65,
    severity: "moderate",
    category: "genetic",
    recommendations: [
      "Joint support supplements with glucosamine and chondroitin",
      "Maintain healthy weight to reduce joint stress",
      "Regular low-impact exercise like swimming"
    ]
  },
  {
    id: "risk2",
    name: "Digestive Sensitivity",
    description: "Current microbiome tests show potential for inflammatory response to certain proteins",
    riskScore: 48,
    severity: "moderate",
    category: "dietary",
    recommendations: [
      "Avoid chicken-based proteins",
      "Add digestive enzymes with meals",
      "Increase fiber intake gradually"
    ]
  },
  {
    id: "risk3",
    name: "Seasonal Allergies",
    description: "History of mild skin irritation during spring months",
    riskScore: 35,
    severity: "low",
    category: "environmental",
    recommendations: [
      "Omega-3 fatty acid supplementation",
      "Regular bathing with hypoallergenic shampoo during allergy season",
      "Monitor and record symptom triggers"
    ]
  }
];

// Mock data for preventative plan
export const mockPreventativePlan: PreventativePlan = {
  id: "plan1",
  name: "<PERSON>'s Spring 2025 Preventative Plan",
  description: "A comprehensive approach to address Bella's specific risk factors proactively",
  riskFactors: ["Joint Deterioration", "Digestive Sensitivity", "Seasonal Allergies"],
  interventions: [
    {
      type: "supplement",
      name: "Joint Protection Complex",
      description: "Advanced formula with glucosamine, chondroitin, MSM and green-lipped mussel",
      frequency: "Once daily with food",
      duration: "Ongoing"
    },
    {
      type: "nutrition",
      name: "Limited Ingredient Diet",
      description: "Switch to lamb and rice formula with limited ingredients to reduce inflammatory triggers",
      frequency: "All meals",
      duration: "3-month trial period"
    },
    {
      type: "exercise",
      name: "Hydrotherapy Sessions",
      description: "Low-impact swimming to strengthen joints without stress",
      frequency: "Weekly",
      duration: "8 weeks"
    }
  ],
  monitoringSchedule: "Monthly check-ins with progress photos and mobility assessment",
  expectedOutcomes: [
    "Increased joint mobility and reduced stiffness after exercise",
    "Improved coat quality and reduced itching",
    "More consistent stool quality and reduced digestive upset"
  ]
};
