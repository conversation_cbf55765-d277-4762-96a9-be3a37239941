
import React, { useState } from "react";
import { RiskFactorCard } from "./RiskFactorCard";
import { RiskFactor } from "@/types/health";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ChevronDown } from "lucide-react";

interface RiskFactorsSectionProps {
  riskFactors: RiskFactor[];
}

type SortField = "severity" | "category" | "default";

export const RiskFactorsSection: React.FC<RiskFactorsSectionProps> = ({ 
  riskFactors 
}) => {
  const [sortField, setSortField] = useState<SortField>("default");

  const sortRiskFactors = (factors: RiskFactor[]): RiskFactor[] => {
    if (sortField === "default") return factors;

    return [...factors].sort((a, b) => {
      if (sortField === "severity") {
        // Sort by severity: high > moderate > low
        const severityOrder = { high: 1, moderate: 2, low: 3 };
        return severityOrder[a.severity] - severityOrder[b.severity];
      } else if (sortField === "category") {
        // Sort alphabetically by category
        return a.category.localeCompare(b.category);
      }
      return 0;
    });
  };

  const sortedRiskFactors = sortRiskFactors(riskFactors);

  return (
    <div>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium">Identified Risk Factors</h3>
        
        <div className="flex items-center gap-4">
          <Select value={sortField} onValueChange={(value) => setSortField(value as SortField)}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Sort by..." />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="default">Default Order</SelectItem>
              <SelectItem value="severity">Sort by Severity</SelectItem>
              <SelectItem value="category">Sort by Category</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      
      <div className="space-y-4">
        {sortedRiskFactors.map((risk) => (
          <RiskFactorCard key={risk.id} risk={risk} />
        ))}
      </div>
    </div>
  );
};
