
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import { PreventativePlan } from "@/types/health";
import { PreventativeInterventionCard } from "./PreventativeInterventionCard";

interface PreventativePlanSectionProps {
  plan: PreventativePlan;
}

export const PreventativePlanSection: React.FC<PreventativePlanSectionProps> = ({
  plan
}) => {
  return (
    <div className="pt-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium">Personalized Preventative Plan</h3>
        <Button variant="ghost" size="sm" className="text-brand-primary">
          Generate New Plan
        </Button>
      </div>
      
      <div className="border rounded-lg p-4">
        <h4 className="font-medium text-lg mb-1">{plan.name}</h4>
        <p className="text-sm text-muted-foreground mb-4">{plan.description}</p>
        
        <div className="space-y-4">
          <div>
            <h5 className="text-sm font-medium mb-2">Interventions</h5>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {plan.interventions.map((intervention, index) => (
                <PreventativeInterventionCard
                  key={index}
                  type={intervention.type}
                  name={intervention.name}
                  description={intervention.description}
                  frequency={intervention.frequency}
                  duration={intervention.duration}
                />
              ))}
            </div>
          </div>
          
          <div>
            <h5 className="text-sm font-medium mb-2">Monitoring Schedule</h5>
            <p className="text-sm">{plan.monitoringSchedule}</p>
          </div>
          
          <div>
            <h5 className="text-sm font-medium mb-2">Expected Outcomes</h5>
            <ul className="text-sm space-y-1">
              {plan.expectedOutcomes.map((outcome, index) => (
                <li key={index} className="flex items-start gap-2">
                  <span>•</span>
                  <span>{outcome}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
      
      <div className="flex justify-end mt-4">
        <Button className="bg-brand-primary hover:bg-brand-dark">
          Implement Preventative Plan
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};
