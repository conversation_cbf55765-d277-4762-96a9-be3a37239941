import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { InfoIcon, AlertTriangle, ArrowRight } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

export const HealthMetricsTab: React.FC = () => {
  return (
    <Tabs defaultValue="microbiome">
      <TabsList className="grid w-full grid-cols-3">
        <TabsTrigger value="microbiome" className="text-xs sm:text-sm px-1 sm:px-3">Microbiome Health</TabsTrigger>
        <TabsTrigger value="gi" className="text-xs sm:text-sm px-1 sm:px-3">GI Function</TabsTrigger>
        <TabsTrigger value="inflammation" className="text-xs sm:text-sm px-1 sm:px-3">Inflammation</TabsTrigger>
      </TabsList>
      
      <TabsContent value="microbiome" className="pt-6">
        <div className="flex flex-col md:flex-row gap-8">
          <div className="flex-1">
            <div className="mb-6">
              <div className="flex justify-between mb-2">
                <div className="flex items-center gap-2">
                  <span className="font-medium text-sm">Overall Microbiome Score</span>
                  <InfoIcon className="h-4 w-4 text-muted-foreground" />
                </div>
                <span className="font-bold">82/100</span>
              </div>
              <div className="health-progress">
                <div className="health-progress-bar bg-green-500" style={{ width: "82%" }}></div>
              </div>
            </div>
            
            <div className="mb-6">
              <div className="flex justify-between mb-2">
                <div className="flex items-center gap-2">
                  <span className="font-medium text-sm">Beneficial Bacteria</span>
                  <InfoIcon className="h-4 w-4 text-muted-foreground" />
                </div>
                <span className="font-bold">78/100</span>
              </div>
              <div className="health-progress">
                <div className="health-progress-bar bg-emerald-500" style={{ width: "78%" }}></div>
              </div>
            </div>
            
            <div className="mb-6">
              <div className="flex justify-between mb-2">
                <div className="flex items-center gap-2">
                  <span className="font-medium text-sm">Bacterial Diversity</span>
                  <InfoIcon className="h-4 w-4 text-muted-foreground" />
                </div>
                <span className="font-bold">85/100</span>
              </div>
              <div className="health-progress">
                <div className="health-progress-bar bg-emerald-500" style={{ width: "85%" }}></div>
              </div>
            </div>
            
            <div>
              <div className="flex justify-between mb-2">
                <div className="flex items-center gap-2">
                  <span className="font-medium text-sm">Balance</span>
                  <InfoIcon className="h-4 w-4 text-muted-foreground" />
                </div>
                <span className="font-bold">65/100</span>
              </div>
              <div className="health-progress">
                <div className="health-progress-bar bg-yellow-500" style={{ width: "65%" }}></div>
              </div>
            </div>
          </div>
          
          <div className="md:w-72 flex flex-col">
            <div className="bg-muted rounded-lg p-4 mb-4">
              <h4 className="font-medium mb-2 flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-yellow-500" />
                Issue Detected
              </h4>
              <p className="text-sm text-muted-foreground mb-2">
                Bella's microbiome balance score is below target. This may contribute to occasional digestive issues.
              </p>
              <Button variant="link" className="p-0 h-auto text-sm text-brand-primary">
                View detailed report
                <ArrowRight className="ml-1 h-3 w-3" />
              </Button>
            </div>
            
            <div className="bg-brand-light rounded-lg p-4">
              <h4 className="font-medium mb-2 text-brand-primary">AI Coach Suggestion</h4>
              <p className="text-sm text-muted-foreground mb-2">
                "Consider adding fermented foods to Bella's diet to increase diversity and beneficial bacteria."
              </p>
              <Button variant="link" className="p-0 h-auto text-sm text-brand-primary">
                More suggestions
                <ArrowRight className="ml-1 h-3 w-3" />
              </Button>
            </div>
          </div>
        </div>
      </TabsContent>
      
      <TabsContent value="gi" className="pt-6">
        <div className="flex flex-col md:flex-row gap-8">
          <div className="flex-1">
            <div className="mb-6">
              <div className="flex justify-between mb-2">
                <div className="flex items-center gap-2">
                  <span className="font-medium text-sm">Overall GI Function</span>
                  <InfoIcon className="h-4 w-4 text-muted-foreground" />
                </div>
                <span className="font-bold">75/100</span>
              </div>
              <div className="health-progress">
                <div className="health-progress-bar bg-green-500" style={{ width: "75%" }}></div>
              </div>
            </div>
            
            <div className="mb-6">
              <div className="flex justify-between mb-2">
                <div className="flex items-center gap-2">
                  <span className="font-medium text-sm">Digestive Efficiency</span>
                  <InfoIcon className="h-4 w-4 text-muted-foreground" />
                </div>
                <span className="font-bold">80/100</span>
              </div>
              <div className="health-progress">
                <div className="health-progress-bar bg-emerald-500" style={{ width: "80%" }}></div>
              </div>
            </div>
            
            <div className="mb-6">
              <div className="flex justify-between mb-2">
                <div className="flex items-center gap-2">
                  <span className="font-medium text-sm">Nutrient Absorption</span>
                  <InfoIcon className="h-4 w-4 text-muted-foreground" />
                </div>
                <span className="font-bold">82/100</span>
              </div>
              <div className="health-progress">
                <div className="health-progress-bar bg-emerald-500" style={{ width: "82%" }}></div>
              </div>
            </div>
            
            <div>
              <div className="flex justify-between mb-2">
                <div className="flex items-center gap-2">
                  <span className="font-medium text-sm">Stool Quality</span>
                  <InfoIcon className="h-4 w-4 text-muted-foreground" />
                </div>
                <span className="font-bold">70/100</span>
              </div>
              <div className="health-progress">
                <div className="health-progress-bar bg-yellow-500" style={{ width: "70%" }}></div>
              </div>
            </div>
          </div>
          
          <div className="md:w-72 flex flex-col">
            <div className="bg-muted rounded-lg p-4 mb-4">
              <h4 className="font-medium mb-2">Key Insights</h4>
              <p className="text-sm text-muted-foreground mb-2">
                Bella's GI function has improved by 15% since the last test. Continue with the current supplement protocol.
              </p>
              <Button variant="link" className="p-0 h-auto text-sm text-brand-primary">
                View trends
                <ArrowRight className="ml-1 h-3 w-3" />
              </Button>
            </div>
            
            <div className="bg-brand-light rounded-lg p-4">
              <h4 className="font-medium mb-2 text-brand-primary">AI Coach Suggestion</h4>
              <p className="text-sm text-muted-foreground mb-2">
                "Consider smaller, more frequent meals to help with Bella's nutrient absorption."
              </p>
              <Button variant="link" className="p-0 h-auto text-sm text-brand-primary">
                More suggestions
                <ArrowRight className="ml-1 h-3 w-3" />
              </Button>
            </div>
          </div>
        </div>
      </TabsContent>
      
      <TabsContent value="inflammation" className="pt-6">
        <div className="flex flex-col md:flex-row gap-8">
          <div className="flex-1">
            <div className="mb-6">
              <div className="flex justify-between mb-2">
                <div className="flex items-center gap-2">
                  <span className="font-medium text-sm">Overall Inflammation</span>
                  <InfoIcon className="h-4 w-4 text-muted-foreground" />
                </div>
                <span className="font-bold">88/100</span>
              </div>
              <div className="health-progress">
                <div className="health-progress-bar bg-green-500" style={{ width: "88%" }}></div>
              </div>
            </div>
            
            <div className="mb-6">
              <div className="flex justify-between mb-2">
                <div className="flex items-center gap-2">
                  <span className="font-medium text-sm">Gut Inflammation</span>
                  <InfoIcon className="h-4 w-4 text-muted-foreground" />
                </div>
                <span className="font-bold">90/100</span>
              </div>
              <div className="health-progress">
                <div className="health-progress-bar bg-emerald-500" style={{ width: "90%" }}></div>
              </div>
            </div>
            
            <div className="mb-6">
              <div className="flex justify-between mb-2">
                <div className="flex items-center gap-2">
                  <span className="font-medium text-sm">Immune Response</span>
                  <InfoIcon className="h-4 w-4 text-muted-foreground" />
                </div>
                <span className="font-bold">85/100</span>
              </div>
              <div className="health-progress">
                <div className="health-progress-bar bg-emerald-500" style={{ width: "85%" }}></div>
              </div>
            </div>
            
            <div>
              <div className="flex justify-between mb-2">
                <div className="flex items-center gap-2">
                  <span className="font-medium text-sm">Skin Health</span>
                  <InfoIcon className="h-4 w-4 text-muted-foreground" />
                </div>
                <span className="font-bold">78/100</span>
              </div>
              <div className="health-progress">
                <div className="health-progress-bar bg-green-500" style={{ width: "78%" }}></div>
              </div>
            </div>
          </div>
          
          <div className="md:w-72 flex flex-col">
            <div className="bg-muted rounded-lg p-4 mb-4">
              <h4 className="font-medium mb-2">Key Insights</h4>
              <p className="text-sm text-muted-foreground mb-2">
                Bella's inflammation markers are well within healthy ranges. The anti-inflammatory supplements are working effectively.
              </p>
              <Button variant="link" className="p-0 h-auto text-sm text-brand-primary">
                Learn more
                <ArrowRight className="ml-1 h-3 w-3" />
              </Button>
            </div>
            
            <div className="bg-brand-light rounded-lg p-4">
              <h4 className="font-medium mb-2 text-brand-primary">AI Coach Note</h4>
              <p className="text-sm text-muted-foreground mb-2">
                "Great job! Continue with the current protocol and monitor for any seasonal allergy changes."
              </p>
              <Button variant="link" className="p-0 h-auto text-sm text-brand-primary">
                Chat with Coach
                <ArrowRight className="ml-1 h-3 w-3" />
              </Button>
            </div>
          </div>
        </div>
      </TabsContent>
    </Tabs>
  );
};
