
import React from "react";
import { MainContentGrid } from "@/components/dashboard/MainContentGrid";
import { DashboardStats } from "@/components/dashboard/DashboardStats";
import { RecentActivityCard } from "@/components/dashboard/RecentActivityCard";
import { HealthInsightsCard } from "@/components/dashboard/HealthInsightsCard";
import { SupplementRecommendations } from "@/components/dashboard/SupplementRecommendations";
import { SupplementAdherenceCard } from "@/components/dashboard/sense/SupplementAdherenceCard";
import { CelebrationBanner } from "@/components/dashboard/celebration/CelebrationBanner";
import { StatsCard } from "@/components/dashboard/stats/StatsCard";
import { EnhancedShapeCheckInSection } from "@/components/dashboard/EnhancedShapeCheckInSection";
import { useSupplementAdherence } from "@/hooks/useSupplementAdherence";

interface Pet {
  id: string;
  name: string;
  breed: string;
  age: number;
  avatar?: string;
}

interface ShapeRatings {
  sleep: number;
  hydration: number;
  activity: number;
  poop: number;
  eating: number;
}

interface DashboardMainContentProps {
  activePet: Pet;
  isFullyCompleted: boolean;
  shapeScore: number;
  streak: number;
  hasAnyEngagement: boolean;
  completedCategories: number;
  totalCategories: number;
  completionRate: number;
  motivationalMessage: string | null;
  shapeRatings: ShapeRatings;
  overallScore: number;
  poopLogs: any[];
  recommendations: any[];
  dashboardState: any;
  onCardClick: (category: keyof ShapeRatings, title: string, letter: string, description: string) => void;
  onQuickAction: () => void;
  onAddPoopLog: (log: any) => void;
  startSupplement: (recommendation: any) => void;
  dismissRecommendation: (id: string) => void;
}

export const DashboardMainContent: React.FC<DashboardMainContentProps> = ({
  activePet,
  isFullyCompleted,
  shapeScore,
  streak,
  hasAnyEngagement,
  completedCategories,
  totalCategories,
  completionRate,
  motivationalMessage,
  shapeRatings,
  overallScore,
  poopLogs,
  recommendations,
  dashboardState,
  onCardClick,
  onQuickAction,
  onAddPoopLog,
  startSupplement,
  dismissRecommendation
}) => {
  const { supplements, adherenceRate, toggleSupplement } = useSupplementAdherence(activePet.id);

  return (
    <div className="w-full max-w-6xl mx-auto px-4 py-6 space-y-6">
      {/* Celebration Banner - Only show when 100% complete */}
      {isFullyCompleted && (
        <CelebrationBanner
          petName={activePet.name}
          shapeScore={shapeScore}
          streak={streak}
          onShare={() => console.log('Share achievement clicked')}
        />
      )}

      {/* Enhanced Stats Card - Only show if user has engagement */}
      {hasAnyEngagement && (
        <StatsCard
          shapeScore={shapeScore}
          streak={streak}
          completedCategories={completedCategories}
          totalCategories={totalCategories}
          completionRate={completionRate}
          hasAnyEngagement={hasAnyEngagement}
          motivationalMessage={motivationalMessage}
          trend="up"
          showStreakBadge={streak > 0}
        />
      )}

      {/* Enhanced SHAPE Check-In Section */}
      <EnhancedShapeCheckInSection
        shapeRatings={shapeRatings}
        onCardClick={onCardClick}
        petName={activePet.name}
        petInfo={`${activePet.age >= 12 ? `${Math.round(activePet.age / 12 * 10) / 10} years` : `${activePet.age} months`} old ${activePet.breed}`}
        streakCount={streak}
        overallScore={overallScore}
        poopLogs={poopLogs}
        onAddPoopLog={onAddPoopLog}
        hasAnyEngagement={hasAnyEngagement}
        motivationalMessage={motivationalMessage}
        onQuickAction={onQuickAction}
      />

      {/* Supplement Adherence Card - Always show if supplements exist */}
      {supplements.length > 0 && (
        <SupplementAdherenceCard
          supplements={supplements}
          adherenceRate={adherenceRate}
          onToggleSupplement={toggleSupplement}
        />
      )}

      {/* Supplement Recommendations */}
      <SupplementRecommendations
        recommendations={recommendations}
        onStartSupplement={startSupplement}
        onDismiss={dismissRecommendation}
      />

      {/* Main Content Grid - Only show if user has engagement */}
      {hasAnyEngagement && (
        <MainContentGrid>
          <DashboardStats dashboardState={dashboardState} />
          <RecentActivityCard />
          <HealthInsightsCard petName={activePet.name} />
        </MainContentGrid>
      )}
    </div>
  );
};
