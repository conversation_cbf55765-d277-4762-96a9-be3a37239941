import React from "react";
import { SenseRatingCard } from "@/components/dashboard/sense/SenseRatingCard";
import { Moon, Zap, <PERSON>, Heart, Flame } from "lucide-react";
import { StoolIcon } from "@/components/icons/StoolIcon";

// Define the type for sense ratings
type SenseRatings = {
  sleep: number;
  exercise: number;
  nutrition: number;
  stool: number;
  emotional: number;
};

interface SenseCheckInSectionProps {
  senseRatings: SenseRatings;
  onCardClick: (category: keyof SenseRatings, title: string, icon: React.ElementType, description: string) => void;
  petName: string;
  petInfo: string;
  streakCount: number;
  overallScore?: number;
}

export const SenseCheckInSection: React.FC<SenseCheckInSectionProps> = ({
  senseRatings,
  onCardClick,
  petName,
  petInfo,
  streakCount,
  overallScore
}) => {
  const senseCategories = [
    {
      key: 'sleep' as keyof SenseRatings,
      title: "Sleep",
      icon: Moon,
      description: "Quality of rest and sleep patterns",
      color: "bg-teal-700"
    },
    {
      key: 'exercise' as keyof SenseRatings,
      title: "Exercise",
      icon: Zap,
      description: "Activity level and energy",
      color: "bg-teal-700"
    },
    {
      key: 'nutrition' as keyof SenseRatings,
      title: "Nutrition",
      icon: Apple,
      description: "Appetite and food intake",
      color: "bg-teal-700"
    },
    {
      key: 'stool' as keyof SenseRatings,
      title: "Stool",
      icon: StoolIcon,
      description: "Quality and consistency",
      color: "bg-gray-200"
    },
    {
      key: 'emotional' as keyof SenseRatings,
      title: "Emotional",
      icon: Heart,
      description: "Mood and behavior",
      color: "bg-teal-700"
    }
  ];

  const currentDate = new Date().toLocaleDateString('en-US', { 
    weekday: 'long',
    month: 'long',
    day: 'numeric'
  });

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-4">
      {/* Header Section */}
      <div className="flex items-start justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 mb-1">{petName}</h1>
          <p className="text-gray-600 text-sm">{petInfo}</p>
        </div>
        
        {overallScore && (
          <div className="text-right">
            <div className="text-3xl font-bold text-green-600">{Math.round(overallScore * 20)}</div>
            <div className="text-sm text-gray-600">SENSE Score</div>
          </div>
        )}
      </div>

      {/* Check-in Title and Date/Streak */}
      <div className="flex items-center justify-between mb-4">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">Today's Check-in</h2>
          <p className="text-sm text-gray-600">{currentDate}</p>
        </div>
        
        <div className="flex items-center gap-1 text-orange-500">
          <Flame className="h-4 w-4" />
          <span className="text-sm font-semibold">{streakCount} day streak</span>
        </div>
      </div>

      {/* SENSE Categories */}
      <div className="space-y-2">
        {senseCategories.map((category) => (
          <SenseRatingCard
            key={category.key}
            title={category.title}
            icon={category.icon}
            value={senseRatings[category.key]}
            onRate={() => {}} // Not used in the refactored version
            onClick={() => onCardClick(category.key, category.title, category.icon, category.description)}
            description={category.description}
            lastUpdated={new Date()}
            color={category.color}
          />
        ))}
      </div>
    </div>
  );
};
