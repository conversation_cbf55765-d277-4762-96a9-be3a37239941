
import React, { useState } from "react";
import { QuickPoopModal } from "@/components/dashboard/poop/QuickPoopModal";
import { PoopLog } from "@/types/poop";
import { ShapeCheckInHeader } from "./shape/ShapeCheckInHeader";
import { ShapeCategoriesList } from "./shape/ShapeCategoriesList";

// Define the type for shape ratings
type ShapeRatings = {
  sleep: number;
  hydration: number;
  activity: number;
  poop: number;
  eating: number;
};

interface ShapeCheckInSectionProps {
  shapeRatings: ShapeRatings;
  onCardClick: (category: keyof ShapeRatings, title: string, icon: React.ElementType, description: string) => void;
  petName: string;
  petInfo: string;
  streakCount: number;
  overallScore?: number;
  poopLogs: PoopLog[];
  onAddPoopLog: (log: PoopLog) => void;
  hasAnyEngagement: boolean;
  motivationalMessage?: string | null;
}

export const ShapeCheckInSection: React.FC<ShapeCheckInSectionProps> = ({
  shapeRatings,
  onCardClick,
  petName,
  petInfo,
  streakCount,
  overallScore,
  poopLogs,
  onAddPoopLog,
  hasAnyEngagement,
  motivationalMessage
}) => {
  const [poopModalOpen, setPoopModalOpen] = useState(false);

  // Get today's date in YYYY-MM-DD format (local timezone)
  const getTodayDateString = () => {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  const today = getTodayDateString();
  const todayLog = poopLogs.find(log => log.date === today);

  const handlePoopCardClick = () => {
    setPoopModalOpen(true);
  };

  // Calculate completion rate for progress visualization
  const completedCategories = Object.values(shapeRatings).filter(rating => rating > 0).length;
  const totalCategories = Object.keys(shapeRatings).length;
  const completionRate = (completedCategories / totalCategories) * 100;

  return (
    <>
      <div className="bg-white rounded-xl border border-gray-100 shadow-sm">
        {/* Mobile-Optimized Header Section */}
        <ShapeCheckInHeader
          petName={petName}
          petInfo={petInfo}
          streakCount={streakCount}
          overallScore={overallScore}
          completedCategories={completedCategories}
          totalCategories={totalCategories}
          completionRate={completionRate}
          hasAnyEngagement={hasAnyEngagement}
          motivationalMessage={motivationalMessage}
        />

        {/* SHAPE Categories with Improved Mobile Layout */}
        <ShapeCategoriesList
          shapeRatings={shapeRatings}
          onCardClick={onCardClick}
          onPoopCardClick={handlePoopCardClick}
          petName={petName}
        />
      </div>

      {/* Poop Modal - Now consistent with other SHAPE modals */}
      <QuickPoopModal
        open={poopModalOpen}
        onOpenChange={setPoopModalOpen}
        onComplete={onAddPoopLog}
        petName={petName}
        existingLog={todayLog}
        startingMode="choice"
      />
    </>
  );
};
