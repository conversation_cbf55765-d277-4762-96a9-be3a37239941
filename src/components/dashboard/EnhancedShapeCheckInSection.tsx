
import React from "react";
import { PoopLog } from "@/types/poop";
import { EnhancedShapeCheckInHeader } from "./shape/EnhancedShapeCheckInHeader";
import { EnhancedShapeCategoriesList } from "./shape/EnhancedShapeCategoriesList";

// Define the type for shape ratings
type ShapeRatings = {
  sleep: number;
  hydration: number;
  activity: number;
  poop: number;
  eating: number;
};

interface EnhancedShapeCheckInSectionProps {
  shapeRatings: ShapeRatings;
  onCardClick: (category: keyof ShapeRatings, title: string, letter: string, description: string) => void;
  petName: string;
  petInfo: string;
  streakCount: number;
  overallScore?: number;
  poopLogs: PoopLog[];
  onAddPoopLog: (log: PoopLog) => void;
  hasAnyEngagement: boolean;
  motivationalMessage?: string | null;
  onQuickAction?: () => void;
}

export const EnhancedShapeCheckInSection: React.FC<EnhancedShapeCheckInSectionProps> = ({
  shapeRatings,
  onCardClick,
  petName,
  petInfo,
  streakCount,
  overallScore,
  poopLogs,
  onAddPoopLog,
  hasAnyEngagement,
  motivationalMessage,
  onQuickAction
}) => {
  // Calculate completion rate for progress visualization
  const completedCategories = Object.values(shapeRatings).filter(rating => rating > 0).length;
  const totalCategories = Object.keys(shapeRatings).length;
  const completionRate = (completedCategories / totalCategories) * 100;

  const handlePoopCardClick = () => {
    console.log('Poop card clicked, triggering unified modal via onCardClick');
    onCardClick('poop', 'Poop', 'P', 'Bathroom habits and stool quality');
  };

  return (
    <div className="bg-white rounded-xl border border-gray-100 shadow-sm">
      {/* Mobile-Optimized Header Section */}
      <EnhancedShapeCheckInHeader
        petName={petName}
        completedCategories={completedCategories}
        totalCategories={totalCategories}
        hasAnyEngagement={hasAnyEngagement}
        onQuickAction={onQuickAction}
      />

      {/* SHAPE Categories with Improved Mobile Layout */}
      <EnhancedShapeCategoriesList
        shapeRatings={shapeRatings}
        onCardClick={onCardClick}
        onPoopCardClick={handlePoopCardClick}
        petName={petName}
      />
    </div>
  );
};
