
import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Target, Activity, Calendar, TrendingUp, CheckCircle2 } from "lucide-react";

interface DashboardStatsProps {
  dashboardState: {
    weeklyGoal: number;
    completedDays: number;
    currentStreak: number;
    totalLogs: number;
    avgScore: number;
    improvement: number;
  };
}

export const DashboardStats: React.FC<DashboardStatsProps> = ({
  dashboardState
}) => {
  return <>
      {/* Weekly Progress & Goals Card */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Weekly Progress</CardTitle>
          <Target className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-2xl font-bold">{dashboardState.completedDays}/{dashboardState.weeklyGoal}</span>
              <Badge variant="secondary">
                {Math.round(dashboardState.completedDays / dashboardState.weeklyGoal * 100)}%
              </Badge>
            </div>
            <Progress value={dashboardState.completedDays / dashboardState.weeklyGoal * 100} className="h-2" />
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <span>{dashboardState.weeklyGoal - dashboardState.completedDays} more days to goal</span>
              <span>{dashboardState.totalLogs} total logs</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Health Trends Card */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Health Trends</CardTitle>
          <Activity className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-baseline gap-2">
              <span className="text-2xl font-bold">{dashboardState.avgScore}</span>
              <span className="text-sm text-muted-foreground">/5.0</span>
            </div>
            <div className="flex items-center gap-1 text-sm">
              <TrendingUp className="h-3 w-3 text-green-500" />
              <span className="text-green-500">+{dashboardState.improvement}%</span>
              <span className="text-muted-foreground">vs last month</span>
            </div>
            <Progress value={dashboardState.avgScore * 20} className="h-2" />
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <CheckCircle2 className="h-3 w-3 text-green-500" />
              <span>Consistent improvement</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </>;
};
