
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Calendar } from "lucide-react";

interface DashboardHeaderProps {
  petName: string;
  onOpenPoopModal?: () => void;
  onOpenSenseModal?: () => void;
  onOpenShapeModal?: () => void;
}

export const DashboardHeader: React.FC<DashboardHeaderProps> = ({ 
  petName, 
  onOpenPoopModal,
  onOpenSenseModal,
  onOpenShapeModal
}) => {
  
  return (
    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">{petName} Dashboard</h1>
        <p className="text-muted-foreground">Track your pet's health and wellness</p>
      </div>
      <div className="flex items-center gap-3">
        <Button variant="outline" size="sm" className="gap-2">
          <Calendar className="h-4 w-4" />
          Schedule Test
        </Button>
      </div>
    </div>
  );
};
