
import React from "react";
import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { TrendingUp, TrendingDown, Minus, ArrowRight } from "lucide-react";
import { useNavigate } from "react-router-dom";

interface HealthInsightsCardProps {
  petName: string;
}

export const HealthInsightsCard: React.FC<HealthInsightsCardProps> = ({ petName }) => {
  const navigate = useNavigate();

  const insights = [
    {
      title: "Digestive Health",
      value: 85,
      change: +8,
      trend: "up",
      description: "Great improvement this week"
    },
    {
      title: "Energy Levels",
      value: 78,
      change: -2,
      trend: "down",
      description: "Slightly lower than last week"
    },
    {
      title: "Appetite",
      value: 92,
      change: 0,
      trend: "stable",
      description: "Consistently excellent"
    }
  ];

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case "up":
        return <TrendingUp className="h-4 w-4 text-green-500" />;
      case "down":
        return <TrendingDown className="h-4 w-4 text-red-500" />;
      default:
        return <Minus className="h-4 w-4 text-gray-500" />;
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case "up":
        return "text-green-500";
      case "down":
        return "text-red-500";
      default:
        return "text-gray-500";
    }
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg">Health Insights</CardTitle>
            <CardDescription>{petName}'s key health metrics</CardDescription>
          </div>
          <Button 
            variant="ghost" 
            size="sm"
            onClick={() => navigate('/coach')}
            className="text-brand-primary hover:text-brand-dark"
          >
            View Details
            <ArrowRight className="ml-1 h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {insights.map((insight, index) => (
          <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                <h4 className="font-medium text-sm">{insight.title}</h4>
                <Badge variant="secondary" className="text-xs">
                  {insight.value}%
                </Badge>
              </div>
              <p className="text-xs text-muted-foreground">{insight.description}</p>
            </div>
            <div className="flex items-center gap-1">
              {getTrendIcon(insight.trend)}
              <span className={`text-sm font-medium ${getTrendColor(insight.trend)}`}>
                {insight.change !== 0 ? (insight.change > 0 ? `+${insight.change}` : insight.change) : "0"}
              </span>
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  );
};
