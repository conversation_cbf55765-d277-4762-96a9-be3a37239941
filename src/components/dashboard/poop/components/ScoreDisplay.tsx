
import React from "react";
import { cn } from "@/lib/utils";

interface ScoreDisplayProps {
  score: number;
  label: string;
}

export const ScoreDisplay: React.FC<ScoreDisplayProps> = ({ score, label }) => {
  const getScoreGradient = (score: number) => {
    if (score >= 80) return "from-green-400 to-green-600";
    if (score >= 60) return "from-yellow-400 to-yellow-600";
    return "from-red-400 to-red-600";
  };

  return (
    <div className="text-center">
      <div className={cn(
        "w-20 h-20 mx-auto rounded-full flex items-center justify-center text-2xl font-bold text-white mb-2",
        `bg-gradient-to-br ${getScoreGradient(score)}`
      )}>
        {score}
      </div>
      <div className="text-sm text-gray-600">{label}</div>
    </div>
  );
};
