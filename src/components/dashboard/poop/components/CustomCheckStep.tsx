
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { AlertTriangle } from "lucide-react";

interface CustomCheckStepProps {
  onCustomCheck: () => void;
}

export const CustomCheckStep: React.FC<CustomCheckStepProps> = ({
  onCustomCheck
}) => {
  return (
    <Card 
      className="cursor-pointer transition-all hover:shadow-md border-2 border-amber-300 bg-gradient-to-r from-amber-50 to-orange-50"
      onClick={onCustomCheck}
    >
      <CardContent className="p-6 text-center">
        <AlertTriangle className="h-12 w-12 mx-auto mb-3 text-amber-600" />
        <div className="text-xl font-bold text-amber-700 mb-2">Something's Different</div>
        <div className="text-sm text-amber-600">Quick 2-step check for any issues</div>
      </CardContent>
    </Card>
  );
};
