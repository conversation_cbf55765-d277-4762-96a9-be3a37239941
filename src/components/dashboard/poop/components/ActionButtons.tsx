
import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { CheckCircle, AlertTriangle } from "lucide-react";
import { PoopLog } from "@/types/poop";
import confetti from "canvas-confetti";

interface ActionButtonsProps {
  onAddLog: (log: PoopLog) => void;
  petName: string;
  onSomethingOff: () => void;
}

export const ActionButtons: React.FC<ActionButtonsProps> = ({
  onAddLog,
  petName,
  onSomethingOff
}) => {
  const handleQuickNormal = () => {
    const normalLog: PoopLog = {
      id: `log-${Date.now()}`,
      date: new Date().toISOString().split('T')[0],
      dogName: petName,
      type: 4, // Perfect normal
      easeOfPickup: 'easy',
      color: 'brown',
      odor: 'normal',
      frequency: true,
      straining: 'none',
      score: 90,
      feedback: "Perfect! Everything looks healthy and normal today! 🎉",
      completed: true
    };

    // Celebration
    confetti({
      particleCount: 60,
      spread: 70,
      origin: { y: 0.6 }
    });

    onAddLog(normalLog);
  };

  return (
    <div className="space-y-3">
      <Button 
        onClick={handleQuickNormal}
        className="w-full bg-brand-primary hover:bg-brand-dark text-white font-medium"
        size="lg"
      >
        <CheckCircle className="h-5 w-5 mr-2" />
        Everything Normal Today!
      </Button>
      
      <Button 
        variant="outline"
        onClick={onSomethingOff}
        className="w-full border-brand-primary text-brand-primary hover:bg-brand-light"
      >
        <AlertTriangle className="h-4 w-4 mr-2" />
        Something's Off
      </Button>
    </div>
  );
};
