
import React from "react";
import { Button } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import { cn } from "@/lib/utils";
import { PoopLog } from "@/types/poop";

interface TodayScoreDisplayProps {
  todayLog: PoopLog;
  onUpdateLog: () => void;
}

export const TodayScoreDisplay: React.FC<TodayScoreDisplayProps> = ({
  todayLog,
  onUpdateLog
}) => {
  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600";
    if (score >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  return (
    <div className="space-y-4">
      {/* Today's Score Display */}
      <div className="text-center bg-white rounded-lg p-4 border border-brand-light shadow-sm">
        <div className="flex items-center justify-center gap-2 mb-2">
          <span className={cn("text-3xl font-bold", getScoreColor(todayLog.score))}>
            {todayLog.score}
          </span>
          <span className="text-sm text-muted-foreground">/100</span>
        </div>
        <div className="text-sm text-brand-primary font-medium mb-2">Today's Score</div>
        <div className="text-xs text-muted-foreground">{todayLog.feedback}</div>
      </div>

      <div className="flex justify-between items-center">
        <Button 
          variant="outline" 
          size="sm" 
          className="border-brand-primary text-brand-primary hover:bg-brand-light"
          onClick={onUpdateLog}
        >
          Update Log
        </Button>
        <Button 
          variant="link" 
          size="sm" 
          className="text-muted-foreground hover:text-brand-primary"
        >
          View History
          <ArrowRight className="h-4 w-4 ml-1" />
        </Button>
      </div>
    </div>
  );
};
