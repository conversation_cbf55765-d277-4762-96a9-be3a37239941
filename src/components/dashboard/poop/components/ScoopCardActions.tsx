
import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Zap, Target } from "lucide-react";
import { StoolIcon } from "@/components/icons/StoolIcon";

interface ScoopCardActionsProps {
  hasLog: boolean;
  petName: string;
  streakCount: number;
  onCheckIn: () => void;
  onUpdate: () => void;
}

export const ScoopCardActions: React.FC<ScoopCardActionsProps> = ({
  hasLog,
  petName,
  streakCount,
  onCheckIn,
  onUpdate
}) => {
  if (hasLog) {
    return (
      <div className="space-y-2">
        <Button 
          variant="outline" 
          size="sm" 
          className="w-full border-amber-200 hover:bg-amber-50"
          onClick={onUpdate}
        >
          Update Today's Log
        </Button>
        
        <Button variant="ghost" size="sm" className="w-full text-gray-600">
          <Target className="h-4 w-4 mr-2" />
          View History & Trends
        </Button>
      </div>
    );
  }

  return (
    <div className="text-center space-y-4">
      {/* Engaging Call to Action */}
      <div className="text-gray-500">
        <div className="w-16 h-16 mx-auto mb-3 bg-gradient-to-br from-amber-100 to-orange-100 rounded-full flex items-center justify-center">
          <StoolIcon className="h-8 w-8 text-amber-500" />
        </div>
        <p className="text-sm mb-2">Track {petName}'s digestive health</p>
        <p className="text-xs text-gray-400">Quick & easy daily check-in</p>
      </div>
      
      {/* Primary CTA with Enthusiasm */}
      <Button 
        onClick={onCheckIn}
        className="w-full bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-white font-medium"
      >
        <Zap className="h-4 w-4 mr-2" />
        Quick Check-In
      </Button>

      {/* Streak Motivation */}
      {streakCount > 0 && (
        <div className="text-xs text-orange-600 font-medium">
          Keep your {streakCount} day streak going! 🎯
        </div>
      )}
    </div>
  );
};
