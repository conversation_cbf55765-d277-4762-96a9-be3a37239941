
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { TrendingUp, Camera } from "lucide-react";

interface ScoreSummaryProps {
  score: number;
  feedback: string;
  petName: string;
  onComplete: () => void;
}

export const ScoreSummary: React.FC<ScoreSummaryProps> = ({
  score,
  feedback,
  petName,
  onComplete
}) => {
  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600";
    if (score >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  const getScoreBackground = (score: number) => {
    if (score >= 80) return "bg-green-100 border-green-300";
    if (score >= 60) return "bg-yellow-100 border-yellow-300";
    return "bg-red-100 border-red-300";
  };

  return (
    <div className="space-y-4 text-center">
      <Card className={cn("border-2", getScoreBackground(score))}>
        <CardContent className="p-6">
          <div className={cn("text-6xl font-bold mb-2", getScoreColor(score))}>
            {score}
          </div>
          <div className="text-lg font-medium text-gray-900 mb-3">
            {petName}'s Digestive Score
          </div>
          <div className="text-sm text-gray-700 bg-white/50 rounded-lg p-3">
            {feedback}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="grid grid-cols-2 gap-3">
        <Button variant="outline" size="sm" className="flex items-center gap-2">
          <TrendingUp className="h-4 w-4" />
          View Trends
        </Button>
        <Button variant="outline" size="sm" className="flex items-center gap-2">
          <Camera className="h-4 w-4" />
          Add Photo
        </Button>
      </div>

      <Button 
        onClick={onComplete}
        className="w-full bg-green-600 hover:bg-green-700 text-white"
      >
        Complete Check-In ✨
      </Button>
    </div>
  );
};
