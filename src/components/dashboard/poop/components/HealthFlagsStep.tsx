
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface HealthFlagsStepProps {
  formData: {
    color?: string;
    straining?: string;
    odor?: string;
    frequency?: boolean;
  };
  onUpdate: (field: string, value: any) => void;
  onContinue: () => void;
}

export const HealthFlagsStep: React.FC<HealthFlagsStepProps> = ({
  formData,
  onUpdate,
  onContinue
}) => {
  return (
    <div className="space-y-4">
      <div className="text-center text-sm text-gray-600 mb-4">
        Any of these concerning signs?
      </div>
      
      <div className="grid grid-cols-2 gap-3">
        {/* Color Issues */}
        <Card 
          className={cn(
            "cursor-pointer transition-all",
            formData.color !== 'brown' ? "ring-2 ring-red-500 bg-red-50" : "hover:bg-gray-50"
          )}
          onClick={() => onUpdate('color', formData.color === 'brown' ? 'red' : 'brown')}
        >
          <CardContent className="p-3 text-center">
            <div className="text-2xl">🔴</div>
            <div className="text-sm">Red/Black Color</div>
          </CardContent>
        </Card>

        {/* Straining */}
        <Card 
          className={cn(
            "cursor-pointer transition-all",
            formData.straining !== 'none' ? "ring-2 ring-yellow-500 bg-yellow-50" : "hover:bg-gray-50"
          )}
          onClick={() => onUpdate('straining', formData.straining === 'none' ? 'a-lot' : 'none')}
        >
          <CardContent className="p-3 text-center">
            <div className="text-2xl">😣</div>
            <div className="text-sm">Straining</div>
          </CardContent>
        </Card>

        {/* Strong Odor */}
        <Card 
          className={cn(
            "cursor-pointer transition-all",
            formData.odor !== 'normal' ? "ring-2 ring-purple-500 bg-purple-50" : "hover:bg-gray-50"
          )}
          onClick={() => onUpdate('odor', formData.odor === 'normal' ? 'strong' : 'normal')}
        >
          <CardContent className="p-3 text-center">
            <div className="text-2xl">👃</div>
            <div className="text-sm">Strong Odor</div>
          </CardContent>
        </Card>

        {/* No Poop */}
        <Card 
          className={cn(
            "cursor-pointer transition-all",
            !formData.frequency ? "ring-2 ring-orange-500 bg-orange-50" : "hover:bg-gray-50"
          )}
          onClick={() => onUpdate('frequency', !formData.frequency)}
        >
          <CardContent className="p-3 text-center">
            <div className="text-2xl">❌</div>
            <div className="text-sm">No Poop Today</div>
          </CardContent>
        </Card>
      </div>

      <Button 
        onClick={onContinue} 
        className="w-full mt-4"
      >
        Continue to Score
      </Button>
    </div>
  );
};
