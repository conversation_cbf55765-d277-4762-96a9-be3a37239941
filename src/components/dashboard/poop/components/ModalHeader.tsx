
import React from "react";
import { DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft } from "lucide-react";
import { cn } from "@/lib/utils";

interface ModalHeaderProps {
  mode: 'choice' | 'type' | 'flags';
  startingMode: 'choice' | 'type';
  petName: string;
  onBackClick: () => void;
  scorePreview?: number;
}

export const ModalHeader: React.FC<ModalHeaderProps> = ({
  mode,
  startingMode,
  petName,
  onBackClick,
  scorePreview
}) => {
  const getModalTitle = () => {
    switch (mode) {
      case 'choice':
        return `${petName}'s Check-In`;
      case 'type':
        return "What did it look like?";
      case 'flags':
        return "Any concerning signs?";
      default:
        return `${petName}'s Check-In`;
    }
  };

  const showBackButton = mode !== 'choice' && !(mode === 'type' && startingMode === 'type');

  return (
    <DialogHeader>
      <div className="flex items-center gap-2">
        {showBackButton && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onBackClick}
            className="p-1"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
        )}
        <DialogTitle className="text-center flex-1">
          {getModalTitle()}
        </DialogTitle>
      </div>
      
      {/* Live Score Preview */}
      {mode === 'flags' && scorePreview !== undefined && (
        <div className="flex justify-center">
          <Badge className={cn(
            "text-lg font-bold px-3 py-1",
            scorePreview >= 80 ? "bg-green-100 border-green-300" :
            scorePreview >= 60 ? "bg-yellow-100 border-yellow-300" :
            "bg-red-100 border-red-300"
          )}>
            <span className={cn(
              scorePreview >= 80 ? "text-green-600" :
              scorePreview >= 60 ? "text-yellow-600" :
              "text-red-600"
            )}>
              Score: {scorePreview}
            </span>
          </Badge>
        </div>
      )}
    </DialogHeader>
  );
};
