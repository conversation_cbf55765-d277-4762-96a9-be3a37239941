
import React from "react";
import { Badge } from "@/components/ui/badge";
import { AlertTriangle, CheckCircle } from "lucide-react";
import { cn } from "@/lib/utils";

interface WeeklyScoreDisplayProps {
  weeklyAverage: number;
  todayLog?: any;
}

export const WeeklyScoreDisplay: React.FC<WeeklyScoreDisplayProps> = ({
  weeklyAverage,
  todayLog
}) => {
  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600";
    if (score >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  const getScoreStatus = (score: number) => {
    if (score >= 80) return { status: "excellent", color: "bg-green-100 text-green-800 border-green-200", showAlert: false };
    if (score >= 60) return { status: "good", color: "bg-yellow-100 text-yellow-800 border-yellow-200", showAlert: false };
    return { status: "needs attention", color: "bg-red-100 text-red-800 border-red-200", showAlert: true };
  };

  return (
    <div className="flex items-center gap-3">
      {/* Score Display with Alert Icon */}
      <div className="text-right">
        <div className="flex items-center justify-end gap-2">
          <div className={cn("text-2xl font-bold", getScoreColor(weeklyAverage))}>
            {weeklyAverage}
          </div>
          {getScoreStatus(weeklyAverage).showAlert && (
            <AlertTriangle className="h-5 w-5 text-red-500" />
          )}
        </div>
        <div className="text-xs text-muted-foreground">Weekly Score</div>
      </div>
      
      {todayLog && (
        <Badge className="bg-green-100 text-green-800 border-green-200" variant="outline">
          <CheckCircle className="h-3 w-3 mr-1" />
          Logged
        </Badge>
      )}
    </div>
  );
};
