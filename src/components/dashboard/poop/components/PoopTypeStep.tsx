
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { POOP_TYPES } from "@/types/poop";

interface PoopTypeStepProps {
  onQuickNormal: () => void;
  onTypeSelect: (type: number, ease: 'easy' | 'sticky' | 'mushy') => void;
}

export const PoopTypeStep: React.FC<PoopTypeStepProps> = ({
  onQuickNormal,
  onTypeSelect
}) => {
  return (
    <div className="space-y-4">
      {/* Quick Normal Option */}
      <Card 
        className="cursor-pointer transition-all hover:shadow-md border-2 border-green-300 bg-green-50"
        onClick={onQuickNormal}
      >
        <CardContent className="p-4 text-center">
          <div className="text-3xl mb-2">✨</div>
          <div className="font-bold text-green-700 mb-1">Everything Looks Normal!</div>
          <div className="text-sm text-green-600">Quick log - firm, brown, easy pickup</div>
        </CardContent>
      </Card>

      <div className="text-center text-sm text-gray-500 my-2">or select specific type:</div>

      {/* Simplified Type Selection with Pickup Ease */}
      <div className="grid grid-cols-1 gap-2 max-h-64 overflow-y-auto">
        {POOP_TYPES.map((type) => {
          return (
            <div key={type.id} className="space-y-1">
              <div className="text-xs font-medium text-gray-600">
                Type {type.id}: {type.name}
              </div>
              <div className="grid grid-cols-3 gap-1">
                {(['easy', 'sticky', 'mushy'] as const).map((ease) => (
                  <Card 
                    key={`${type.id}-${ease}`}
                    className="cursor-pointer transition-all hover:shadow-sm"
                    onClick={() => onTypeSelect(type.id, ease)}
                  >
                    <CardContent className="p-2 text-center">
                      <div className="flex justify-center mb-1">
                        <img 
                          src={type.imageUrl} 
                          alt={type.name}
                          className="w-5 h-5 object-contain"
                        />
                      </div>
                      <div className="text-xs capitalize">{ease}</div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};
