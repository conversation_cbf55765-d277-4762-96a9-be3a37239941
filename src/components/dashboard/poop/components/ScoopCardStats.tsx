
import React from "react";

interface ScoopCardStatsProps {
  weeklyAverage: number;
  totalLogs: number;
}

export const ScoopCardStats: React.FC<ScoopCardStatsProps> = ({
  weeklyAverage,
  totalLogs
}) => {
  return (
    <div className="grid grid-cols-2 gap-3">
      <div className="text-center p-2 bg-white/50 rounded-lg">
        <div className="text-lg font-bold text-blue-600">{weeklyAverage}</div>
        <div className="text-xs text-gray-600">7-day avg</div>
      </div>
      <div className="text-center p-2 bg-white/50 rounded-lg">
        <div className="text-lg font-bold text-purple-600">#{totalLogs}</div>
        <div className="text-xs text-gray-600">Total logs</div>
      </div>
    </div>
  );
};
