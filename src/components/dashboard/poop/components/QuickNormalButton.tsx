
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { CheckCircle } from "lucide-react";
import confetti from "canvas-confetti";
import { PoopLog } from "@/types/poop";

interface QuickNormalButtonProps {
  petName: string;
  onComplete: (log: PoopLog) => void;
  onClose: () => void;
  existingLog?: PoopLog;
}

export const QuickNormalButton: React.FC<QuickNormalButtonProps> = ({
  petName,
  onComplete,
  onClose,
  existingLog
}) => {
  // Get today's date in YYYY-MM-DD format (local timezone)
  const getTodayDateString = () => {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  const handleQuickNormal = () => {
    const normalLog: PoopLog = {
      id: existingLog?.id || `log-${Date.now()}`,
      date: getTodayDateString(),
      dogName: petName,
      type: 4, // Perfect normal
      easeOfPickup: 'easy',
      color: 'brown',
      odor: 'normal',
      frequency: true,
      straining: 'none',
      score: 90,
      feedback: "Perfect! Everything looks healthy and normal today! 🎉",
      completed: true
    };

    console.log('Creating normal poop log for date:', normalLog.date);

    // Celebration
    confetti({
      particleCount: 60,
      spread: 70,
      origin: { y: 0.6 }
    });

    onComplete(normalLog);
    onClose();
  };

  return (
    <Card 
      className="cursor-pointer transition-all hover:shadow-lg border-2 border-green-300 bg-gradient-to-r from-green-50 to-green-100"
      onClick={handleQuickNormal}
    >
      <CardContent className="p-6 text-center">
        <CheckCircle className="h-12 w-12 mx-auto mb-3 text-green-600" />
        <div className="text-xl font-bold text-green-700 mb-2">Everything Normal!</div>
        <div className="text-sm text-green-600">Firm, brown, easy cleanup - all good!</div>
      </CardContent>
    </Card>
  );
};
