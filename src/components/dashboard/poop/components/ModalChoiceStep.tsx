
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { CheckCircle, AlertTriangle } from "lucide-react";
import { QuickNormalButton } from "./QuickNormalButton";
import { CustomCheckStep } from "./CustomCheckStep";
import { ModalSeparator } from "./ModalSeparator";
import { PoopLog } from "@/types/poop";

interface ModalChoiceStepProps {
  onQuickNormal: () => void;
  onCustomCheck: () => void;
  petName?: string;
  onComplete?: (log: PoopLog) => void;
  onClose?: () => void;
  existingLog?: PoopLog;
}

export const ModalChoiceStep: React.FC<ModalChoiceStepProps> = ({
  onQuickNormal,
  onCustomCheck,
  petName,
  onComplete,
  onClose,
  existingLog
}) => {
  // If we have the new props, use the new components
  if (petName && onComplete && onClose) {
    return (
      <div className="space-y-4">
        <QuickNormalButton
          petName={petName}
          onComplete={onComplete}
          onClose={onClose}
          existingLog={existingLog}
        />
        <ModalSeparator />
        <CustomCheckStep onCustomCheck={onCustomCheck} />
      </div>
    );
  }

  // Keep existing fallback for backward compatibility
  return (
    <div className="space-y-4">
      <Card 
        className="cursor-pointer transition-all hover:shadow-lg border-2 border-green-300 bg-gradient-to-r from-green-50 to-green-100"
        onClick={onQuickNormal}
      >
        <CardContent className="p-6 text-center">
          <CheckCircle className="h-12 w-12 mx-auto mb-3 text-green-600" />
          <div className="text-xl font-bold text-green-700 mb-2">Everything Normal!</div>
          <div className="text-sm text-green-600">Firm, brown, easy cleanup - all good!</div>
        </CardContent>
      </Card>

      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t border-gray-200" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-white px-2 text-gray-500">or</span>
        </div>
      </div>

      <Card 
        className="cursor-pointer transition-all hover:shadow-md border-2 border-amber-300 bg-gradient-to-r from-amber-50 to-orange-50"
        onClick={onCustomCheck}
      >
        <CardContent className="p-6 text-center">
          <AlertTriangle className="h-12 w-12 mx-auto mb-3 text-amber-600" />
          <div className="text-xl font-bold text-amber-700 mb-2">Something's Different</div>
          <div className="text-sm text-amber-600">Quick 2-step check for any issues</div>
        </CardContent>
      </Card>
    </div>
  );
};
