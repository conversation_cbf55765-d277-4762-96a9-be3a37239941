
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface ModalFlagsStepProps {
  flags: {
    colorIssue: boolean;
    straining: boolean;
    strongOdor: boolean;
    noPoop: boolean;
  };
  onFlagToggle: (flag: keyof ModalFlagsStepProps['flags']) => void;
  onComplete: () => void;
}

export const ModalFlagsStep: React.FC<ModalFlagsStepProps> = ({
  flags,
  onFlagToggle,
  onComplete
}) => {
  return (
    <div className="space-y-4">
      <div className="text-center text-sm text-gray-600 mb-4">
        Tap any concerning signs (or skip if all normal)
      </div>
      
      <div className="grid grid-cols-2 gap-3">
        <Card 
          className={cn(
            "cursor-pointer transition-all",
            flags.colorIssue ? "ring-2 ring-red-500 bg-red-50" : "hover:bg-gray-50"
          )}
          onClick={() => onFlagToggle('colorIssue')}
        >
          <CardContent className="p-4 text-center">
            <div className="text-3xl mb-2">🔴</div>
            <div className="text-sm font-medium">Red/Black Color</div>
          </CardContent>
        </Card>

        <Card 
          className={cn(
            "cursor-pointer transition-all",
            flags.straining ? "ring-2 ring-yellow-500 bg-yellow-50" : "hover:bg-gray-50"
          )}
          onClick={() => onFlagToggle('straining')}
        >
          <CardContent className="p-4 text-center">
            <div className="text-3xl mb-2">😣</div>
            <div className="text-sm font-medium">Straining</div>
          </CardContent>
        </Card>

        <Card 
          className={cn(
            "cursor-pointer transition-all",
            flags.strongOdor ? "ring-2 ring-purple-500 bg-purple-50" : "hover:bg-gray-50"
          )}
          onClick={() => onFlagToggle('strongOdor')}
        >
          <CardContent className="p-4 text-center">
            <div className="text-3xl mb-2">👃</div>
            <div className="text-sm font-medium">Strong Odor</div>
          </CardContent>
        </Card>

        <Card 
          className={cn(
            "cursor-pointer transition-all",
            flags.noPoop ? "ring-2 ring-orange-500 bg-orange-50" : "hover:bg-gray-50"
          )}
          onClick={() => onFlagToggle('noPoop')}
        >
          <CardContent className="p-4 text-center">
            <div className="text-3xl mb-2">❌</div>
            <div className="text-sm font-medium">No Poop Today</div>
          </CardContent>
        </Card>
      </div>

      <Button 
        onClick={onComplete}
        className="w-full mt-6 bg-green-600 hover:bg-green-700 text-white"
        size="lg"
      >
        Complete Check-In ✨
      </Button>
    </div>
  );
};
