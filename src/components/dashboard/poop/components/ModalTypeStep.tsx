
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { POOP_TYPES } from "@/types/poop";
import { cn } from "@/lib/utils";

interface ModalTypeStepProps {
  selectedType: number | null;
  petName: string;
  onTypeSelect: (type: number) => void;
}

export const ModalTypeStep: React.FC<ModalTypeStepProps> = ({
  selectedType,
  petName,
  onTypeSelect
}) => {
  return (
    <div className="space-y-4">
      <div className="text-center text-sm text-gray-600 mb-4">
        What did {petName}'s poop look like today?
      </div>
      
      <div className="grid grid-cols-1 gap-3">
        {POOP_TYPES.map((type) => {
          return (
            <Card 
              key={type.id}
              className={cn(
                "cursor-pointer transition-all hover:shadow-md",
                selectedType === type.id && "ring-2 ring-blue-500 bg-blue-50"
              )}
              onClick={() => onTypeSelect(type.id)}
            >
              <CardContent className="p-4 flex items-center gap-4">
                <div className="w-13 h-13 flex items-center justify-center">
                  <img 
                    src={type.imageUrl} 
                    alt={type.name}
                    className="w-10 h-10 object-contain"
                  />
                </div>
                <div className="flex-1">
                  <div className="font-medium">Type {type.id}: {type.name}</div>
                  <div className="text-sm text-gray-600">{type.description}</div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
};
