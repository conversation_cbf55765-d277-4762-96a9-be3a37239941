
import React from "react";
import { AlertTriangle } from "lucide-react";

interface AlertsListProps {
  alerts: string[];
}

export const AlertsList: React.FC<AlertsListProps> = ({ alerts }) => {
  if (alerts.length === 0) return null;

  return (
    <div className="space-y-2">
      {alerts.map((alert, index) => (
        <div key={index} className="flex items-center gap-2 text-sm text-red-700 bg-red-50 border border-red-200 p-3 rounded-lg">
          <AlertTriangle className="h-4 w-4 flex-shrink-0" />
          <span>{alert}</span>
        </div>
      ))}
    </div>
  );
};
