
import React from "react";
import { cn } from "@/lib/utils";
import { PoopLog } from "@/types/poop";

interface WeeklyProgressDotsProps {
  poopLogs: PoopLog[];
}

export const WeeklyProgressDots: React.FC<WeeklyProgressDotsProps> = ({
  poopLogs
}) => {
  const getWeeklyDots = (logs: PoopLog[]): Array<{date: string, status: 'excellent' | 'good' | 'poor' | 'none', dayLetter: string}> => {
    const dots = [];
    const today = new Date();
    
    for (let i = 6; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      const dateString = date.toDateString();
      
      const log = logs.find(l => new Date(l.date).toDateString() === dateString);
      
      let status: 'excellent' | 'good' | 'poor' | 'none' = 'none';
      if (log) {
        if (log.score >= 80) status = 'excellent';
        else if (log.score >= 60) status = 'good';
        else status = 'poor';
      }
      
      const dayLetter = date.toLocaleDateString('en-US', { weekday: 'short' }).charAt(0);
      
      dots.push({
        date: date.toLocaleDateString('en-US', { weekday: 'short' }),
        status,
        dayLetter
      });
    }
    
    return dots;
  };

  const weeklyDots = getWeeklyDots(poopLogs);

  return (
    <div className="space-y-3">
      <div className="flex flex-col items-center">
        <div className="flex items-center justify-between w-full mb-2">
          {weeklyDots.map((dot, index) => (
            <div key={index} className="flex flex-col items-center gap-1">
              <div
                className={cn(
                  "w-8 h-8 rounded-full border-2 flex items-center justify-center text-xs font-bold transition-all",
                  dot.status === 'excellent' && "bg-green-500 border-green-500 text-white",
                  dot.status === 'good' && "bg-yellow-500 border-yellow-500 text-white",
                  dot.status === 'poor' && "bg-red-500 border-red-500 text-white",
                  dot.status === 'none' && "bg-gray-100 border-gray-300 text-gray-400"
                )}
                title={dot.date}
              >
                {dot.status !== 'none' ? '✓' : ''}
              </div>
              <span className="text-xs text-muted-foreground font-medium">
                {dot.dayLetter}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
