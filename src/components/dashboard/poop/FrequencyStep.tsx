
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { PoopLog } from "@/types/poop";

interface FrequencyStepProps {
  frequency?: boolean;
  straining?: PoopLog['straining'];
  onUpdate: (data: Partial<PoopLog>) => void;
}

export const FrequencyStep: React.FC<FrequencyStepProps> = ({
  frequency,
  straining,
  onUpdate
}) => {
  const strainingOptions = [
    { value: 'none' as const, label: 'No straining', icon: '😊', description: 'Normal, comfortable' },
    { value: 'mild' as const, label: 'Mild straining', icon: '😐', description: 'Some effort required' },
    { value: 'a-lot' as const, label: 'A lot of straining', icon: '😰', description: 'Significant difficulty' }
  ];

  return (
    <div className="space-y-6">
      {/* Frequency Question */}
      <div>
        <h3 className="font-medium text-gray-900 mb-3">Did they poop today?</h3>
        <div className="grid grid-cols-2 gap-3">
          <Card 
            className={cn(
              "cursor-pointer transition-all hover:shadow-md",
              frequency === true 
                ? "ring-2 ring-green-500 bg-green-50" 
                : "hover:bg-gray-50"
            )}
            onClick={() => onUpdate({ frequency: true })}
          >
            <CardContent className="p-4 text-center">
              <div className="text-2xl mb-2">✅</div>
              <div className="font-medium">Yes</div>
            </CardContent>
          </Card>
          
          <Card 
            className={cn(
              "cursor-pointer transition-all hover:shadow-md",
              frequency === false 
                ? "ring-2 ring-red-500 bg-red-50" 
                : "hover:bg-gray-50"
            )}
            onClick={() => onUpdate({ frequency: false })}
          >
            <CardContent className="p-4 text-center">
              <div className="text-2xl mb-2">❌</div>
              <div className="font-medium">No</div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Straining Question - only show if they pooped */}
      {frequency === true && (
        <div>
          <h3 className="font-medium text-gray-900 mb-3">Any straining or discomfort?</h3>
          <div className="space-y-2">
            {strainingOptions.map((option) => (
              <Card 
                key={option.value}
                className={cn(
                  "cursor-pointer transition-all hover:shadow-md",
                  straining === option.value 
                    ? "ring-2 ring-amber-500 bg-amber-50" 
                    : "hover:bg-gray-50"
                )}
                onClick={() => onUpdate({ straining: option.value })}
              >
                <CardContent className="p-3">
                  <div className="flex items-center gap-3">
                    <div className="text-2xl">{option.icon}</div>
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">{option.label}</div>
                      <div className="text-sm text-gray-600">{option.description}</div>
                    </div>
                    {straining === option.value && (
                      <div className="w-6 h-6 rounded-full bg-amber-500 flex items-center justify-center">
                        <span className="text-white text-sm">✓</span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* No poop message */}
      {frequency === false && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center gap-2 text-yellow-800">
            <span className="text-lg">⚠️</span>
            <div>
              <div className="font-medium">No bowel movement today</div>
              <div className="text-sm">This will be noted. Consider increasing water intake and monitoring closely.</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
