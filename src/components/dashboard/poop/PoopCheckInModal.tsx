
import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { PoopLog, POOP_TYPES } from "@/types/poop";
import { calculatePoopScore } from "@/utils/poopScoring";
import { PoopTypeSelector } from "./PoopTypeSelector";
import { PickupEaseStep } from "./PickupEaseStep";
import { ColorOdorStep } from "./ColorOdorStep";
import { FrequencyStep } from "./FrequencyStep";
import { PhotoUploadStep } from "./PhotoUploadStep";
import { ScoreSummaryStep } from "./ScoreSummaryStep";
import { Progress } from "@/components/ui/progress";

interface PoopCheckInModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onComplete: (log: PoopLog) => void;
  petName: string;
  existingLog?: <PERSON><PERSON><PERSON><PERSON>;
}

export const PoopCheckInModal: React.FC<PoopCheckInModalProps> = ({
  open,
  onOpenChange,
  onComplete,
  petName,
  existingLog
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<Partial<PoopLog>>({
    date: new Date().toISOString().split('T')[0],
    dogName: petName,
    type: existingLog?.type,
    easeOfPickup: existingLog?.easeOfPickup,
    color: existingLog?.color || 'brown',
    odor: existingLog?.odor || 'normal',
    frequency: existingLog?.frequency ?? true,
    straining: existingLog?.straining || 'none',
    photoUrl: existingLog?.photoUrl,
    completed: false
  });

  const totalSteps = 6;
  const progress = (currentStep / totalSteps) * 100;

  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleComplete = () => {
    const { score, feedback } = calculatePoopScore(formData);
    
    const log: PoopLog = {
      id: existingLog?.id || `log-${Date.now()}`,
      date: formData.date!,
      dogName: formData.dogName!,
      type: formData.type!,
      easeOfPickup: formData.easeOfPickup!,
      color: formData.color!,
      odor: formData.odor!,
      frequency: formData.frequency!,
      straining: formData.straining!,
      photoUrl: formData.photoUrl,
      score,
      feedback,
      completed: true
    };

    onComplete(log);
    onOpenChange(false);
    setCurrentStep(1);
  };

  const updateFormData = (data: Partial<PoopLog>) => {
    setFormData(prev => ({ ...prev, ...data }));
  };

  const getStepTitle = () => {
    switch (currentStep) {
      case 1: return "What did your dog's poop look like today?";
      case 2: return "Was it easy to pick up?";
      case 3: return "Color & Odor Check";
      case 4: return "Frequency & Comfort";
      case 5: return "Optional Photo";
      case 6: return "Digestive Score Summary";
      default: return "";
    }
  };

  const canProceed = () => {
    switch (currentStep) {
      case 1: return formData.type !== undefined;
      case 2: return formData.easeOfPickup !== undefined;
      case 3: return formData.color && formData.odor;
      case 4: return formData.frequency !== undefined && formData.straining;
      case 5: return true; // Photo is optional
      case 6: return true;
      default: return false;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-center">
            {getStepTitle()}
          </DialogTitle>
          <div className="space-y-2">
            <Progress value={progress} className="w-full" />
            <div className="text-center text-sm text-gray-500">
              Step {currentStep} of {totalSteps}
            </div>
          </div>
        </DialogHeader>

        <div className="py-4">
          {currentStep === 1 && (
            <PoopTypeSelector
              selectedType={formData.type}
              onSelect={(type) => updateFormData({ type })}
            />
          )}
          
          {currentStep === 2 && (
            <PickupEaseStep
              selected={formData.easeOfPickup}
              onSelect={(easeOfPickup) => updateFormData({ easeOfPickup })}
            />
          )}
          
          {currentStep === 3 && (
            <ColorOdorStep
              color={formData.color}
              odor={formData.odor}
              onUpdate={updateFormData}
            />
          )}
          
          {currentStep === 4 && (
            <FrequencyStep
              frequency={formData.frequency}
              straining={formData.straining}
              onUpdate={updateFormData}
            />
          )}
          
          {currentStep === 5 && (
            <PhotoUploadStep
              photoUrl={formData.photoUrl}
              onUpload={(photoUrl) => updateFormData({ photoUrl })}
            />
          )}
          
          {currentStep === 6 && (
            <ScoreSummaryStep
              formData={formData}
              petName={petName}
            />
          )}
        </div>

        <div className="flex gap-3">
          {currentStep > 1 && (
            <Button variant="outline" onClick={handleBack} className="flex-1">
              Back
            </Button>
          )}
          
          {currentStep < totalSteps ? (
            <Button 
              onClick={handleNext} 
              disabled={!canProceed()}
              className="flex-1"
            >
              Next
            </Button>
          ) : (
            <Button 
              onClick={handleComplete}
              className="flex-1 bg-green-600 hover:bg-green-700"
            >
              Complete Check-In
            </Button>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
