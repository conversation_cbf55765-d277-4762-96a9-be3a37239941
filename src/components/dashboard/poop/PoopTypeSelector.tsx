
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { POOP_TYPES } from "@/types/poop";
import { cn } from "@/lib/utils";

interface PoopTypeSelectorProps {
  selectedType?: number;
  onSelect: (type: number) => void;
}

export const PoopTypeSelector: React.FC<PoopTypeSelectorProps> = ({
  selectedType,
  onSelect
}) => {
  return (
    <div className="space-y-3">
      <div className="text-center text-sm text-gray-600 mb-4">
        Select the type that best matches what you observed:
      </div>
      
      <div className="grid grid-cols-1 gap-3 max-h-96 overflow-y-auto">
        {POOP_TYPES.map((type) => {
          return (
            <Card 
              key={type.id}
              className={cn(
                "cursor-pointer transition-all hover:shadow-md",
                selectedType === type.id 
                  ? "ring-2 ring-amber-500 bg-amber-50" 
                  : "hover:bg-gray-50"
              )}
              onClick={() => onSelect(type.id)}
            >
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="w-16 h-16 flex items-center justify-center">
                    <img 
                      src={type.imageUrl} 
                      alt={type.name}
                      className="w-13 h-13 object-contain"
                    />
                  </div>
                  <div className="flex-1">
                    <div className="font-medium text-gray-900">
                      Type {type.id}: {type.name}
                    </div>
                    <div className="text-sm text-gray-600">
                      {type.description}
                    </div>
                  </div>
                  {selectedType === type.id && (
                    <div className="w-6 h-6 rounded-full bg-amber-500 flex items-center justify-center">
                      <span className="text-white text-sm">✓</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
};
