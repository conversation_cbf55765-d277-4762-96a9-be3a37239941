
import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { PoopLog } from "@/types/poop";
import { calculatePoopScore } from "@/utils/poopScoring";
import { PoopTypeStep } from "./components/PoopTypeStep";
import { HealthFlagsStep } from "./components/HealthFlagsStep";
import { ScoreSummary } from "./components/ScoreSummary";
import { cn } from "@/lib/utils";
import { Sparkles } from "lucide-react";
import confetti from "canvas-confetti";

interface SimplifiedPoopModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onComplete: (log: PoopLog) => void;
  petName: string;
  existingLog?: PoopLog;
}

export const SimplifiedPoopModal: React.FC<SimplifiedPoopModalProps> = ({
  open,
  onO<PERSON>Change,
  onComplete,
  petName,
  existingLog
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<Partial<PoopLog>>({
    date: new Date().toISOString().split('T')[0],
    dogName: petName,
    type: existingLog?.type,
    easeOfPickup: existingLog?.easeOfPickup,
    color: existingLog?.color || 'brown',
    odor: existingLog?.odor || 'normal',
    frequency: existingLog?.frequency ?? true,
    straining: existingLog?.straining || 'none',
    completed: false
  });

  // Live score calculation
  const { score, feedback } = calculatePoopScore(formData);

  const handleQuickNormal = () => {
    const normalLog: PoopLog = {
      id: existingLog?.id || `log-${Date.now()}`,
      date: formData.date!,
      dogName: formData.dogName!,
      type: 4, // Normal
      easeOfPickup: 'easy',
      color: 'brown',
      odor: 'normal',
      frequency: true,
      straining: 'none',
      score: 90,
      feedback: "Everything looks normal! Great job keeping track! 🎉",
      completed: true
    };

    // Celebration for quick normal
    confetti({
      particleCount: 50,
      spread: 70,
      origin: { y: 0.6 }
    });

    onComplete(normalLog);
    onOpenChange(false);
    setCurrentStep(1);
  };

  const handleTypeSelect = (type: number, ease: 'easy' | 'sticky' | 'mushy') => {
    const newData = { ...formData, type, easeOfPickup: ease };
    setFormData(newData);
    
    // Auto-advance if score looks good (no concerning flags needed)
    const testScore = calculatePoopScore(newData).score;
    if (testScore >= 70) {
      setTimeout(() => setCurrentStep(3), 300);
    } else {
      setCurrentStep(2);
    }
  };

  const handleFormUpdate = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleComplete = () => {
    const finalScore = calculatePoopScore(formData);
    
    const log: PoopLog = {
      id: existingLog?.id || `log-${Date.now()}`,
      date: formData.date!,
      dogName: formData.dogName!,
      type: formData.type!,
      easeOfPickup: formData.easeOfPickup!,
      color: formData.color!,
      odor: formData.odor!,
      frequency: formData.frequency!,
      straining: formData.straining!,
      score: finalScore.score,
      feedback: finalScore.feedback,
      completed: true
    };

    // Celebration for good scores
    if (finalScore.score >= 80) {
      confetti({
        particleCount: 100,
        spread: 70,
        origin: { y: 0.6 }
      });
    }

    onComplete(log);
    onOpenChange(false);
    setCurrentStep(1);
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600";
    if (score >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  const getScoreBackground = (score: number) => {
    if (score >= 80) return "bg-green-100 border-green-300";
    if (score >= 60) return "bg-yellow-100 border-yellow-300";
    return "bg-red-100 border-red-300";
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-center flex items-center justify-center gap-2">
            <Sparkles className="h-5 w-5 text-amber-500" />
            {currentStep === 1 && "How did it look today?"}
            {currentStep === 2 && "Any concerning signs?"}
            {currentStep === 3 && `${petName}'s Score`}
          </DialogTitle>
          
          {/* Live Score Preview */}
          {formData.type && (
            <div className="flex justify-center">
              <Badge className={cn("text-lg font-bold px-3 py-1", getScoreBackground(score))}>
                <span className={getScoreColor(score)}>Score: {score}</span>
              </Badge>
            </div>
          )}
        </DialogHeader>

        <div className="py-4">
          {/* Step 1: Quick Normal or Poop Type Selection */}
          {currentStep === 1 && (
            <PoopTypeStep
              onQuickNormal={handleQuickNormal}
              onTypeSelect={handleTypeSelect}
            />
          )}

          {/* Step 2: Health Flags (Only if concerning) */}
          {currentStep === 2 && (
            <HealthFlagsStep
              formData={formData}
              onUpdate={handleFormUpdate}
              onContinue={() => setCurrentStep(3)}
            />
          )}

          {/* Step 3: Final Score & Celebration */}
          {currentStep === 3 && (
            <ScoreSummary
              score={score}
              feedback={feedback}
              petName={petName}
              onComplete={handleComplete}
            />
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
