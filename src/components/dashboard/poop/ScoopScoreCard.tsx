
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { PoopCheckInModal } from "./PoopCheckInModal";
import { PoopLog } from "@/types/poop";
import { getHealthTrend, checkForAlerts } from "@/utils/poopScoring";
import { Calendar, TrendingUp, AlertTriangle } from "lucide-react";
import { StoolIcon } from "@/components/icons/StoolIcon";

interface ScoopScoreCardProps {
  poopLogs: PoopLog[];
  onAddLog: (log: PoopLog) => void;
  petName: string;
}

export const ScoopScoreCard: React.FC<ScoopScoreCardProps> = ({
  poopLogs,
  onAddLog,
  petName
}) => {
  const [checkInOpen, setCheckInOpen] = useState(false);
  
  const todayLog = poopLogs.find(log => 
    new Date(log.date).toDateString() === new Date().toDateString()
  );
  
  const weeklyTrend = getHealthTrend(poopLogs);
  const alerts = checkForAlerts(poopLogs);
  const streakCount = calculateStreak(poopLogs);

  return (
    <>
      <Card className="h-full">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-semibold flex items-center gap-2">
              <StoolIcon className="h-5 w-5 text-amber-600" />
              Scoop & Score
            </CardTitle>
            {todayLog && (
              <Badge variant="secondary" className="bg-green-100 text-green-700">
                ✓ Logged
              </Badge>
            )}
          </div>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* Today's Status */}
          {todayLog ? (
            <div className="space-y-3">
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600 mb-1">
                  {todayLog.score}
                </div>
                <div className="text-sm text-gray-600">Today's Digestive Score</div>
              </div>
              
              <div className="bg-gray-50 rounded-lg p-3">
                <p className="text-sm text-gray-700">{todayLog.feedback}</p>
              </div>
            </div>
          ) : (
            <div className="text-center space-y-3">
              <div className="text-gray-500">
                <StoolIcon className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                <p className="text-sm">Haven't checked {petName}'s poop today</p>
              </div>
              
              <Button 
                onClick={() => setCheckInOpen(true)}
                className="w-full bg-amber-600 hover:bg-amber-700"
              >
                🐾 Daily Check-In
              </Button>
            </div>
          )}

          {/* Weekly Trend */}
          <div className="flex items-center gap-2 text-sm">
            <TrendingUp className="h-4 w-4 text-blue-500" />
            <span className="text-gray-600">7-day trend:</span>
            <span className="font-medium">{weeklyTrend}</span>
          </div>

          {/* Streak */}
          {streakCount > 0 && (
            <div className="flex items-center gap-2 text-sm">
              <Calendar className="h-4 w-4 text-orange-500" />
              <span className="text-gray-600">{streakCount} day streak!</span>
              <span>🦴</span>
            </div>
          )}

          {/* Alerts */}
          {alerts.length > 0 && (
            <div className="space-y-2">
              {alerts.map((alert, index) => (
                <div key={index} className="flex items-center gap-2 text-sm text-amber-700 bg-amber-50 p-2 rounded">
                  <AlertTriangle className="h-4 w-4" />
                  <span>{alert}</span>
                </div>
              ))}
            </div>
          )}

          {/* Action Buttons */}
          <div className="space-y-2">
            {todayLog && (
              <Button 
                variant="outline" 
                size="sm" 
                className="w-full"
                onClick={() => setCheckInOpen(true)}
              >
                Update Today's Log
              </Button>
            )}
            
            <Button variant="ghost" size="sm" className="w-full">
              View 7-Day History
            </Button>
          </div>
        </CardContent>
      </Card>

      <PoopCheckInModal
        open={checkInOpen}
        onOpenChange={setCheckInOpen}
        onComplete={onAddLog}
        petName={petName}
        existingLog={todayLog}
      />
    </>
  );
};

function calculateStreak(logs: PoopLog[]): number {
  let streak = 0;
  const sortedLogs = [...logs].sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  
  for (const log of sortedLogs) {
    if (log.score >= 70) {
      streak++;
    } else {
      break;
    }
  }
  
  return streak;
}
