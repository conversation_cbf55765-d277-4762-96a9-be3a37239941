
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { PoopLog } from "@/types/poop";

interface ColorOdorStepProps {
  color?: PoopLog['color'];
  odor?: PoopLog['odor'];
  onUpdate: (data: Partial<PoopLog>) => void;
}

export const ColorOdorStep: React.FC<ColorOdorStepProps> = ({
  color,
  odor,
  onUpdate
}) => {
  const colorOptions = [
    { value: 'brown' as const, label: 'Brown', icon: '🟫', risk: 'normal' },
    { value: 'black' as const, label: 'Black', icon: '⬛', risk: 'high' },
    { value: 'red' as const, label: 'Red', icon: '🟥', risk: 'high' },
    { value: 'pale' as const, label: 'Pale', icon: '⬜', risk: 'medium' },
    { value: 'greenish' as const, label: 'Greenish', icon: '🟩', risk: 'medium' }
  ];

  const odorOptions = [
    { value: 'normal' as const, label: 'Normal', description: 'Typical poop smell' },
    { value: 'strong' as const, label: 'Strong', description: 'More pungent than usual' },
    { value: 'sour' as const, label: 'Sour-smelling', description: 'Acidic or unusual odor' }
  ];

  return (
    <div className="space-y-6">
      {/* Color Selection */}
      <div>
        <h3 className="font-medium text-gray-900 mb-3">What color was it?</h3>
        <div className="grid grid-cols-2 gap-2">
          {colorOptions.map((option) => (
            <Card 
              key={option.value}
              className={cn(
                "cursor-pointer transition-all hover:shadow-md",
                color === option.value 
                  ? "ring-2 ring-amber-500 bg-amber-50" 
                  : "hover:bg-gray-50",
                option.risk === 'high' && color === option.value && "ring-red-500 bg-red-50",
                option.risk === 'medium' && color === option.value && "ring-yellow-500 bg-yellow-50"
              )}
              onClick={() => onUpdate({ color: option.value })}
            >
              <CardContent className="p-3 text-center">
                <div className="text-2xl mb-1">{option.icon}</div>
                <div className="text-sm font-medium">{option.label}</div>
                {option.risk === 'high' && color === option.value && (
                  <div className="text-xs text-red-600 mt-1">⚠️ Alert</div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Odor Selection */}
      <div>
        <h3 className="font-medium text-gray-900 mb-3">How was the smell?</h3>
        <div className="space-y-2">
          {odorOptions.map((option) => (
            <Card 
              key={option.value}
              className={cn(
                "cursor-pointer transition-all hover:shadow-md",
                odor === option.value 
                  ? "ring-2 ring-amber-500 bg-amber-50" 
                  : "hover:bg-gray-50"
              )}
              onClick={() => onUpdate({ odor: option.value })}
            >
              <CardContent className="p-3">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium text-gray-900">{option.label}</div>
                    <div className="text-sm text-gray-600">{option.description}</div>
                  </div>
                  {odor === option.value && (
                    <div className="w-6 h-6 rounded-full bg-amber-500 flex items-center justify-center">
                      <span className="text-white text-sm">✓</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};
