import React, { useState, useEffect } from "react";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { PoopLog } from "@/types/poop";
import { ModalChoiceStep } from "./components/ModalChoiceStep";
import { ModalTypeStep } from "./components/ModalTypeStep";
import { ModalFlagsStep } from "./components/ModalFlagsStep";
import { QuickNormalButton } from "./components/QuickNormalButton";
import { CustomCheckStep } from "./components/CustomCheckStep";
import { ModalSeparator } from "./components/ModalSeparator";
import { ModalHeader } from "./components/ModalHeader";
import { createCompletedLog, calculateScorePreview } from "./utils/completionHandler";

interface QuickPoopModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onComplete: (log: PoopLog) => void;
  petName: string;
  existingLog?: PoopLog;
  startingMode?: 'choice' | 'type';
}

export const QuickPoopModal: React.FC<QuickPoopModalProps> = ({
  open,
  onOpenChange,
  onComplete,
  petName,
  existingLog,
  startingMode = 'choice'
}) => {
  const [mode, setMode] = useState<'choice' | 'type' | 'flags'>(startingMode);
  const [selectedType, setSelectedType] = useState<number | null>(null);
  const [flags, setFlags] = useState({
    colorIssue: false,
    straining: false,
    strongOdor: false,
    noPoop: false
  });

  // Reset mode when modal opens with different starting mode
  useEffect(() => {
    if (open) {
      setMode(startingMode);
    }
  }, [open, startingMode]);

  const handleTypeSelect = (type: number) => {
    setSelectedType(type);
    setMode('flags');
  };

  const handleFlagToggle = (flag: keyof typeof flags) => {
    setFlags(prev => ({ ...prev, [flag]: !prev[flag] }));
  };

  const handleComplete = () => {
    if (!selectedType) return;
    
    const log = createCompletedLog({
      selectedType,
      flags,
      petName,
      existingLog
    });

    onComplete(log);
    onOpenChange(false);
    resetModal();
  };

  const resetModal = () => {
    setMode(startingMode);
    setSelectedType(null);
    setFlags({ colorIssue: false, straining: false, strongOdor: false, noPoop: false });
  };

  const handleBackClick = () => {
    if (mode === 'flags') {
      if (startingMode === 'type') {
        setMode('type');
      } else {
        setMode('choice');
      }
    } else if (mode === 'type') {
      setMode('choice');
    }
  };

  const scorePreview = calculateScorePreview(selectedType, flags);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md max-h-[90vh] overflow-y-auto text-center">
        <ModalHeader
          mode={mode}
          startingMode={startingMode}
          petName={petName}
          onBackClick={handleBackClick}
          scorePreview={mode === 'flags' ? scorePreview : undefined}
        />

        <div className="py-4">
          {mode === 'choice' && (
            <div className="space-y-4">
              <QuickNormalButton
                petName={petName}
                onComplete={onComplete}
                onClose={() => onOpenChange(false)}
                existingLog={existingLog}
              />
              <ModalSeparator />
              <CustomCheckStep onCustomCheck={() => setMode('type')} />
            </div>
          )}

          {mode === 'type' && (
            <ModalTypeStep
              selectedType={selectedType}
              petName={petName}
              onTypeSelect={handleTypeSelect}
            />
          )}

          {mode === 'flags' && (
            <ModalFlagsStep
              flags={flags}
              onFlagToggle={handleFlagToggle}
              onComplete={handleComplete}
            />
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
