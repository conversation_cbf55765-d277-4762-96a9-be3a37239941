
import confetti from "canvas-confetti";
import { PoopLog } from "@/types/poop";
import { calculatePoopScore } from "@/utils/poopScoring";

interface CompletionHandlerProps {
  selectedType: number;
  flags: {
    colorIssue: boolean;
    straining: boolean;
    strongOdor: boolean;
    noPoop: boolean;
  };
  petName: string;
  existingLog?: PoopLog;
}

export const createCompletedLog = ({
  selectedType,
  flags,
  petName,
  existingLog
}: CompletionHandlerProps): PoopLog => {
  // Get today's date in YYYY-MM-DD format (local timezone)
  const getTodayDateString = () => {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  const formData = {
    type: selectedType,
    easeOfPickup: (selectedType >= 6 ? 'mushy' : selectedType <= 2 ? 'sticky' : 'easy') as 'easy' | 'sticky' | 'mushy',
    color: (flags.colorIssue ? 'red' : 'brown') as 'brown' | 'black' | 'red' | 'pale' | 'greenish',
    odor: (flags.strongOdor ? 'strong' : 'normal') as 'normal' | 'strong' | 'sour',
    frequency: !flags.noPoop,
    straining: (flags.straining ? 'a-lot' : 'none') as 'none' | 'mild' | 'a-lot'
  };

  const { score, feedback } = calculatePoopScore(formData);
  
  const log: PoopLog = {
    id: existingLog?.id || `log-${Date.now()}`,
    date: getTodayDateString(),
    dogName: petName,
    type: selectedType,
    easeOfPickup: formData.easeOfPickup,
    color: formData.color,
    odor: formData.odor,
    frequency: formData.frequency,
    straining: formData.straining,
    score,
    feedback,
    completed: true
  };

  console.log('Creating custom poop log for date:', log.date);

  if (score >= 80) {
    confetti({
      particleCount: 40,
      spread: 60,
      origin: { y: 0.6 }
    });
  }

  return log;
};

export const calculateScorePreview = (
  selectedType: number | null,
  flags: CompletionHandlerProps['flags']
): number => {
  if (!selectedType) return 0;
  
  const formData = {
    type: selectedType,
    easeOfPickup: (selectedType >= 6 ? 'mushy' : selectedType <= 2 ? 'sticky' : 'easy') as 'easy' | 'sticky' | 'mushy',
    color: (flags.colorIssue ? 'red' : 'brown') as 'brown' | 'black' | 'red' | 'pale' | 'greenish',
    odor: (flags.strongOdor ? 'strong' : 'normal') as 'normal' | 'strong' | 'sour',
    frequency: !flags.noPoop,
    straining: (flags.straining ? 'a-lot' : 'none') as 'none' | 'mild' | 'a-lot'
  };
  
  return calculatePoopScore(formData).score;
};
