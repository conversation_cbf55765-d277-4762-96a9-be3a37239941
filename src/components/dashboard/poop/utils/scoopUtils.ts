
import { PoopLog } from "@/types/poop";

export function calculateStreak(logs: PoopLog[]): number {
  let streak = 0;
  const sortedLogs = [...logs].sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  
  for (const log of sortedLogs) {
    if (log.score >= 70) {
      streak++;
    } else {
      break;
    }
  }
  
  return streak;
}

export function calculateWeeklyAverage(logs: PoopLog[]): number {
  const recentLogs = logs.slice(-7);
  if (recentLogs.length === 0) return 0;
  
  const avgScore = recentLogs.reduce((sum, log) => sum + log.score, 0) / recentLogs.length;
  return Math.round(avgScore);
}
