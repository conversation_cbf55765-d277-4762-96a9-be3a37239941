
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";

interface PickupEaseStepProps {
  selected?: 'easy' | 'sticky' | 'mushy';
  onSelect: (ease: 'easy' | 'sticky' | 'mushy') => void;
}

export const PickupEaseStep: React.FC<PickupEaseStepProps> = ({
  selected,
  onSelect
}) => {
  const options = [
    {
      value: 'easy' as const,
      label: 'Easy, kept its shape',
      icon: '✅',
      description: 'Firm and solid, picked up cleanly'
    },
    {
      value: 'sticky' as const,
      label: 'A bit sticky',
      icon: '⚠️',
      description: 'Somewhat soft, left slight residue'
    },
    {
      value: 'mushy' as const,
      label: 'Mushy or left residue',
      icon: '❌',
      description: 'Very soft, difficult to pick up completely'
    }
  ];

  return (
    <div className="space-y-3">
      <div className="text-center text-sm text-gray-600 mb-4">
        How easy was it to pick up with a poop bag?
      </div>
      
      {options.map((option) => (
        <Card 
          key={option.value}
          className={cn(
            "cursor-pointer transition-all hover:shadow-md",
            selected === option.value 
              ? "ring-2 ring-amber-500 bg-amber-50" 
              : "hover:bg-gray-50"
          )}
          onClick={() => onSelect(option.value)}
        >
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="text-2xl">{option.icon}</div>
              <div className="flex-1">
                <div className="font-medium text-gray-900">
                  {option.label}
                </div>
                <div className="text-sm text-gray-600">
                  {option.description}
                </div>
              </div>
              {selected === option.value && (
                <div className="w-6 h-6 rounded-full bg-amber-500 flex items-center justify-center">
                  <span className="text-white text-sm">✓</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};
