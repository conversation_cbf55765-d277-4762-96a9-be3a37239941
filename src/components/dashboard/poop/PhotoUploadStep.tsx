
import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Camera, Upload, X } from "lucide-react";

interface PhotoUploadStepProps {
  photoUrl?: string;
  onUpload: (photoUrl: string) => void;
}

export const PhotoUploadStep: React.FC<PhotoUploadStepProps> = ({
  photoUrl,
  onUpload
}) => {
  const [uploading, setUploading] = useState(false);

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setUploading(true);
    
    // Simulate upload - in real app would upload to Supabase storage
    setTimeout(() => {
      const mockUrl = URL.createObjectURL(file);
      onUpload(mockUrl);
      setUploading(false);
    }, 1000);
  };

  const removePhoto = () => {
    onUpload('');
  };

  return (
    <div className="space-y-4">
      <div className="text-center text-sm text-gray-600 mb-4">
        Want to track visually? Upload a photo for your records.
      </div>

      {photoUrl ? (
        <Card>
          <CardContent className="p-4">
            <div className="relative">
              <img 
                src={photoUrl} 
                alt="Poop photo" 
                className="w-full h-32 object-cover rounded-lg"
              />
              <Button
                variant="destructive"
                size="sm"
                className="absolute top-2 right-2"
                onClick={removePhoto}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            <div className="text-center text-sm text-gray-600 mt-2">
              Photo uploaded successfully
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-3">
          <Card className="border-dashed border-2 border-gray-300 hover:border-gray-400 transition-colors">
            <CardContent className="p-6">
              <div className="text-center space-y-4">
                <div className="text-4xl text-gray-400">📷</div>
                <div>
                  <h3 className="font-medium text-gray-900 mb-1">Upload a photo</h3>
                  <p className="text-sm text-gray-600">Help track changes over time</p>
                </div>
                
                <div className="space-y-2">
                  <label htmlFor="photo-upload">
                    <Button 
                      variant="outline" 
                      className="w-full"
                      disabled={uploading}
                      asChild
                    >
                      <span>
                        <Camera className="h-4 w-4 mr-2" />
                        {uploading ? 'Uploading...' : 'Take or Upload Photo'}
                      </span>
                    </Button>
                  </label>
                  <input
                    id="photo-upload"
                    type="file"
                    accept="image/*"
                    capture="environment"
                    className="hidden"
                    onChange={handleFileUpload}
                    disabled={uploading}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="text-center">
            <Button variant="ghost" size="sm" className="text-gray-500">
              Skip for now
            </Button>
          </div>
        </div>
      )}

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
        <div className="text-sm text-blue-800">
          <strong>💡 Tip:</strong> Photos help you and your vet spot patterns and changes over time.
        </div>
      </div>
    </div>
  );
};
