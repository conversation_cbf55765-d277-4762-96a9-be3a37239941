
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { SimplifiedPoopModal } from "./SimplifiedPoopModal";
import { ScoreDisplay } from "./components/ScoreDisplay";
import { ScoopCardStats } from "./components/ScoopCardStats";
import { ScoopCardActions } from "./components/ScoopCardActions";
import { PoopLog } from "@/types/poop";
import { getHealthTrend, checkForAlerts } from "@/utils/poopScoring";
import { calculateStreak, calculateWeeklyAverage } from "./utils/scoopUtils";
import { TrendingUp, AlertTriangle } from "lucide-react";
import { StoolIcon } from "@/components/icons/StoolIcon";

interface EnhancedScoopScoreCardProps {
  poopLogs: PoopLog[];
  onAddLog: (log: PoopLog) => void;
  petName: string;
}

export const EnhancedScoopScoreCard: React.FC<EnhancedScoopScoreCardProps> = ({
  poopLogs,
  onAddLog,
  petName
}) => {
  const [checkInOpen, setCheckInOpen] = useState(false);
  
  const todayLog = poopLogs.find(log => 
    new Date(log.date).toDateString() === new Date().toDateString()
  );
  
  const weeklyTrend = getHealthTrend(poopLogs);
  const alerts = checkForAlerts(poopLogs);
  const streakCount = calculateStreak(poopLogs);
  const weeklyAverage = calculateWeeklyAverage(poopLogs);

  return (
    <>
      <Card className="h-full relative overflow-hidden">
        {/* Gradient background for visual appeal */}
        <div className="absolute inset-0 bg-gradient-to-br from-amber-50 to-orange-50 opacity-50" />
        
        <CardHeader className="pb-3 relative">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-semibold flex items-center gap-2">
              <StoolIcon className="h-5 w-5 text-amber-600" />
              Scoop & Score
              {streakCount > 2 && (
                <Badge variant="secondary" className="bg-orange-100 text-orange-700">
                  🔥 {streakCount} day streak!
                </Badge>
              )}
            </CardTitle>
            {todayLog && (
              <Badge variant="secondary" className="bg-green-100 text-green-700">
                ✓ Logged
              </Badge>
            )}
          </div>
        </CardHeader>
        
        <CardContent className="space-y-4 relative">
          {/* Today's Status */}
          {todayLog ? (
            <div className="space-y-3">
              {/* Score Display with Visual Appeal */}
              <ScoreDisplay 
                score={todayLog.score} 
                label="Today's Digestive Score" 
              />
              
              {/* Feedback with Better Styling */}
              <div className="bg-white/70 backdrop-blur-sm rounded-lg p-3 border">
                <p className="text-sm text-gray-700">{todayLog.feedback}</p>
              </div>

              {/* Quick Stats Row */}
              <ScoopCardStats 
                weeklyAverage={weeklyAverage}
                totalLogs={poopLogs.length}
              />
            </div>
          ) : null}

          {/* Weekly Trend - More Visual */}
          <div className="flex items-center gap-2 text-sm bg-white/50 rounded-lg p-2">
            <TrendingUp className="h-4 w-4 text-blue-500" />
            <span className="text-gray-600">Week:</span>
            <span className="font-medium">{weeklyTrend}</span>
          </div>

          {/* Alerts with Better Visibility */}
          {alerts.length > 0 && (
            <div className="space-y-2">
              {alerts.map((alert, index) => (
                <div key={index} className="flex items-center gap-2 text-sm text-amber-700 bg-amber-50 border border-amber-200 p-2 rounded-lg">
                  <AlertTriangle className="h-4 w-4 flex-shrink-0" />
                  <span>{alert}</span>
                </div>
              ))}
            </div>
          )}

          {/* Action Buttons - Simplified */}
          <ScoopCardActions
            hasLog={!!todayLog}
            petName={petName}
            streakCount={streakCount}
            onCheckIn={() => setCheckInOpen(true)}
            onUpdate={() => setCheckInOpen(true)}
          />
        </CardContent>
      </Card>

      <SimplifiedPoopModal
        open={checkInOpen}
        onOpenChange={setCheckInOpen}
        onComplete={onAddLog}
        petName={petName}
        existingLog={todayLog}
      />
    </>
  );
};
