
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { PoopLog, POOP_TYPES } from "@/types/poop";
import { calculatePoopScore } from "@/utils/poopScoring";
import { Button } from "@/components/ui/button";
import { Share2, TrendingUp } from "lucide-react";

interface ScoreSummaryStepProps {
  formData: Partial<PoopLog>;
  petName: string;
}

export const ScoreSummaryStep: React.FC<ScoreSummaryStepProps> = ({
  formData,
  petName
}) => {
  const { score, feedback } = calculatePoopScore(formData);
  const poopType = POOP_TYPES.find(t => t.id === formData.type);
  
  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600";
    if (score >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  const getScoreBackground = (score: number) => {
    if (score >= 80) return "bg-green-100";
    if (score >= 60) return "bg-yellow-100";
    return "bg-red-100";
  };

  return (
    <div className="space-y-6">
      {/* Score Display */}
      <Card className={`${getScoreBackground(score)} border-2`}>
        <CardContent className="p-6 text-center">
          <div className={`text-5xl font-bold ${getScoreColor(score)} mb-2`}>
            {score}
          </div>
          <div className="text-lg font-medium text-gray-900 mb-3">
            {petName}'s Digestive Score
          </div>
          <div className="text-sm text-gray-700 bg-white/50 rounded-lg p-3">
            {feedback}
          </div>
        </CardContent>
      </Card>

      {/* Summary Details */}
      <Card>
        <CardContent className="p-4">
          <h3 className="font-medium text-gray-900 mb-3">Today's Summary</h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Poop Type:</span>
              <span className="font-medium">
                {poopType ? `${poopType.name} (Type ${poopType.id})` : 'Not specified'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Pickup Ease:</span>
              <span className="font-medium capitalize">{formData.easeOfPickup}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Color:</span>
              <span className="font-medium capitalize">{formData.color}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Frequency:</span>
              <span className="font-medium">{formData.frequency ? 'Yes' : 'No movement today'}</span>
            </div>
            {formData.frequency && (
              <div className="flex justify-between">
                <span className="text-gray-600">Straining:</span>
                <span className="font-medium capitalize">{formData.straining?.replace('-', ' ')}</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="grid grid-cols-2 gap-3">
        <Button variant="outline" size="sm" className="flex items-center gap-2">
          <TrendingUp className="h-4 w-4" />
          View History
        </Button>
        <Button variant="outline" size="sm" className="flex items-center gap-2">
          <Share2 className="h-4 w-4" />
          Share with Vet
        </Button>
      </div>

      {/* Tips based on score */}
      {score < 70 && (
        <Card className="bg-amber-50 border-amber-200">
          <CardContent className="p-4">
            <h4 className="font-medium text-amber-800 mb-2">💡 Improvement Tips</h4>
            <ul className="text-sm text-amber-700 space-y-1">
              <li>• Ensure fresh water is always available</li>
              <li>• Consider adding fiber-rich foods</li>
              <li>• Maintain regular exercise routine</li>
              <li>• Monitor for 2-3 days and consult vet if concerns persist</li>
            </ul>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
