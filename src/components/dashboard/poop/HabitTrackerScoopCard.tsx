
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { QuickPoopModal } from "./QuickPoopModal";
import { PoopLog } from "@/types/poop";
import { checkForAlerts } from "@/utils/poopScoring";
import { WeeklyScoreDisplay } from "./components/WeeklyScoreDisplay";
import { WeeklyProgressDots } from "./components/WeeklyProgressDots";
import { TodayScoreDisplay } from "./components/TodayScoreDisplay";
import { ActionButtons } from "./components/ActionButtons";
import { AlertsList } from "./components/AlertsList";

interface HabitTrackerScoopCardProps {
  poopLogs: PoopLog[];
  onAddLog: (log: PoopLog) => void;
  petName: string;
}

export const HabitTrackerScoopCard: React.FC<HabitTrackerScoopCardProps> = ({
  poopLogs,
  onAddLog,
  petName
}) => {
  const [modalO<PERSON>, setModalOpen] = useState(false);
  const [modalStartingMode, setModalStartingMode] = useState<'choice' | 'type'>('choice');
  
  const todayLog = poopLogs.find(log => 
    new Date(log.date).toDateString() === new Date().toDateString()
  );
  
  const alerts = checkForAlerts(poopLogs);
  const weeklyAverage = calculateWeeklyAverage(poopLogs);

  const handleSomethingOff = () => {
    setModalStartingMode('type');
    setModalOpen(true);
  };

  const handleUpdateLog = () => {
    setModalStartingMode('choice');
    setModalOpen(true);
  };

  return (
    <>
      <Card className="hover:shadow-md transition-shadow cursor-pointer border-2 border-brand-light bg-gradient-to-br from-white to-brand-light/30">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span className="text-xl">💩</span>
              <CardTitle className="text-lg font-semibold text-brand-primary">
                Poop Check
              </CardTitle>
            </div>
            
            <WeeklyScoreDisplay 
              weeklyAverage={weeklyAverage} 
              todayLog={todayLog}
            />
          </div>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Weekly Progress Dots */}
          <WeeklyProgressDots poopLogs={poopLogs} />

          {/* Action Buttons or Today's Score */}
          {!todayLog ? (
            <ActionButtons 
              onAddLog={onAddLog}
              petName={petName}
              onSomethingOff={handleSomethingOff}
            />
          ) : (
            <TodayScoreDisplay 
              todayLog={todayLog}
              onUpdateLog={handleUpdateLog}
            />
          )}

          {/* Alerts */}
          <AlertsList alerts={alerts} />
        </CardContent>
      </Card>

      <QuickPoopModal
        open={modalOpen}
        onOpenChange={setModalOpen}
        onComplete={onAddLog}
        petName={petName}
        existingLog={todayLog}
        startingMode={modalStartingMode}
      />
    </>
  );
};

function calculateWeeklyAverage(logs: PoopLog[]): number {
  const recentLogs = logs.slice(-7);
  if (recentLogs.length === 0) return 0;
  
  const avgScore = recentLogs.reduce((sum, log) => sum + log.score, 0) / recentLogs.length;
  return Math.round(avgScore);
}
