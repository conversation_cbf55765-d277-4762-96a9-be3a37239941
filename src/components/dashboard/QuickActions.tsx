
import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { MessageSquare, Calendar, TrendingUp } from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";

interface QuickActionsProps {
  onOpenAICoach?: () => void;
}

export const QuickActions: React.FC<QuickActionsProps> = ({ onOpenAICoach }) => {
  const navigate = useNavigate();
  const isMobile = useIsMobile();

  const handleAICoachClick = () => {
    if (isMobile && onOpenAICoach) {
      onOpenAICoach();
    } else {
      navigate("/coach");
    }
  };

  return (
    <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
      <Button 
        onClick={handleAICoachClick}
        className="h-16 bg-brand-primary hover:bg-brand-dark flex flex-col gap-1"
      >
        <MessageSquare className="h-5 w-5" />
        <span className="text-sm">Talk to AI Coach</span>
      </Button>
      
      <Button 
        onClick={() => navigate("/tests")}
        variant="outline"
        className="h-16 flex flex-col gap-1"
      >
        <Calendar className="h-5 w-5" />
        <span className="text-sm">Order Test</span>
      </Button>
      
      <Button 
        onClick={() => navigate("/supplements")}
        variant="outline"
        className="h-16 flex flex-col gap-1"
      >
        <TrendingUp className="h-5 w-5" />
        <span className="text-sm">View Reports</span>
      </Button>
    </div>
  );
};
