
import React from "react";
import { FloatingChatButton } from "@/components/ai-coach/FloatingChatButton";
import { ChatModal } from "@/components/ai-coach/ChatModal";
import { useIsMobile } from "@/hooks/use-mobile";

interface DashboardMobileElementsProps {
  isAICoachOpen: boolean;
  setIsAICoachOpen: (open: boolean) => void;
  messages: any[];
  isTyping: boolean;
  handleSendMessage: (message: string) => void;
}

export const DashboardMobileElements: React.FC<DashboardMobileElementsProps> = ({
  isAICoachOpen,
  setIsAICoachOpen,
  messages,
  isTyping,
  handleSendMessage
}) => {
  const isMobile = useIsMobile();

  return (
    <>
      {/* Floating Chat Button - Only on mobile */}
      {isMobile && (
        <FloatingChatButton onClick={() => setIsAICoachOpen(true)} />
      )}

      {/* AI Coach Modal */}
      <ChatModal
        open={isAICoachOpen}
        onOpenChange={setIsAICoachOpen}
        messages={messages}
        isTyping={isTyping}
        onSendMessage={handleSendMessage}
      />
    </>
  );
};
