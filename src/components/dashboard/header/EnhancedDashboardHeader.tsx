
import React, { useState } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ChevronDown, Menu } from "lucide-react";
import { Button } from "@/components/ui/button";
import { PetSelectorModal } from "./PetSelectorModal";
import { cn } from "@/lib/utils";

interface Pet {
  id: string;
  name: string;
  breed: string;
  age: number;
  avatar?: string;
}

interface EnhancedDashboardHeaderProps {
  userName: string;
  activePet: Pet;
  pets: Pet[];
  onSelectPet: (pet: Pet) => void;
  onAddPet?: () => void;
  onMenuClick?: () => void;
}

const getTimeBasedGreeting = (): string => {
  const hour = new Date().getHours();
  if (hour < 12) return "Good morning";
  if (hour < 17) return "Good afternoon";
  return "Good evening";
};

export const EnhancedDashboardHeader: React.FC<EnhancedDashboardHeaderProps> = ({
  userName,
  activePet,
  pets,
  onSelectPet,
  onAddPet,
  onMenuClick
}) => {
  const [petSelectorOpen, setPetSelectorOpen] = useState(false);
  const greeting = getTimeBasedGreeting();

  return (
    <>
      <div className="sticky top-0 z-40 bg-white/95 backdrop-blur-sm border-b border-gray-100">
        <div className="px-4 py-3">
          {/* Top row: Greeting and Menu */}
          <div className="flex items-center justify-between mb-2">
            {/* Greeting */}
            <h1 className="text-lg font-semibold text-gray-900">
              {greeting}, {userName}!
            </h1>
            
            <Button
              variant="ghost"
              size="icon"
              onClick={onMenuClick}
              className="md:hidden"
            >
              <Menu className="h-5 w-5" />
            </Button>
          </div>
          
          {/* Bottom row: Pet selector and status */}
          <div className="flex items-center justify-between">
            <button
              onClick={() => setPetSelectorOpen(true)}
              className={cn(
                "flex items-center gap-2 py-1 px-2 -mx-2 rounded-lg transition-colors",
                "hover:bg-gray-50 active:bg-gray-100"
              )}
            >
              <Avatar className="h-8 w-8">
                <AvatarImage src={activePet.avatar} alt={activePet.name} />
                <AvatarFallback className="bg-brand-primary/20 text-brand-primary font-semibold text-sm">
                  {activePet.name.charAt(0).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              
              <span className="font-medium text-gray-900">{activePet.name}</span>
              <ChevronDown className="h-4 w-4 text-gray-400" />
            </button>
            
            <div className="flex items-center gap-1.5">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
              <span className="text-sm font-medium text-green-600">Active</span>
            </div>
          </div>
        </div>
      </div>

      <PetSelectorModal
        open={petSelectorOpen}
        onOpenChange={setPetSelectorOpen}
        pets={pets}
        activePet={activePet}
        onSelectPet={onSelectPet}
        onAddPet={onAddPet}
      />
    </>
  );
};
