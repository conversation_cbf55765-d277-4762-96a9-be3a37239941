
import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Plus } from "lucide-react";
import { cn } from "@/lib/utils";

interface Pet {
  id: string;
  name: string;
  breed: string;
  age: number;
  avatar?: string;
}

interface PetSelectorModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  pets: Pet[];
  activePet: Pet;
  onSelectPet: (pet: Pet) => void;
  onAddPet?: () => void;
}

export const PetSelectorModal: React.FC<PetSelectorModalProps> = ({
  open,
  onOpenChange,
  pets,
  activePet,
  onSelectPet,
  onAddPet
}) => {
  const handlePetSelect = (pet: Pet) => {
    onSelectPet(pet);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Select Pet</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-3">
          {pets.map((pet) => (
            <button
              key={pet.id}
              onClick={() => handlePetSelect(pet)}
              className={cn(
                "w-full flex items-center gap-3 p-3 rounded-lg transition-all duration-200",
                "hover:bg-gray-50 active:scale-[0.98]",
                activePet.id === pet.id ? "bg-brand-light border border-brand-primary" : "border border-transparent"
              )}
            >
              <Avatar className="h-12 w-12">
                <AvatarImage src={pet.avatar} alt={pet.name} />
                <AvatarFallback className="bg-brand-primary/20 text-brand-primary font-semibold">
                  {pet.name.charAt(0).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              
              <div className="flex-1 text-left">
                <h3 className="font-semibold text-gray-900">{pet.name}</h3>
                <p className="text-sm text-gray-500">
                  {pet.age >= 12 ? `${Math.round(pet.age / 12 * 10) / 10} years` : `${pet.age} months`} • {pet.breed}
                </p>
              </div>
              
              {activePet.id === pet.id && (
                <div className="w-2 h-2 bg-green-500 rounded-full" />
              )}
            </button>
          ))}
          
          {onAddPet && (
            <Button
              variant="outline"
              onClick={onAddPet}
              className="w-full justify-start gap-3 p-3 h-auto"
            >
              <div className="w-12 h-12 rounded-full border-2 border-dashed border-gray-300 flex items-center justify-center">
                <Plus className="h-5 w-5 text-gray-400" />
              </div>
              <span>Add New Pet</span>
            </Button>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
