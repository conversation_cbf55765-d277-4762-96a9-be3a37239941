
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ArrowRight, Database, Heart } from "lucide-react";
import { PersonalizedRecommendation } from "@/types/sense";

// Mock personalized recommendations
const mockRecommendations: PersonalizedRecommendation[] = [
  {
    id: "rec1",
    category: "supplement",
    title: "Enhanced Probiotic Blend",
    description: "Based on your pet's specific microbiome profile, a custom probiotic blend targeting the underrepresented Lactobacillus species",
    priority: "high",
    action: "Add to supplement plan",
    confidenceScore: 92,
    targetedIssues: ["Low diversity in gut bacteria", "Occasional loose stool"],
    expectedOutcome: "Improved stool consistency and increased beneficial bacteria population",
    timeToEffect: "2-3 weeks",
    supportingEvidence: "Recent studies show 87% improvement in similar cases with targeted probiotic therapy"
  },
  {
    id: "rec2",
    category: "nutrition",
    title: "Anti-Inflammatory Diet Modification",
    description: "Removing chicken and adding novel protein sources based on <PERSON>'s specific inflammatory markers",
    priority: "medium",
    action: "Update meal plan",
    confidenceScore: 85,
    targetedIssues: ["Elevated inflammatory markers", "Seasonal skin irritation"],
    expectedOutcome: "Reduced inflammation and improved coat condition",
    timeToEffect: "4-6 weeks",
    supportingEvidence: "Analysis of 240 similar profiles showed 72% reduction in inflammation markers"
  },
  {
    id: "rec3",
    category: "supplement",
    title: "Joint Support Protocol",
    description: "Customized combination of glucosamine, chondroitin, and green-lipped mussel extract",
    priority: "medium",
    action: "Add joint supplement",
    confidenceScore: 88,
    targetedIssues: ["Early signs of joint stiffness", "Breed-specific risk of hip dysplasia"],
    expectedOutcome: "Maintained mobility and joint protection",
    timeToEffect: "6-8 weeks",
    supportingEvidence: "Preventative intervention shows 64% reduction in joint issues later in life"
  }
];

interface PersonalizedInsightsProps {
  petName?: string;
}

export const PersonalizedInsights: React.FC<PersonalizedInsightsProps> = ({
  petName = "Sophie"
}) => {
  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2">
          <Heart className="h-5 w-5 text-brand-primary" />
          Personalized Health Insights
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-6">
        <div className="bg-muted/50 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <Database className="h-5 w-5 text-brand-primary" />
            <h3 className="font-medium">AI-Generated Insights</h3>
          </div>
          <p className="text-sm text-muted-foreground">
            These recommendations are generated specifically for {petName} based on:
          </p>
          <ul className="mt-2 space-y-1 text-sm">
            <li className="flex items-center gap-2">
              <span className="bg-green-100 rounded-full p-1">
                <svg className="h-2 w-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M5 13l4 4L19 7"></path>
                </svg>
              </span>
              <span>Breed-specific health patterns (Labrador Retriever)</span>
            </li>
            <li className="flex items-center gap-2">
              <span className="bg-green-100 rounded-full p-1">
                <svg className="h-2 w-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M5 13l4 4L19 7"></path>
                </svg>
              </span>
              <span>Age-related considerations (4 years old)</span>
            </li>
            <li className="flex items-center gap-2">
              <span className="bg-green-100 rounded-full p-1">
                <svg className="h-2 w-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M5 13l4 4L19 7"></path>
                </svg>
              </span>
              <span>Latest microbiome test results (2 days ago)</span>
            </li>
            <li className="flex items-center gap-2">
              <span className="bg-green-100 rounded-full p-1">
                <svg className="h-2 w-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M5 13l4 4L19 7"></path>
                </svg>
              </span>
              <span>Historical health data (previous tests and outcomes)</span>
            </li>
            <li className="flex items-center gap-2">
              <span className="bg-green-100 rounded-full p-1">
                <svg className="h-2 w-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M5 13l4 4L19 7"></path>
                </svg>
              </span>
              <span>Response to previous supplements and interventions</span>
            </li>
          </ul>
        </div>
        
        <h3 className="font-medium text-lg">Personalized Recommendations</h3>
        
        <div className="space-y-4">
          {mockRecommendations.map((rec) => (
            <div key={rec.id} className="border rounded-lg p-4 hover:border-brand-primary/30 transition-colors">
              <div className="flex justify-between items-start">
                <h4 className="font-medium">{rec.title}</h4>
                <Badge 
                  variant="outline" 
                  className={`
                    ${rec.confidenceScore > 90 ? "bg-green-50 text-green-700 border-green-200" : 
                      rec.confidenceScore > 80 ? "bg-blue-50 text-blue-700 border-blue-200" :
                      "bg-amber-50 text-amber-700 border-amber-200"}
                  `}
                >
                  {rec.confidenceScore}% confidence
                </Badge>
              </div>
              
              <p className="text-sm text-muted-foreground mt-2">{rec.description}</p>
              
              <div className="mt-3 space-y-2">
                <div>
                  <h5 className="text-xs font-medium text-muted-foreground">TARGETED ISSUES</h5>
                  <div className="flex flex-wrap gap-2 mt-1">
                    {rec.targetedIssues.map((issue, i) => (
                      <span key={i} className="text-xs bg-muted px-2 py-1 rounded-full">
                        {issue}
                      </span>
                    ))}
                  </div>
                </div>
                
                <div className="flex justify-between text-sm pt-1">
                  <div>
                    <span className="text-xs font-medium text-muted-foreground">EXPECTED OUTCOME</span>
                    <p>{rec.expectedOutcome}</p>
                  </div>
                  <div className="text-right">
                    <span className="text-xs font-medium text-muted-foreground">TIME TO EFFECT</span>
                    <p>{rec.timeToEffect}</p>
                  </div>
                </div>
                
                <div className="text-xs pt-1">
                  <span className="font-medium text-muted-foreground">EVIDENCE BASE</span>
                  <p>{rec.supportingEvidence}</p>
                </div>
              </div>
              
              <div className="flex justify-end mt-3">
                <Button variant="link" className="text-brand-primary p-0 h-auto">
                  View detailed analysis
                  <ArrowRight className="ml-1 h-3 w-3" />
                </Button>
              </div>
            </div>
          ))}
        </div>
        
        <div className="flex justify-between pt-2">
          <Button variant="outline">
            Download Full Report
          </Button>
          <Button className="bg-brand-primary hover:bg-brand-dark">
            Implement Recommendations
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
