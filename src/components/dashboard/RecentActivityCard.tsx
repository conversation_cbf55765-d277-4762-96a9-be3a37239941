
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Clock } from "lucide-react";

export const RecentActivityCard: React.FC = () => {
  return (
    <Card className="md:col-span-2">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="h-5 w-5" />
          Recent Activity
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/30">
            <div className="w-2 h-2 rounded-full bg-green-500"></div>
            <div className="flex-1">
              <p className="text-sm font-medium">SHAPE check-in completed</p>
              <p className="text-xs text-muted-foreground">2 hours ago</p>
            </div>
            <Badge variant="outline">SHAPE</Badge>
          </div>
          
          <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/30">
            <div className="w-2 h-2 rounded-full bg-blue-500"></div>
            <div className="flex-1">
              <p className="text-sm font-medium">Stool log recorded - Type 4</p>
              <p className="text-xs text-muted-foreground">Yesterday, 8:30 AM</p>
            </div>
            <Badge variant="outline">Poop</Badge>
          </div>
          
          <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/30">
            <div className="w-2 h-2 rounded-full bg-purple-500"></div>
            <div className="flex-1">
              <p className="text-sm font-medium">Started new supplement: Probiotics</p>
              <p className="text-xs text-muted-foreground">3 days ago</p>
            </div>
            <Badge variant="outline">Supplement</Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
