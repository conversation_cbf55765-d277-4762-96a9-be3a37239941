
import React from "react";
import { FirstTimeUserSection } from "./components/FirstTimeUserSection";
import { SmartCTAButton } from "./components/SmartCTAButton";
import { getSmartCTAText } from "./utils/headerUtils";

interface EnhancedShapeCheckInHeaderProps {
  petName: string;
  completedCategories: number;
  totalCategories: number;
  hasAnyEngagement: boolean;
  onQuickAction?: () => void;
  userName?: string;
}

const getTimeBasedGreeting = (): string => {
  const hour = new Date().getHours();
  if (hour < 12) return "Good morning";
  if (hour < 17) return "Good afternoon";
  return "Good evening";
};

export const EnhancedShapeCheckInHeader: React.FC<EnhancedShapeCheckInHeaderProps> = ({
  petName,
  completedCategories,
  totalCategories,
  hasAnyEngagement,
  onQuickAction,
  userName = "Dan"
}) => {
  const isFullyCompleted = completedCategories === totalCategories;
  const smartCTAText = getSmartCTAText(hasAnyEngagement, completedCategories, totalCategories);
  const greeting = getTimeBasedGreeting();

  return (
    <div className="relative overflow-hidden">
      {/* Subtle Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-brand-light/20 via-brand-light/10 to-transparent" />
      
      <div className="relative p-4 md:p-6">
        {/* Mobile Greeting - only show on mobile */}
        <div className="md:hidden mb-4">
          <h2 className="text-lg font-semibold text-gray-900">
            {greeting}, {userName}!
          </h2>
        </div>

        {/* First-time User Experience */}
        {!hasAnyEngagement && (
          <FirstTimeUserSection petName={petName} />
        )}

        {/* Smart Primary CTA - Hide when fully completed */}
        {!isFullyCompleted && (
          <SmartCTAButton
            ctaText={smartCTAText}
            hasAnyEngagement={hasAnyEngagement}
            onQuickAction={onQuickAction}
          />
        )}
      </div>
    </div>
  );
};
