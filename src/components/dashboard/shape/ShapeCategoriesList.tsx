
import React from "react";
import { <PERSON>, Droplet, Zap, Apple } from "lucide-react";
import { ShapeRatingCard } from "./ShapeRatingCard";
import { PoopRatingCard } from "./PoopRatingCard";

type ShapeRatings = {
  sleep: number;
  hydration: number;
  activity: number;
  poop: number;
  eating: number;
};

interface ShapeCategoriesListProps {
  shapeRatings: ShapeRatings;
  onCardClick: (category: keyof ShapeRatings, title: string, icon: React.ElementType, description: string) => void;
  onPoopCardClick: () => void;
  petName: string;
}

export const ShapeCategoriesList: React.FC<ShapeCategoriesListProps> = ({
  shapeRatings,
  onCardClick,
  onPoopCardClick,
  petName
}) => {
  const shapeCategories = [
    {
      key: 'sleep' as keyof ShapeRatings,
      title: "Sleep",
      icon: Moon,
      description: "Quality of rest and sleep patterns",
      color: "bg-teal-700"
    },
    {
      key: 'hydration' as keyof ShapeRatings,
      title: "Hydration",
      icon: Droplet,
      description: "Water intake and hydration levels",
      color: "bg-blue-700"
    },
    {
      key: 'activity' as keyof ShapeRatings,
      title: "Activity",
      icon: Zap,
      description: "Exercise and physical activity",
      color: "bg-orange-700"
    }
  ];

  // Helper function to get value only if it's completed (> 0)
  const getCompletedValue = (rating: number): number | undefined => {
    return rating > 0 ? rating : undefined;
  };

  return (
    <div className="p-4 md:p-6">
      <div className="space-y-3">
        {shapeCategories.map((category) => (
          <ShapeRatingCard
            key={category.key}
            title={category.title}
            icon={category.icon}
            value={getCompletedValue(shapeRatings[category.key])}
            onRate={() => {}} // Not used in the refactored version
            onClick={() => onCardClick(category.key, category.title, category.icon, category.description)}
            description={category.description}
            lastUpdated={new Date()}
            color={category.color}
          />
        ))}
        
        {/* Poop Card - Now consistent with other SHAPE cards */}
        <PoopRatingCard
          value={getCompletedValue(shapeRatings.poop)}
          onClick={onPoopCardClick}
          petName={petName}
        />

        {/* Eating Card */}
        <ShapeRatingCard
          key="eating"
          title="Eating"
          icon={Apple}
          value={getCompletedValue(shapeRatings.eating)}
          onRate={() => {}} // Not used in the refactored version
          onClick={() => onCardClick('eating', 'Eating', Apple, 'Appetite and food intake')}
          description="Appetite and food intake"
          lastUpdated={new Date()}
          color="bg-green-700"
        />
      </div>
    </div>
  );
};
