
import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Star, ArrowRight, CheckCircle } from "lucide-react";
import { cn } from "@/lib/utils";

interface ShapeRatingModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  letter: string;
  currentValue: number;
  onRate: (rating: number) => void;
  description?: string;
}

export const ShapeRatingModal: React.FC<ShapeRatingModalProps> = ({
  open,
  onOpenChange,
  title,
  letter,
  currentValue,
  onRate,
  description
}) => {
  const [selectedRating, setSelectedRating] = useState(currentValue || 0);
  const [hoveredRating, setHoveredRating] = useState(0);
  const [showCompletion, setShowCompletion] = useState(false);

  useEffect(() => {
    if (open) {
      setSelectedRating(currentValue || 0);
      setHoveredRating(0);
      setShowCompletion(false);
    }
  }, [open, currentValue]);

  // Category-specific rating labels
  const getCategoryRatingLabels = (category: string) => {
    switch (category.toLowerCase()) {
      case 'sleep':
        return {
          1: { label: "Restless", description: "Frequent waking, difficulty settling" },
          2: { label: "Light Sleep", description: "Some restlessness, shorter sleep periods" },
          3: { label: "Decent Rest", description: "Average sleep quality, occasional stirring" },
          4: { label: "Sound Sleep", description: "Good rest with minimal interruptions" },
          5: { label: "Energized", description: "Deep, restorative sleep all night" }
        };
      case 'hydration':
        return {
          1: { label: "Dehydrated", description: "Rarely drinking, showing signs of thirst" },
          2: { label: "Low Intake", description: "Drinking less than usual" },
          3: { label: "Moderate", description: "Regular drinking throughout the day" },
          4: { label: "Well Hydrated", description: "Consistent water intake, healthy signs" },
          5: { label: "Optimal", description: "Perfect hydration, excellent water consumption" }
        };
      case 'activity':
        return {
          1: { label: "Lethargic", description: "Very low energy, reluctant to move" },
          2: { label: "Low Energy", description: "Less active than usual, short bursts" },
          3: { label: "Moderate", description: "Normal activity levels, some play" },
          4: { label: "Active", description: "Good energy, engaging in activities" },
          5: { label: "Energetic", description: "High energy, playful and engaged" }
        };
      case 'poop':
        return {
          1: { label: "Concerning", description: "Unusual consistency, color, or frequency" },
          2: { label: "Below Normal", description: "Some irregularities noted" },
          3: { label: "Fair", description: "Mostly normal with minor variations" },
          4: { label: "Good", description: "Normal consistency and frequency" },
          5: { label: "Perfect", description: "Ideal consistency, color, and timing" }
        };
      case 'eating':
        return {
          1: { label: "Poor Appetite", description: "Reluctant to eat, leaving food" },
          2: { label: "Light Eating", description: "Eating less than usual" },
          3: { label: "Moderate", description: "Normal eating patterns" },
          4: { label: "Good Appetite", description: "Eating well, finishing meals" },
          5: { label: "Excellent", description: "Enthusiastic eating, great appetite" }
        };
      default:
        return {
          1: { label: "Poor", description: "Needs attention" },
          2: { label: "Fair", description: "Below average" },
          3: { label: "Good", description: "Average condition" },
          4: { label: "Very Good", description: "Above average" },
          5: { label: "Excellent", description: "Optimal condition" }
        };
    }
  };

  const ratingLabels = getCategoryRatingLabels(title);
  const activeRating = hoveredRating || selectedRating;

  const handleStarClick = (rating: number) => {
    setSelectedRating(rating);
  };

  const handleConfirm = () => {
    if (selectedRating === 0) return;
    
    setShowCompletion(true);
    
    // Delay the callback to show completion animation
    setTimeout(() => {
      onRate(selectedRating);
      onOpenChange(false);
    }, 800);
  };

  const getColorForRating = (rating: number) => {
    if (rating <= 2) return "text-red-500";
    if (rating === 3) return "text-yellow-500";
    return "text-green-500";
  };

  if (showCompletion) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-md">
          <div className="text-center py-8">
            <div className="w-16 h-16 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center animate-scale-in">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              {title} Recorded!
            </h3>
            <p className="text-gray-600">
              {ratingLabels[selectedRating]?.label} - Great job tracking!
            </p>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader className="text-center space-y-4">
          <div>
            <DialogTitle className="text-2xl font-bold text-gray-900">{title}</DialogTitle>
            {description && (
              <p className="text-sm text-gray-600 mt-2 max-w-sm mx-auto">{description}</p>
            )}
          </div>
        </DialogHeader>

        <div className="py-6 space-y-6">
          {/* Enhanced Star Rating with Slider Behavior */}
          <div className="flex justify-center gap-1">
            {[1, 2, 3, 4, 5].map((rating) => {
              const isSelected = selectedRating >= rating;
              const isHovered = hoveredRating >= rating;
              const isActive = isSelected || isHovered;
              
              return (
                <Button
                  key={rating}
                  variant="ghost"
                  size="sm"
                  className="p-3 hover:bg-gray-50 rounded-full transition-all duration-200 ease-out hover:scale-105 active:scale-95"
                  onMouseEnter={() => setHoveredRating(rating)}
                  onMouseLeave={() => setHoveredRating(0)}
                  onClick={() => handleStarClick(rating)}
                >
                  <Star
                    className={cn(
                      "transition-all duration-200 ease-out",
                      isActive
                        ? "h-12 w-12 fill-yellow-400 text-yellow-400 scale-110 drop-shadow-sm"
                        : "h-14 w-14 text-gray-300 hover:text-yellow-300 hover:scale-105"
                    )}
                  />
                </Button>
              );
            })}
          </div>

          {/* Dynamic Rating Label */}
          {activeRating > 0 && (
            <div className="text-center space-y-2 min-h-[80px] flex flex-col justify-center animate-fade-in">
              <div className="flex items-center justify-center gap-2">
                <span className={cn("text-2xl font-bold transition-colors duration-300", getColorForRating(activeRating))}>
                  {ratingLabels[activeRating]?.label}
                </span>
              </div>
              <p className="text-sm text-gray-600 max-w-xs mx-auto leading-relaxed">
                {ratingLabels[activeRating]?.description}
              </p>
            </div>
          )}

          {/* Helper Text */}
          {activeRating === 0 && (
            <div className="text-center py-4">
              <p className="text-gray-500 text-sm">
                Tap a star to rate {title.toLowerCase()} quality
              </p>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3 pt-4 border-t border-gray-100">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="flex-1 h-12 text-base font-medium"
          >
            Cancel
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={selectedRating === 0}
            className={cn(
              "flex-1 h-12 text-base font-semibold transition-all duration-200",
              "bg-gradient-to-r from-brand-primary to-brand-secondary",
              "hover:from-brand-primary/90 hover:to-brand-secondary/90",
              "disabled:opacity-50 disabled:cursor-not-allowed",
              "text-white border-0 shadow-lg hover:shadow-xl"
            )}
          >
            {selectedRating > 0 ? (
              <div className="flex items-center gap-2">
                Confirm Rating
                <ArrowRight className="h-4 w-4" />
              </div>
            ) : (
              "Select Rating"
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
