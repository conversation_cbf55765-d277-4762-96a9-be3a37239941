
import React from "react";
import { <PERSON>, TrendingUp, TrendingDown, Sparkles } from "lucide-react";

interface ShapeCheckInHeaderProps {
  petName: string;
  petInfo: string;
  streakCount: number;
  overallScore?: number;
  completedCategories: number;
  totalCategories: number;
  completionRate: number;
  hasAnyEngagement: boolean;
  motivationalMessage?: string | null;
}

export const ShapeCheckInHeader: React.FC<ShapeCheckInHeaderProps> = ({
  petName,
  petInfo,
  streakCount,
  overallScore,
  completedCategories,
  totalCategories,
  completionRate,
  hasAnyEngagement,
  motivationalMessage
}) => {
  const isImproving = overallScore && overallScore > 3.5;

  // Convert pet info to use natural language for age
  const formatPetAge = (petInfo: string): string => {
    // Extract age in months if it follows the pattern "X month old" or "X months old"
    const monthMatch = petInfo.match(/(\d+)\s+months?\s+old/i);
    if (monthMatch) {
      const months = parseInt(monthMatch[1]);
      if (months >= 12) {
        const years = months / 12;
        const roundedYears = Math.round(years * 10) / 10;
        return petInfo.replace(/\d+\s+months?\s+old/i, `${roundedYears} year${roundedYears !== 1 ? 's' : ''} old`);
      }
    }
    return petInfo;
  };

  const formattedPetInfo = formatPetAge(petInfo);

  return (
    <div className="p-4 md:p-5 border-b border-gray-50">
      {/* Simplified Pet Profile Card */}
      <div className="bg-gradient-to-r from-brand-light/30 to-brand-light/10 rounded-lg p-4 mb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3 flex-1 min-w-0">
            {/* Pet Avatar */}
            <div className="w-12 h-12 rounded-full bg-brand-primary/20 flex items-center justify-center">
              <span className="text-lg font-bold text-brand-primary">
                {petName.charAt(0).toUpperCase()}
              </span>
            </div>
            
            <div className="flex-1 min-w-0">
              <h1 className="text-xl md:text-2xl font-bold text-gray-900 leading-tight">{petName}</h1>
              <p className="text-sm text-gray-500 mt-0.5">{formattedPetInfo}</p>
            </div>
          </div>
          
          {/* SHAPE Score Display - Only show if user has engagement */}
          {hasAnyEngagement && overallScore && (
            <div className="text-right ml-4">
              <div className="flex items-center justify-end gap-1.5 mb-1">
                <div className="text-3xl md:text-4xl font-bold text-brand-primary tabular-nums">
                  {Math.round(overallScore * 20)}
                </div>
                {isImproving ? (
                  <TrendingUp className="h-5 w-5 text-green-500" />
                ) : (
                  <TrendingDown className="h-5 w-5 text-gray-400" />
                )}
              </div>
              <div className="text-xs font-medium text-gray-600 uppercase tracking-wide">SHAPE Score</div>
            </div>
          )}
        </div>
      </div>

      {/* Streak Display - Only show if user has engagement and streak > 0 */}
      {hasAnyEngagement && streakCount > 0 && (
        <div className="flex items-center justify-center gap-2 mb-3">
          <Flame className="h-4 w-4 text-orange-500" />
          <span className="text-lg font-bold text-orange-500 tabular-nums">{streakCount}</span>
          <span className="text-sm font-medium text-gray-600">day streak</span>
        </div>
      )}

      {/* Progress Section - Only show if user has engagement */}
      {hasAnyEngagement && (
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">
              {completedCategories} of {totalCategories} complete
            </span>
            <span className="text-sm font-bold text-brand-primary tabular-nums">
              {Math.round(completionRate)}%
            </span>
          </div>
          <div className="w-full bg-gray-100 rounded-full h-2.5 overflow-hidden">
            <div 
              className="bg-gradient-to-r from-brand-primary to-brand-secondary h-2.5 rounded-full transition-all duration-700 ease-out"
              style={{ width: `${completionRate}%` }}
            />
          </div>
          {/* Visual Progress Dots */}
          <div className="flex justify-center gap-1.5 mt-2">
            {[...Array(5)].map((_, i) => (
              <div 
                key={i} 
                className={`w-2 h-2 rounded-full transition-colors duration-300 ${
                  i < completedCategories ? 'bg-brand-primary' : 'bg-gray-200'
                }`} 
              />
            ))}
          </div>
          
          {/* Motivational Message */}
          {motivationalMessage && (
            <div className="flex items-center justify-center gap-2 mt-3 p-3 bg-green-50 border border-green-200 rounded-lg">
              <Sparkles className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium text-green-700">{motivationalMessage}</span>
            </div>
          )}
        </div>
      )}

      {/* First-time user encouragement */}
      {!hasAnyEngagement && (
        <div className="text-center py-4 bg-brand-light/20 rounded-lg">
          <h3 className="text-lg font-semibold text-gray-900 mb-1">
            Ready to track {petName}'s wellness?
          </h3>
          <p className="text-sm text-gray-600 mb-3">
            Complete your first SHAPE check-in to start tracking progress.
          </p>
          <div className="text-xs text-gray-500">
            SHAPE: Sleep • Hydration • Activity • Poop • Eating
          </div>
        </div>
      )}
    </div>
  );
};
