
import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Star, ArrowLeft, CheckCircle, Sparkles, ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils";
import { triggerHapticFeedback } from "@/utils/shareUtils";
import { useToast } from "@/hooks/use-toast";

interface CheckInFlowModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  shapeRatings: {
    sleep: number;
    hydration: number;
    activity: number;
    poop: number;
    eating: number;
  };
  onRate: (category: string, rating: number) => void;
  petName: string;
}

type ShapeCategory = {
  key: keyof CheckInFlowModalProps['shapeRatings'];
  title: string;
  letter: string;
  description: string;
  labels: {
    [key: number]: { label: string; description: string };
  };
};

const SHAPE_CATEGORIES: ShapeCategory[] = [
  {
    key: 'sleep',
    title: 'Sleep',
    letter: 'S',
    description: 'How was your pet\'s sleep quality?',
    labels: {
      1: { label: "Restless", description: "Frequent waking, difficulty settling" },
      2: { label: "Light Sleep", description: "Some restlessness, shorter sleep periods" },
      3: { label: "Decent Rest", description: "Average sleep quality, occasional stirring" },
      4: { label: "Sound Sleep", description: "Good rest with minimal interruptions" },
      5: { label: "Energized", description: "Deep, restorative sleep all night" }
    }
  },
  {
    key: 'hydration',
    title: 'Hydration',
    letter: 'H',
    description: 'How was your pet\'s water intake?',
    labels: {
      1: { label: "Dehydrated", description: "Rarely drinking, showing signs of thirst" },
      2: { label: "Low Intake", description: "Drinking less than usual" },
      3: { label: "Moderate", description: "Regular drinking throughout the day" },
      4: { label: "Well Hydrated", description: "Consistent water intake, healthy signs" },
      5: { label: "Optimal", description: "Perfect hydration, excellent water consumption" }
    }
  },
  {
    key: 'activity',
    title: 'Activity',
    letter: 'A',
    description: 'How was your pet\'s activity level?',
    labels: {
      1: { label: "Lethargic", description: "Very low energy, reluctant to move" },
      2: { label: "Low Energy", description: "Less active than usual, short bursts" },
      3: { label: "Moderate", description: "Normal activity levels, some play" },
      4: { label: "Active", description: "Good energy, engaging in activities" },
      5: { label: "Energetic", description: "High energy, playful and engaged" }
    }
  },
  {
    key: 'eating',
    title: 'Eating',
    letter: 'E',
    description: 'How was your pet\'s appetite?',
    labels: {
      1: { label: "Poor Appetite", description: "Reluctant to eat, leaving food" },
      2: { label: "Light Eating", description: "Eating less than usual" },
      3: { label: "Moderate", description: "Normal eating patterns" },
      4: { label: "Good Appetite", description: "Eating well, finishing meals" },
      5: { label: "Excellent", description: "Enthusiastic eating, great appetite" }
    }
  }
];

export const CheckInFlowModal: React.FC<CheckInFlowModalProps> = ({
  open,
  onOpenChange,
  shapeRatings,
  onRate,
  petName
}) => {
  const { toast } = useToast();
  const [currentCategoryIndex, setCurrentCategoryIndex] = useState(0);
  const [selectedRating, setSelectedRating] = useState(0);
  const [hoveredRating, setHoveredRating] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [completedInThisSession, setCompletedInThisSession] = useState<Set<string>>(new Set());

  // Get unchecked categories (excluding poop which has its own modal)
  const getUncompletedCategories = () => {
    const categories = SHAPE_CATEGORIES.filter(category => 
      category.key !== 'poop' && shapeRatings[category.key] === 0
    );
    console.log('Uncompleted categories:', categories.map(c => c.key));
    console.log('Current shape ratings:', shapeRatings);
    return categories;
  };

  const uncompletedCategories = getUncompletedCategories();
  const currentCategory = uncompletedCategories[currentCategoryIndex];
  const totalCategories = uncompletedCategories.length;
  const progress = totalCategories > 0 ? ((currentCategoryIndex + 1) / totalCategories) * 100 : 100;

  // Initialize selected rating when category changes
  useEffect(() => {
    if (currentCategory) {
      setSelectedRating(shapeRatings[currentCategory.key] || 0);
      setHoveredRating(0);
    }
  }, [currentCategory, shapeRatings]);

  // Reset state when modal opens
  useEffect(() => {
    if (open) {
      setCurrentCategoryIndex(0);
      setIsTransitioning(false);
      setCompletedInThisSession(new Set());
    }
  }, [open]);

  const handleStarClick = async (rating: number) => {
    if (!currentCategory || isTransitioning) return;
    
    triggerHapticFeedback();
    setSelectedRating(rating);
    
    // Save the rating
    onRate(currentCategory.key, rating);
    setCompletedInThisSession(prev => new Set(prev.add(currentCategory.key)));
    
    // Show brief success feedback
    toast({
      title: `${currentCategory.title} recorded! ✓`,
      description: `${currentCategory.labels[rating]?.label} - Great job!`,
      duration: 1500,
    });

    // Auto-progress to next category after brief delay
    setIsTransitioning(true);
    
    setTimeout(() => {
      if (currentCategoryIndex < totalCategories - 1) {
        // Move to next category
        setCurrentCategoryIndex(prev => prev + 1);
        setIsTransitioning(false);
      } else {
        // Completed all categories - auto close
        setTimeout(() => {
          onOpenChange(false);
          toast({
            title: "SHAPE check-in complete! 🎉",
            description: `${petName}'s daily check-in is now complete.`,
            duration: 3000,
          });
        }, 300);
      }
    }, 300); // Reduced from 800ms to 300ms for better UX
  };

  // NEW: Separate completion handler for the completion button
  const handleComplete = async () => {
    if (!currentCategory || selectedRating === 0 || isTransitioning) return;
    
    triggerHapticFeedback();
    
    // Save the rating immediately
    onRate(currentCategory.key, selectedRating);
    setCompletedInThisSession(prev => new Set(prev.add(currentCategory.key)));
    
    // Close modal immediately with celebration
    onOpenChange(false);
    toast({
      title: "SHAPE check-in complete! 🎉",
      description: `${petName}'s daily check-in is now complete.`,
      duration: 3000,
    });
  };

  const handleBack = () => {
    if (currentCategoryIndex > 0) {
      setCurrentCategoryIndex(prev => prev - 1);
    }
  };

  const handleSkip = () => {
    if (currentCategoryIndex < totalCategories - 1) {
      setCurrentCategoryIndex(prev => prev + 1);
    } else {
      onOpenChange(false);
    }
  };

  const getColorForRating = (rating: number) => {
    if (rating <= 2) return "text-red-500";
    if (rating === 3) return "text-yellow-500";
    return "text-green-500";
  };

  const activeRating = hoveredRating || selectedRating;
  const isLastCategory = currentCategoryIndex === totalCategories - 1;

  // Don't render if no categories to complete
  if (!currentCategory || totalCategories === 0) {
    console.log('No categories to complete, modal will not render');
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-lg">
        <DialogTitle className="sr-only">
          SHAPE Check-in: {currentCategory.title}
        </DialogTitle>
        
        {/* Header with Progress */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-between mb-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBack}
              disabled={currentCategoryIndex === 0 || isTransitioning}
              className="p-2"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            
            <div className="flex-1 mx-4">
              <Progress value={progress} className="h-2" />
              <div className="text-xs text-gray-500 mt-1">
                {currentCategoryIndex + 1} of {totalCategories}
              </div>
            </div>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={handleSkip}
              disabled={isTransitioning}
              className="text-xs text-gray-500 hover:text-gray-700"
            >
              Skip
            </Button>
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-center gap-3">
              <div className={cn(
                "w-12 h-12 rounded-xl flex items-center justify-center font-bold text-lg transition-all duration-300",
                "bg-gradient-to-br from-brand-primary to-brand-secondary text-white shadow-md"
              )}>
                {currentCategory.letter}
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900">{currentCategory.title}</h2>
                <p className="text-sm text-gray-600">{currentCategory.description}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Rating Section */}
        <div className="py-6 space-y-6">
          {/* Star Rating */}
          <div className="flex justify-center gap-2">
            {[1, 2, 3, 4, 5].map((rating) => {
              const isSelected = selectedRating >= rating;
              const isHovered = hoveredRating >= rating;
              const isActive = isSelected || isHovered;
              
              return (
                <Button
                  key={rating}
                  variant="ghost"
                  size="sm"
                  disabled={isTransitioning}
                  className="p-3 hover:bg-gray-50 rounded-full transition-all duration-200 ease-out hover:scale-105 active:scale-95"
                  onMouseEnter={() => setHoveredRating(rating)}
                  onMouseLeave={() => setHoveredRating(0)}
                  onClick={() => handleStarClick(rating)}
                >
                  <Star
                    className={cn(
                      "transition-all duration-200 ease-out",
                      isActive
                        ? "h-10 w-10 fill-yellow-400 text-yellow-400 scale-110 drop-shadow-sm"
                        : "h-10 w-10 text-gray-300 hover:text-yellow-300 hover:scale-105"
                    )}
                  />
                </Button>
              );
            })}
          </div>

          {/* Rating Label and Description */}
          {activeRating > 0 && (
            <div className="text-center space-y-2 min-h-[80px] flex flex-col justify-center animate-fade-in">
              <div className="flex items-center justify-center gap-2">
                <span className={cn("text-2xl font-bold transition-colors duration-300", getColorForRating(activeRating))}>
                  {currentCategory.labels[activeRating]?.label}
                </span>
              </div>
              <p className="text-sm text-gray-600 max-w-xs mx-auto leading-relaxed">
                {currentCategory.labels[activeRating]?.description}
              </p>
            </div>
          )}

          {/* Helper Text */}
          {activeRating === 0 && !isTransitioning && (
            <div className="text-center py-4">
              <p className="text-gray-500 text-sm">
                Tap a star to rate {currentCategory.title.toLowerCase()}
              </p>
            </div>
          )}

          {/* Transitioning State */}
          {isTransitioning && !isLastCategory && (
            <div className="text-center py-4">
              <div className="flex items-center justify-center gap-2 text-green-600">
                <CheckCircle className="h-5 w-5" />
                <span className="text-sm font-medium">Moving to next category...</span>
              </div>
            </div>
          )}
        </div>

        {/* Quick Actions (only show if not transitioning) */}
        {!isTransitioning && (
          <div className="flex gap-3 pt-4 border-t border-gray-100">
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              className="flex-1"
              disabled={isTransitioning}
            >
              Cancel
            </Button>
            
            {/* FIXED: Completion button with separate handler */}
            {isLastCategory && selectedRating > 0 && (
              <Button
                onClick={handleComplete}
                disabled={isTransitioning}
                className="flex-1 bg-gradient-to-r from-brand-primary to-brand-secondary text-white"
              >
                <div className="flex items-center gap-2">
                  Complete Check-in
                  <Sparkles className="h-4 w-4" />
                </div>
              </Button>
            )}
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};
