
import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Star } from "lucide-react";
import { cn } from "@/lib/utils";
import { triggerHapticFeedback } from "@/utils/shareUtils";

interface ShapeRatingStepProps {
  category: string;
  title: string;
  description: string;
  currentRating: number;
  onRate: (rating: number) => void;
  petName: string;
}

const RATING_LABELS: { [key: string]: { [rating: number]: { label: string; description: string } } } = {
  sleep: {
    1: { label: "Restless", description: "Frequent waking, difficulty settling" },
    2: { label: "Light Sleep", description: "Some restlessness, shorter sleep periods" },
    3: { label: "Decent Rest", description: "Average sleep quality, occasional stirring" },
    4: { label: "Sound Sleep", description: "Good rest with minimal interruptions" },
    5: { label: "Energized", description: "Deep, restorative sleep all night" }
  },
  hydration: {
    1: { label: "Dehydrated", description: "Rarely drinking, showing signs of thirst" },
    2: { label: "Low Intake", description: "Drinking less than usual" },
    3: { label: "Moderate", description: "Regular drinking throughout the day" },
    4: { label: "Well Hydrated", description: "Consistent water intake, healthy signs" },
    5: { label: "Optimal", description: "Perfect hydration, excellent water consumption" }
  },
  activity: {
    1: { label: "Lethargic", description: "Very low energy, reluctant to move" },
    2: { label: "Low Energy", description: "Less active than usual, short bursts" },
    3: { label: "Moderate", description: "Normal activity levels, some play" },
    4: { label: "Active", description: "Good energy, engaging in activities" },
    5: { label: "Energetic", description: "High energy, playful and engaged" }
  },
  eating: {
    1: { label: "Poor Appetite", description: "Reluctant to eat, leaving food" },
    2: { label: "Light Eating", description: "Eating less than usual" },
    3: { label: "Moderate", description: "Normal eating patterns" },
    4: { label: "Good Appetite", description: "Eating well, finishing meals" },
    5: { label: "Excellent", description: "Enthusiastic eating, great appetite" }
  }
};

export const ShapeRatingStep: React.FC<ShapeRatingStepProps> = ({
  category,
  title,
  description,
  currentRating,
  onRate,
  petName
}) => {
  const [selectedRating, setSelectedRating] = useState(currentRating);
  const [hoveredRating, setHoveredRating] = useState(0);

  const handleStarClick = (rating: number) => {
    triggerHapticFeedback();
    setSelectedRating(rating);
    onRate(rating);
  };

  const getColorForRating = (rating: number) => {
    if (rating <= 2) return "text-red-500";
    if (rating === 3) return "text-yellow-500";
    return "text-green-500";
  };

  const activeRating = hoveredRating || selectedRating;
  const categoryLabels = RATING_LABELS[category] || {};

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-bold text-gray-900">{title}</h2>
        <p className="text-sm text-gray-600">{description}</p>
        <p className="text-xs text-gray-500">for {petName}</p>
      </div>

      {/* Star Rating */}
      <div className="flex justify-center gap-2">
        {[1, 2, 3, 4, 5].map((rating) => {
          const isSelected = selectedRating >= rating;
          const isHovered = hoveredRating >= rating;
          const isActive = isSelected || isHovered;
          
          return (
            <Button
              key={rating}
              variant="ghost"
              size="sm"
              className="p-3 hover:bg-gray-50 rounded-full transition-all duration-200 ease-out hover:scale-105 active:scale-95"
              onMouseEnter={() => setHoveredRating(rating)}
              onMouseLeave={() => setHoveredRating(0)}
              onClick={() => handleStarClick(rating)}
            >
              <Star
                className={cn(
                  "transition-all duration-200 ease-out",
                  isActive
                    ? "h-10 w-10 fill-yellow-400 text-yellow-400 scale-110 drop-shadow-sm"
                    : "h-10 w-10 text-gray-300 hover:text-yellow-300 hover:scale-105"
                )}
              />
            </Button>
          );
        })}
      </div>

      {/* Rating Label and Description */}
      {activeRating > 0 && categoryLabels[activeRating] && (
        <div className="text-center space-y-2 min-h-[80px] flex flex-col justify-center animate-fade-in">
          <div className="flex items-center justify-center gap-2">
            <span className={cn("text-2xl font-bold transition-colors duration-300", getColorForRating(activeRating))}>
              {categoryLabels[activeRating].label}
            </span>
          </div>
          <p className="text-sm text-gray-600 max-w-xs mx-auto leading-relaxed">
            {categoryLabels[activeRating].description}
          </p>
        </div>
      )}

      {/* Helper Text */}
      {activeRating === 0 && (
        <div className="text-center py-4">
          <p className="text-gray-500 text-sm">
            Tap a star to rate {title.toLowerCase()}
          </p>
        </div>
      )}
    </div>
  );
};
