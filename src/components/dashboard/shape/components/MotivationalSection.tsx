
import React from "react";
import { Flame, Sparkles } from "lucide-react";

interface MotivationalSectionProps {
  streakCount: number;
  motivationalMessage?: string | null;
}

export const MotivationalSection: React.FC<MotivationalSectionProps> = ({
  streakCount,
  motivationalMessage
}) => {
  return (
    <div className="space-y-4">
      {/* Enhanced Streak Display */}
      {streakCount > 0 && (
        <div className="flex items-center justify-center gap-3 p-4 bg-gradient-to-r from-orange-50 to-red-50 rounded-xl border border-orange-100">
          <div className="flex items-center gap-2">
            <Flame className="h-5 w-5 text-orange-500 animate-pulse" />
            <span className="text-xl md:text-2xl font-bold text-orange-500 tabular-nums">{streakCount}</span>
            <span className="text-sm font-medium text-orange-700">day streak</span>
          </div>
          <div className="text-xs text-orange-600 bg-orange-100 px-2 py-1 rounded-full font-medium">
            🔥 On fire!
          </div>
        </div>
      )}

      {/* Enhanced Motivational Message with better design */}
      {motivationalMessage && (
        <div className="flex items-center justify-center gap-3 mt-4 p-4 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl">
          <Sparkles className="h-5 w-5 text-green-600 animate-bounce flex-shrink-0" />
          <span className="text-sm font-medium text-green-700 text-center">{motivationalMessage}</span>
        </div>
      )}
    </div>
  );
};
