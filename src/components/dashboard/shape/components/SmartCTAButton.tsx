
import React from "react";
import { Sparkles } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface SmartCTAButtonProps {
  ctaText: string;
  hasAnyEngagement: boolean;
  onQuickAction?: () => void;
}

export const SmartCTAButton: React.FC<SmartCTAButtonProps> = ({
  ctaText,
  hasAnyEngagement,
  onQuickAction
}) => {
  return (
    <div className="mt-6">
      <Button 
        onClick={onQuickAction}
        size="lg"
        className={cn(
          "w-full h-14 md:h-16 text-base md:text-lg font-semibold rounded-xl shadow-lg transition-all duration-200",
          "bg-gradient-to-r from-brand-primary to-brand-secondary hover:from-brand-primary/90 hover:to-brand-secondary/90",
          "hover:shadow-xl hover:scale-[1.02] active:scale-[0.98]",
          "text-white border-0 relative overflow-hidden"
        )}
      >
        <div className="flex items-center justify-center gap-2">
          {ctaText}
          {!hasAnyEngagement && <Sparkles className="h-5 w-5" />}
        </div>
        {/* Subtle shine effect */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-pulse" />
      </Button>
    </div>
  );
};
