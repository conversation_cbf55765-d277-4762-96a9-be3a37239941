
import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { PoopLog } from "@/types/poop";
import { useDateUtils } from "@/hooks/useDateUtils";

interface PoopCheckInStepProps {
  onComplete: (log: PoopLog) => void;
  petName: string;
  existingLog?: PoopLog;
}

export const PoopCheckInStep: React.FC<PoopCheckInStepProps> = ({
  onComplete,
  petName,
  existingLog
}) => {
  const { getTodayDateString } = useDateUtils();
  const [selectedType, setSelectedType] = useState<number | null>(existingLog?.type || null);

  const handleQuickNormal = () => {
    const log: PoopLog = {
      id: `log-${Date.now()}`,
      date: getTodayDateString(),
      dogName: petName,
      type: 4,
      easeOfPickup: 'easy',
      color: 'brown',
      odor: 'normal',
      frequency: true,
      straining: 'none',
      score: 95,
      feedback: "Great! That's a healthy, well-formed stool 👏",
      completed: true
    };
    onComplete(log);
  };

  const handleCustomLog = () => {
    if (selectedType !== null) {
      const score = selectedType >= 3 && selectedType <= 5 ? 
        Math.max(70, Math.min(100, 80 + (selectedType - 3) * 10)) : 
        Math.max(20, Math.min(70, selectedType * 15));

      const log: PoopLog = {
        id: `log-${Date.now()}`,
        date: getTodayDateString(),
        dogName: petName,
        type: selectedType,
        easeOfPickup: selectedType <= 3 ? 'sticky' : 'easy',
        color: 'brown',
        odor: 'normal',
        frequency: true,
        straining: 'none',
        score,
        feedback: selectedType >= 3 && selectedType <= 5 ? 
          "Good stool quality!" : 
          "Consider monitoring diet and hydration.",
        completed: true
      };
      onComplete(log);
    }
  };

  if (existingLog?.completed) {
    return (
      <div className="text-center py-8 space-y-4">
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
          <span className="text-2xl">✅</span>
        </div>
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Poop already logged today!
          </h3>
          <p className="text-sm text-gray-600 mb-4">
            Score: {existingLog.score}/100 • Type {existingLog.type}
          </p>
          <Badge variant="secondary" className="text-xs">
            Completed at {new Date().toLocaleTimeString()}
          </Badge>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <div className="flex items-center justify-center gap-3">
          <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-brand-primary to-brand-secondary flex items-center justify-center">
            <span className="text-white font-bold text-lg">P</span>
          </div>
          <div>
            <h2 className="text-xl font-bold text-gray-900">Poop Check</h2>
            <p className="text-sm text-gray-600">How was {petName}'s bathroom visit?</p>
          </div>
        </div>
      </div>

      {/* Quick Normal Button */}
      <div className="text-center">
        <Button
          onClick={handleQuickNormal}
          size="lg"
          className="bg-green-100 text-green-700 hover:bg-green-200 border-2 border-green-300"
        >
          <span className="mr-2">✨</span>
          Normal & Healthy
          <span className="ml-2">✨</span>
        </Button>
        <p className="text-xs text-gray-500 mt-2">
          Perfect stool - firm, easy to pick up, brown color
        </p>
      </div>

      <div className="flex items-center gap-3">
        <div className="flex-1 border-t border-gray-200"></div>
        <span className="text-xs text-gray-500 uppercase tracking-wider">Or choose type</span>
        <div className="flex-1 border-t border-gray-200"></div>
      </div>

      {/* Stool Type Selection */}
      <div className="grid grid-cols-4 gap-2">
        {[1, 2, 3, 4, 5, 6, 7].map((type) => (
          <Button
            key={type}
            variant={selectedType === type ? "default" : "outline"}
            className={`h-12 text-xs ${
              selectedType === type ? "bg-brand-primary text-white" : ""
            }`}
            onClick={() => setSelectedType(type)}
          >
            Type {type}
          </Button>
        ))}
      </div>

      {selectedType && (
        <div className="text-center pt-4">
          <Button
            onClick={handleCustomLog}
            className="bg-brand-primary text-white hover:bg-brand-primary/90"
          >
            Log Type {selectedType} Stool
          </Button>
        </div>
      )}
    </div>
  );
};
