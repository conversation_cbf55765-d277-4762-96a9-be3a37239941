
import React from "react";

interface FirstTimeUserSectionProps {
  petName: string;
}

export const FirstTimeUserSection: React.FC<FirstTimeUserSectionProps> = ({
  petName
}) => {
  return (
    <div className="text-center py-8 space-y-6">
      <div className="space-y-4">
        <h3 className="text-xl md:text-2xl font-bold text-gray-900">
          Ready to track {petName}'s wellness?
        </h3>
        <p className="text-sm md:text-base text-gray-600 max-w-md mx-auto leading-relaxed">
          Start your first SHAPE check-in to begin tracking {petName}'s health journey with personalized insights.
        </p>
        <div className="inline-flex items-center gap-2 text-xs text-gray-500 bg-gray-50 px-4 py-2 rounded-full">
          <div className="flex gap-1">
            {['S', 'H', 'A', 'P', 'E'].map((letter, i) => (
              <span key={i} className="w-5 h-5 bg-gray-200 rounded-full flex items-center justify-center text-xs font-medium">
                {letter}
              </span>
            ))}
          </div>
          <span>Sleep • Hydration • Activity • Poop • Eating</span>
        </div>
      </div>
    </div>
  );
};
