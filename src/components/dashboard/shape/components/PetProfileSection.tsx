
import React from "react";
import { TrendingUp, TrendingDown, Star } from "lucide-react";

interface PetProfileSectionProps {
  petName: string;
  petInfo: string;
  hasAnyEngagement: boolean;
  completedCategories: number;
  overallScore?: number;
}

export const PetProfileSection: React.FC<PetProfileSectionProps> = ({
  petName,
  petInfo,
  hasAnyEngagement,
  completedCategories,
  overallScore
}) => {
  const isImproving = overallScore && overallScore > 3.5;

  return (
    <div className="flex items-start justify-between mb-6">
      <div className="flex items-center gap-4 flex-1 min-w-0">
        {/* Enhanced Pet Avatar with dynamic status */}
        <div className="relative">
          <div className="w-16 h-16 md:w-18 md:h-18 rounded-2xl bg-gradient-to-br from-brand-primary to-brand-secondary flex items-center justify-center shadow-lg">
            <span className="text-2xl md:text-3xl font-bold text-white">
              {petName.charAt(0).toUpperCase()}
            </span>
          </div>
          {hasAnyEngagement && completedCategories > 0 && (
            <div className="absolute -top-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center shadow-md border-2 border-white">
              <Star className="h-3 w-3 text-white fill-current" />
            </div>
          )}
        </div>
        
        <div className="flex-1 min-w-0">
          <h1 className="text-2xl md:text-3xl font-bold text-gray-900 leading-tight mb-1">
            {petName}
          </h1>
          <p className="text-sm text-gray-600 mb-2">{petInfo}</p>
        </div>
      </div>
      
      {/* Enhanced SHAPE Score Display with better mobile positioning */}
      {hasAnyEngagement && overallScore && (
        <div className="text-right ml-4 flex-shrink-0">
          <div className="flex items-center justify-end gap-2 mb-1">
            <div className="text-3xl md:text-4xl font-bold text-brand-primary tabular-nums">
              {Math.round(overallScore * 20)}
            </div>
            <div className="flex flex-col items-center">
              {isImproving ? (
                <TrendingUp className="h-4 w-4 md:h-5 md:w-5 text-green-500" />
              ) : (
                <TrendingDown className="h-4 w-4 md:h-5 md:w-5 text-gray-400" />
              )}
              <div className="text-xs text-gray-500 hidden md:block">vs yesterday</div>
            </div>
          </div>
          <div className="text-xs font-medium text-gray-600 uppercase tracking-wide">SHAPE Score</div>
        </div>
      )}
    </div>
  );
};
