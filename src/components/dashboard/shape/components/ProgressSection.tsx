
import React from "react";
import { cn } from "@/lib/utils";

interface ProgressSectionProps {
  completedCategories: number;
  totalCategories: number;
  completionRate: number;
}

export const ProgressSection: React.FC<ProgressSectionProps> = ({
  completedCategories,
  totalCategories,
  completionRate
}) => {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <span className="text-sm font-semibold text-gray-700">
            Today's Progress
          </span>
          <div className="text-xs text-gray-500">
            {completedCategories} of {totalCategories} completed
          </div>
        </div>
        <span className="text-lg font-bold text-brand-primary tabular-nums">
          {Math.round(completionRate)}%
        </span>
      </div>
      
      {/* Multi-layered Progress Bar with animations */}
      <div className="relative">
        <div className="w-full bg-gray-100 rounded-full h-4 overflow-hidden shadow-inner">
          <div 
            className="bg-gradient-to-r from-brand-primary via-brand-secondary to-brand-primary h-4 rounded-full transition-all duration-1000 ease-out relative overflow-hidden"
            style={{ width: `${completionRate}%` }}
          >
            <div className="absolute inset-0 bg-white/20 animate-pulse rounded-full" />
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-pulse" />
          </div>
        </div>
        
        {/* Enhanced Progress Milestone Indicators */}
        <div className="flex justify-between mt-3">
          {['S', 'H', 'A', 'P', 'E'].map((letter, i) => (
            <div key={i} className="flex flex-col items-center gap-1">
              <div 
                className={cn(
                  "w-4 h-4 rounded-full transition-all duration-300 border-2 flex items-center justify-center",
                  i < completedCategories 
                    ? 'bg-brand-primary border-brand-primary scale-110 shadow-md' 
                    : 'bg-gray-200 border-gray-300'
                )} 
              >
                {i < completedCategories && (
                  <div className="w-2 h-2 bg-white rounded-full" />
                )}
              </div>
              <span className={cn(
                "text-xs font-medium transition-colors",
                i < completedCategories ? "text-brand-primary" : "text-gray-400"
              )}>
                {letter}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
