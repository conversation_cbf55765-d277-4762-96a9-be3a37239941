
import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Pill, CheckCircle, AlertCircle, Clock } from "lucide-react";
import { cn } from "@/lib/utils";

interface Supplement {
  id: string;
  name: string;
  dosage: string;
  timing: string;
  type: 'supplement' | 'medication';
  given: boolean;
  givenAt?: string;
  notes?: string;
}

interface SupplementAdherenceStepProps {
  supplements: Supplement[];
  onToggleSupplement: (supplementId: string) => void;
  petName: string;
}

export const SupplementAdherenceStep: React.FC<SupplementAdherenceStepProps> = ({
  supplements,
  onToggleSupplement,
  petName
}) => {
  const completedCount = supplements.filter(s => s.given).length;
  const totalCount = supplements.length;
  const adherenceRate = totalCount > 0 ? Math.round((completedCount / totalCount) * 100) : 0;

  const getAdherenceColor = (rate: number) => {
    if (rate >= 80) return "text-green-600 bg-green-50 border-green-200";
    if (rate >= 60) return "text-yellow-600 bg-yellow-50 border-yellow-200";
    return "text-red-600 bg-red-50 border-red-200";
  };

  if (supplements.length === 0) {
    return (
      <div className="text-center py-8 space-y-4">
        <div className="flex items-center justify-center">
          <Pill className="h-12 w-12 text-gray-300" />
        </div>
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No supplements configured
          </h3>
          <p className="text-sm text-gray-500">
            {petName} doesn't have any supplements or medications set up yet.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with adherence rate */}
      <div className="text-center space-y-2">
        <div className="flex items-center justify-center gap-3">
          <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-brand-primary to-brand-secondary flex items-center justify-center">
            <Pill className="h-6 w-6 text-white" />
          </div>
          <div>
            <h2 className="text-xl font-bold text-gray-900">Supplements & Medications</h2>
            <p className="text-sm text-gray-600">Mark what {petName} has taken today</p>
          </div>
        </div>
        
        <Badge className={cn("font-semibold text-sm px-3 py-1", getAdherenceColor(adherenceRate))}>
          {adherenceRate}% Complete ({completedCount}/{totalCount})
        </Badge>
      </div>

      {/* Supplements list */}
      <div className="space-y-3">
        {supplements.map((supplement) => (
          <div
            key={supplement.id}
            className={cn(
              "flex items-center justify-between p-4 border rounded-lg transition-all duration-200",
              supplement.given
                ? "bg-green-50 border-green-200"
                : "bg-white border-gray-200 hover:border-gray-300"
            )}
          >
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <span className="font-medium text-sm truncate">{supplement.name}</span>
                <Badge 
                  variant={supplement.type === 'medication' ? 'destructive' : 'secondary'} 
                  className="text-xs whitespace-nowrap flex-shrink-0"
                >
                  {supplement.type}
                </Badge>
                <Badge variant="outline" className="text-xs whitespace-nowrap flex-shrink-0">
                  <Clock className="h-3 w-3 mr-1" />
                  {supplement.timing}
                </Badge>
              </div>
              <p className="text-xs text-muted-foreground">{supplement.dosage}</p>
              {supplement.given && supplement.givenAt && (
                <p className="text-xs text-green-600 mt-1">
                  Given at {new Date(supplement.givenAt).toLocaleTimeString([], { 
                    hour: '2-digit', 
                    minute: '2-digit' 
                  })}
                </p>
              )}
            </div>
            
            <Button
              variant={supplement.given ? "default" : "outline"}
              size="sm"
              onClick={() => onToggleSupplement(supplement.id)}
              className={cn(
                "ml-3 flex-shrink-0",
                supplement.given
                  ? "bg-green-100 text-green-700 hover:bg-green-200 border-green-300"
                  : "hover:bg-gray-50"
              )}
            >
              {supplement.given ? (
                <CheckCircle className="h-4 w-4" />
              ) : (
                <AlertCircle className="h-4 w-4" />
              )}
              <span className="ml-2 text-xs">
                {supplement.given ? "Given" : "Give"}
              </span>
            </Button>
          </div>
        ))}
      </div>

      <div className="pt-4 border-t border-gray-100">
        <p className="text-xs text-muted-foreground text-center">
          Tap "Give" when you administer each supplement or medication
        </p>
      </div>
    </div>
  );
};
