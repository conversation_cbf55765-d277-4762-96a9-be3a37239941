
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Check, ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";

interface PoopRatingCardProps {
  value?: number;
  onClick?: () => void;
  petName: string;
}

export const PoopRatingCard: React.FC<PoopRatingCardProps> = ({
  value,
  onClick,
  petName
}) => {
  const getRatingText = (rating: number) => {
    if (rating === 1) return "Poor";
    if (rating === 2) return "Fair";
    if (rating === 3) return "Good";
    if (rating === 4) return "Very Good";
    return "Excellent";
  };

  const getRatingColor = (rating: number) => {
    if (rating === 1) return "text-red-500";
    if (rating === 2) return "text-orange-500";
    if (rating === 3) return "text-yellow-500";
    if (rating === 4) return "text-blue-500";
    return "text-green-500";
  };

  const isCompleted = value !== undefined && value > 0;

  return (
    <Card 
      className="cursor-pointer hover:shadow-md hover:border-gray-200 transition-all duration-200 border-gray-100 bg-white"
      onClick={onClick}
    >
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4 flex-1 min-w-0">
            {/* Enhanced Category Circle with P */}
            <div className={cn(
              "w-12 h-12 rounded-xl flex items-center justify-center font-bold text-lg shadow-sm border-2 transition-all duration-200",
              isCompleted 
                ? "bg-brand-primary text-white border-brand-primary" 
                : "bg-gray-50 text-gray-400 border-gray-200"
            )}>
              P
            </div>
            
            <div className="flex-1 min-w-0">
              {/* Enhanced Title and Status */}
              <div className="flex items-center gap-3 mb-1">
                <h3 className="font-semibold text-gray-900 text-base leading-tight">Poop</h3>
                {isCompleted && (
                  <div className="w-5 h-5 rounded-full bg-green-500 flex items-center justify-center">
                    <Check className="h-3 w-3 text-white" />
                  </div>
                )}
              </div>
              {/* Only show rating information if completed */}
              {isCompleted && value && (
                <div className="flex items-center gap-2">
                  <span className={cn("text-sm font-medium transition-colors", getRatingColor(value))}>
                    {getRatingText(value)}
                  </span>
                  <div className="flex gap-0.5">
                    {[...Array(5)].map((_, i) => (
                      <div
                        key={i}
                        className={cn(
                          "w-1.5 h-1.5 rounded-full",
                          i < value ? "bg-current" : "bg-gray-200"
                        )}
                        style={{ color: i < value ? getRatingColor(value).replace('text-', '') : undefined }}
                      />
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
          
          {/* Enhanced Right Side Action */}
          <div className="flex items-center">
            {!isCompleted ? (
              <Button
                variant="outline"
                size="sm"
                className="h-8 px-4 text-xs font-medium text-brand-primary border-brand-primary hover:bg-brand-primary hover:text-white transition-colors duration-200 shadow-sm"
                onClick={(e) => {
                  e.stopPropagation();
                  onClick?.();
                }}
              >
                Check-in
              </Button>
            ) : (
              <ChevronRight className="h-5 w-5 text-gray-400" />
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
