
import React, { useState, useEffect } from "react";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Star, ArrowLeft, CheckCircle } from "lucide-react";
import { cn } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";

interface SimplifiedCheckInModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  shapeRatings: {
    sleep: number;
    hydration: number;
    activity: number;
    poop: number;
    eating: number;
  };
  onRate: (category: string, rating: number) => void;
  petName: string;
}

type ShapeCategory = {
  key: keyof SimplifiedCheckInModalProps['shapeRatings'];
  title: string;
  letter: string;
};

const SHAPE_CATEGORIES: ShapeCategory[] = [
  { key: 'sleep', title: 'Sleep', letter: 'S' },
  { key: 'hydration', title: 'Hydration', letter: 'H' },
  { key: 'activity', title: 'Activity', letter: 'A' },
  { key: 'eating', title: 'Eating', letter: 'E' }
];

export const SimplifiedCheckInModal: React.FC<SimplifiedCheckInModalProps> = ({
  open,
  onOpenChange,
  shapeRatings,
  onRate,
  petName
}) => {
  const { toast } = useToast();
  const [currentCategoryIndex, setCurrentCategoryIndex] = useState(0);
  const [selectedRating, setSelectedRating] = useState(0);

  // Get uncompleted categories (excluding poop)
  const uncompletedCategories = SHAPE_CATEGORIES.filter(category => 
    shapeRatings[category.key] === 0
  );

  const currentCategory = uncompletedCategories[currentCategoryIndex];
  const totalCategories = uncompletedCategories.length;
  const progress = totalCategories > 0 ? ((currentCategoryIndex + 1) / totalCategories) * 100 : 100;

  // Reset when modal opens
  useEffect(() => {
    if (open) {
      setCurrentCategoryIndex(0);
      setSelectedRating(0);
    }
  }, [open]);

  // Update selected rating when category changes
  useEffect(() => {
    if (currentCategory) {
      setSelectedRating(0);
    }
  }, [currentCategory]);

  const handleStarClick = (rating: number) => {
    setSelectedRating(rating);
  };

  const handleNext = () => {
    if (!currentCategory || selectedRating === 0) return;
    
    // Save the rating
    onRate(currentCategory.key, selectedRating);
    
    if (currentCategoryIndex < totalCategories - 1) {
      // Move to next category
      setCurrentCategoryIndex(prev => prev + 1);
      setSelectedRating(0);
    } else {
      // Complete check-in
      onOpenChange(false);
      toast({
        title: "Check-in complete! 🎉",
        description: `${petName}'s SHAPE check-in is now complete.`,
      });
    }
  };

  const handleBack = () => {
    if (currentCategoryIndex > 0) {
      setCurrentCategoryIndex(prev => prev - 1);
      setSelectedRating(0);
    }
  };

  // Don't render if no categories to complete
  if (!currentCategory || totalCategories === 0) {
    return null;
  }

  const isLastCategory = currentCategoryIndex === totalCategories - 1;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogTitle className="sr-only">
          SHAPE Check-in: {currentCategory.title}
        </DialogTitle>
        
        {/* Header */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-between mb-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBack}
              disabled={currentCategoryIndex === 0}
              className="p-2"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            
            <div className="flex-1 mx-4">
              <Progress value={progress} className="h-2" />
              <div className="text-xs text-gray-500 mt-1">
                {currentCategoryIndex + 1} of {totalCategories}
              </div>
            </div>
            
            <div className="w-10" /> {/* Spacer */}
          </div>

          <div className="flex items-center justify-center gap-3">
            <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-brand-primary to-brand-secondary text-white flex items-center justify-center font-bold text-lg">
              {currentCategory.letter}
            </div>
            <h2 className="text-2xl font-bold text-gray-900">{currentCategory.title}</h2>
          </div>
        </div>

        {/* Star Rating */}
        <div className="py-6">
          <div className="flex justify-center gap-2 mb-6">
            {[1, 2, 3, 4, 5].map((rating) => (
              <Button
                key={rating}
                variant="ghost"
                size="sm"
                className="p-3 hover:bg-gray-50 rounded-full"
                onClick={() => handleStarClick(rating)}
              >
                <Star
                  className={cn(
                    "h-10 w-10 transition-colors",
                    selectedRating >= rating
                      ? "fill-yellow-400 text-yellow-400"
                      : "text-gray-300 hover:text-yellow-300"
                  )}
                />
              </Button>
            ))}
          </div>

          {selectedRating === 0 && (
            <p className="text-center text-gray-500 text-sm">
              Rate {currentCategory.title.toLowerCase()} from 1-5 stars
            </p>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3 pt-4 border-t border-gray-100">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="flex-1"
          >
            Cancel
          </Button>
          
          <Button
            onClick={handleNext}
            disabled={selectedRating === 0}
            className="flex-1 bg-gradient-to-r from-brand-primary to-brand-secondary text-white"
          >
            {isLastCategory ? "Complete" : "Next"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
