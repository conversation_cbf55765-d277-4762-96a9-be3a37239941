
export const getTimeBasedGreeting = (): string => {
  const hour = new Date().getHours();
  // Extract user name from pet name context - assuming pet owner's name could be derived
  // For now, we'll use a generic approach or could be passed as a prop
  const userName = "Dan"; // This could be passed as a prop or derived from auth context
  
  if (hour < 12) return `Good morning, ${userName}!`;
  if (hour < 17) return `Good afternoon, ${userName}!`;
  return `Good evening, ${userName}!`;
};

export const getSmartCTAText = (
  hasAnyEngagement: boolean,
  completedCategories: number,
  totalCategories: number
): string => {
  if (!hasAnyEngagement) return "Start Today's Check-in";
  if (completedCategories === 0) return "Begin SHAPE Check-in";
  if (completedCategories < totalCategories) {
    const remaining = totalCategories - completedCategories;
    return `Continue • ${remaining} left`;
  }
  return "Review Today's Progress";
};

export const formatPetAge = (petInfo: string): string => {
  const monthMatch = petInfo.match(/(\d+)\s+months?\s+old/i);
  if (monthMatch) {
    const months = parseInt(monthMatch[1]);
    if (months >= 12) {
      const years = months / 12;
      const roundedYears = Math.round(years * 10) / 10;
      return petInfo.replace(/\d+\s+months?\s+old/i, `${roundedYears} year${roundedYears !== 1 ? 's' : ''} old`);
    }
  }
  return petInfo;
};
