
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Check, ChevronRight, Clock } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";

interface EnhancedPoopRatingCardProps {
  value?: number;
  onClick?: () => void;
  petName: string;
}

export const EnhancedPoopRatingCard: React.FC<EnhancedPoopRatingCardProps> = ({
  value,
  onClick,
  petName
}) => {
  const getRatingText = (rating: number) => {
    if (rating === 1) return "Poor";
    if (rating === 2) return "Fair";
    if (rating === 3) return "Good";
    if (rating === 4) return "Very Good";
    return "Excellent";
  };

  const getRatingColor = (rating: number) => {
    if (rating === 1) return "bg-red-100 text-red-700 border-red-200";
    if (rating === 2) return "bg-orange-100 text-orange-700 border-orange-200";
    if (rating === 3) return "bg-yellow-100 text-yellow-700 border-yellow-200";
    if (rating === 4) return "bg-blue-100 text-blue-700 border-blue-200";
    return "bg-green-100 text-green-700 border-green-200";
  };

  const isCompleted = value !== undefined && value > 0;

  return (
    <Card 
      className={cn(
        "cursor-pointer transition-all duration-300 shadow-sm hover:shadow-lg",
        "hover:scale-[1.02] active:scale-[0.98]",
        "min-h-[80px] overflow-hidden border-0"
      )}
      onClick={onClick}
    >
      <div className={cn(
        "relative h-full w-full",
        isCompleted 
          ? "bg-gradient-to-r from-amber-50/30 to-yellow-50/10" 
          : "bg-white"
      )}>
        {isCompleted && (
          <div className="absolute inset-0 opacity-5">
            <div className="absolute inset-0 bg-gradient-to-br from-transparent via-white to-transparent transform rotate-12 scale-150" />
          </div>
        )}
        
        <CardContent className="p-4 relative z-10 h-full">
          {/* Single Row Layout */}
          <div className="flex items-center justify-between gap-4 h-full">
            {/* Left Section: Icon + Content */}
            <div className="flex items-center gap-4 flex-1 min-w-0">
              {/* Letter Circle */}
              <div className={cn(
                "w-12 h-12 rounded-xl flex items-center justify-center font-bold text-lg shadow-sm transition-all duration-300 flex-shrink-0",
                isCompleted 
                  ? "bg-gradient-to-br from-amber-500 to-yellow-600 text-white shadow-md" 
                  : "bg-gray-50 text-gray-400 border border-gray-200"
              )}>
                P
              </div>
              
              {/* Content Section */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-1">
                  <h3 className="font-semibold text-gray-900 text-base leading-tight">Poop</h3>
                </div>
                
                {/* Status and Rating */}
                {isCompleted && value ? (
                  <div className="flex items-center gap-2">
                    <div className={cn(
                      "px-2 py-1 rounded-full text-xs font-medium border transition-colors",
                      getRatingColor(value)
                    )}>
                      {getRatingText(value)}
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center gap-2 text-gray-500">
                    <Clock className="h-3 w-3" />
                    <span className="text-xs">Not checked today</span>
                  </div>
                )}
              </div>
            </div>
            
            {/* Right Section: Status + Action */}
            <div className="flex items-center gap-3 flex-shrink-0">
              {/* Completion Status */}
              {isCompleted && (
                <div className="flex items-center gap-1">
                  <div className="w-5 h-5 rounded-full bg-green-500 flex items-center justify-center">
                    <Check className="h-3 w-3 text-white" />
                  </div>
                  <span className="text-xs text-green-600 font-medium hidden sm:inline">Done</span>
                </div>
              )}
              
              {/* Action Button or Edit Indicator */}
              {!isCompleted ? (
                <Button
                  variant="outline"
                  size="sm"
                  className={cn(
                    "h-9 px-3 text-sm font-medium transition-all duration-200 shadow-sm",
                    "hover:shadow-md hover:scale-105 active:scale-95",
                    "text-amber-600 border-current hover:bg-current hover:text-white"
                  )}
                  onClick={(e) => {
                    e.stopPropagation();
                    onClick?.();
                  }}
                >
                  Check-in
                </Button>
              ) : (
                <div className="flex items-center gap-1 text-gray-400">
                  <span className="text-xs text-gray-500 hidden sm:inline">Edit</span>
                  <ChevronRight className="h-4 w-4" />
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </div>
    </Card>
  );
};
