
import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { ArrowLef<PERSON>, ArrowRight, CheckCircle } from "lucide-react";
import { PoopLog } from "@/types/poop";
import { ShapeRatingStep } from "./components/ShapeRatingStep";
import { PoopCheckInStep } from "./components/PoopCheckInStep";
import { SupplementAdherenceStep } from "./SupplementAdherenceStep";
import { useSupplementAdherence } from "@/hooks/useSupplementAdherence";
import { useToast } from "@/hooks/use-toast";

interface UnifiedCheckInModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  shapeRatings: {
    sleep: number;
    hydration: number;
    activity: number;
    poop: number;
    eating: number;
  };
  onRate: (category: string, rating: number) => void;
  onAddPoopLog: (log: PoopLog) => void;
  petName: string;
  petId: string;
  existingPoopLog?: PoopLog;
}

type StepType = 'sleep' | 'hydration' | 'activity' | 'eating' | 'supplements' | 'poop';

const STEPS: Array<{
  key: StepType;
  title: string;
  description: string;
}> = [
  { key: 'sleep', title: 'Sleep', description: 'How was your pet\'s sleep quality?' },
  { key: 'hydration', title: 'Hydration', description: 'How was your pet\'s water intake?' },
  { key: 'activity', title: 'Activity', description: 'How was your pet\'s activity level?' },
  { key: 'eating', title: 'Eating', description: 'How was your pet\'s appetite?' },
  { key: 'supplements', title: 'Supplements', description: 'Medications and supplements' },
  { key: 'poop', title: 'Poop', description: 'Bathroom habits and stool quality' }
];

export const UnifiedCheckInModal: React.FC<UnifiedCheckInModalProps> = ({
  open,
  onOpenChange,
  shapeRatings,
  onRate,
  onAddPoopLog,
  petName,
  petId,
  existingPoopLog
}) => {
  const { toast } = useToast();
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<Set<StepType>>(new Set());
  
  const { supplements, adherenceRate, toggleSupplement } = useSupplementAdherence(petId);

  const currentStep = STEPS[currentStepIndex];
  const totalSteps = STEPS.length;
  const progress = ((currentStepIndex + 1) / totalSteps) * 100;

  // Reset state when modal opens
  useEffect(() => {
    if (open) {
      setCurrentStepIndex(0);
      // Mark already completed SHAPE ratings
      const completed = new Set<StepType>();
      Object.entries(shapeRatings).forEach(([key, value]) => {
        if (value > 0) {
          completed.add(key as StepType);
        }
      });
      if (existingPoopLog?.completed) {
        completed.add('poop');
      }
      // Check supplement adherence
      if (supplements.length > 0 && supplements.every(s => s.given)) {
        completed.add('supplements');
      }
      setCompletedSteps(completed);
    }
  }, [open, shapeRatings, existingPoopLog, supplements]);

  const handleNext = () => {
    if (currentStepIndex < totalSteps - 1) {
      setCurrentStepIndex(prev => prev + 1);
    }
  };

  const handleBack = () => {
    if (currentStepIndex > 0) {
      setCurrentStepIndex(prev => prev - 1);
    }
  };

  const handleStepComplete = (stepKey: StepType) => {
    setCompletedSteps(prev => new Set([...prev, stepKey]));
    
    // Auto-advance to next step if not the last one
    if (currentStepIndex < totalSteps - 1) {
      setTimeout(() => {
        handleNext();
      }, 800);
    } else {
      // All steps completed
      setTimeout(() => {
        onOpenChange(false);
        toast({
          title: "Daily check-in complete! 🎉",
          description: `${petName}'s complete health check-in is now done.`,
          duration: 3000,
        });
      }, 800);
    }
  };

  const handleRating = (rating: number) => {
    if (currentStep.key !== 'poop' && currentStep.key !== 'supplements') {
      onRate(currentStep.key, rating);
      handleStepComplete(currentStep.key);
    }
  };

  const handlePoopComplete = (log: PoopLog) => {
    onAddPoopLog(log);
    handleStepComplete('poop');
  };

  const handleSupplementToggle = async (supplementId: string) => {
    await toggleSupplement(supplementId);
    
    // Check if all supplements are now given
    const updatedSupplements = supplements.map(s => 
      s.id === supplementId ? { ...s, given: !s.given } : s
    );
    
    if (updatedSupplements.length > 0 && updatedSupplements.every(s => s.given)) {
      handleStepComplete('supplements');
    }
  };

  const isStepCompleted = (stepKey: StepType) => completedSteps.has(stepKey);
  const canProceed = isStepCompleted(currentStep.key);

  const renderCurrentStep = () => {
    switch (currentStep.key) {
      case 'poop':
        return (
          <PoopCheckInStep
            onComplete={handlePoopComplete}
            petName={petName}
            existingLog={existingPoopLog}
          />
        );
      case 'supplements':
        return (
          <SupplementAdherenceStep
            supplements={supplements}
            onToggleSupplement={handleSupplementToggle}
            petName={petName}
          />
        );
      default:
        return (
          <ShapeRatingStep
            category={currentStep.key}
            title={currentStep.title}
            description={currentStep.description}
            currentRating={shapeRatings[currentStep.key as keyof typeof shapeRatings] || 0}
            onRate={handleRating}
            petName={petName}
          />
        );
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-lg max-h-[90vh] overflow-y-auto">
        <DialogTitle className="sr-only">
          Daily Check-in: {currentStep.title}
        </DialogTitle>
        
        {/* Header with Progress */}
        <div className="sticky top-0 bg-white border-b pb-4 mb-4">
          <div className="flex items-center justify-between mb-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBack}
              disabled={currentStepIndex === 0}
              className="p-2"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            
            <div className="flex-1 mx-4">
              <Progress value={progress} className="h-2" />
              <div className="text-xs text-gray-500 mt-1 text-center">
                Step {currentStepIndex + 1} of {totalSteps}
              </div>
            </div>
            
            <div className="w-8"> {/* Spacer for symmetry */}
              {isStepCompleted(currentStep.key) && (
                <CheckCircle className="h-5 w-5 text-green-500" />
              )}
            </div>
          </div>
        </div>

        {/* Current Step Content */}
        <div className="space-y-6">
          {renderCurrentStep()}
        </div>

        {/* Navigation */}
        <div className="flex gap-3 pt-4 border-t border-gray-100 sticky bottom-0 bg-white">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="flex-1"
          >
            Cancel
          </Button>
          
          {currentStepIndex < totalSteps - 1 ? (
            <Button
              onClick={handleNext}
              disabled={!canProceed}
              className="flex-1"
            >
              <ArrowRight className="h-4 w-4 mr-2" />
              Next Step
            </Button>
          ) : (
            <Button
              onClick={() => onOpenChange(false)}
              disabled={!canProceed}
              className="flex-1 bg-gradient-to-r from-brand-primary to-brand-secondary text-white"
            >
              Complete Check-in
            </Button>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
