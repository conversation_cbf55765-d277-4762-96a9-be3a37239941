
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Check, ChevronDown, ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { HabitTrackerScoopCard } from "@/components/dashboard/poop/HabitTrackerScoopCard";
import { PoopLog } from "@/types/poop";

interface ExpandablePoopCardProps {
  value?: number;
  onClick: () => void;
  expanded: boolean;
  poopLogs: PoopLog[];
  onAddPoopLog: (log: PoopLog) => void;
  petName: string;
}

export const ExpandablePoopCard: React.FC<ExpandablePoopCardProps> = ({
  value,
  onClick,
  expanded,
  poopLogs,
  onAddPoopLog,
  petName
}) => {
  const getRatingText = (rating?: number) => {
    if (!rating) return "Not rated";
    if (rating === 1) return "Poor";
    if (rating === 2) return "Fair";
    if (rating === 3) return "Good";
    if (rating === 4) return "Very Good";
    return "Excellent";
  };

  // Get today's date in YYYY-MM-DD format (local timezone)
  const getTodayDateString = () => {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  const today = getTodayDateString();
  const todayLog = poopLogs.find(log => log.date === today);
  const isCompleted = !!todayLog;

  const handleCheckInClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onClick(); // This will expand the card
  };

  return (
    <Card className="border-gray-100 bg-white">
      {/* Collapsed Header */}
      <CardContent className="p-3">
        <div 
          className="flex items-center justify-between cursor-pointer"
          onClick={onClick}
        >
          <div className="flex items-center gap-3 flex-1">
            {/* Category Circle with P */}
            <div className={cn(
              "w-10 h-10 rounded-lg flex items-center justify-center font-bold text-lg",
              isCompleted 
                ? "bg-brand-primary text-white" 
                : "bg-gray-200 text-gray-400"
            )}>
              P
            </div>
            
            <div className="flex-1 min-w-0">
              {/* Title and Status */}
              <div className="flex items-center gap-2 mb-1">
                <h3 className="font-semibold text-gray-900 text-base">Poop</h3>
                <span className={cn("text-sm font-medium", 
                  value === 5 ? "text-brand-primary" : 
                  value === 4 ? "text-brand-secondary" :
                  value === 3 ? "text-yellow-600" :
                  value === 2 ? "text-orange-600" :
                  value === 1 ? "text-red-600" : "text-gray-500"
                )}>
                  {getRatingText(value)}
                </span>
              </div>
            </div>
          </div>
          
          {/* Right side indicators */}
          <div className="flex items-center gap-2">
            {isCompleted ? (
              <>
                <div className="w-6 h-6 rounded-full bg-brand-primary flex items-center justify-center">
                  <Check className="h-4 w-4 text-white" />
                </div>
                {/* Expand/Collapse Icon - only show when completed */}
                {expanded ? (
                  <ChevronDown className="h-4 w-4 text-gray-400" />
                ) : (
                  <ChevronRight className="h-4 w-4 text-gray-400" />
                )}
              </>
            ) : (
              <Button
                variant="outline"
                size="sm"
                className="h-7 px-3 text-xs text-brand-primary border-brand-primary hover:bg-brand-primary hover:text-white"
                onClick={handleCheckInClick}
              >
                Check-in
              </Button>
            )}
          </div>
        </div>
      </CardContent>

      {/* Expanded Content */}
      {expanded && (
        <CardContent className="px-3 pb-3 pt-0">
          <div className="border-t border-gray-100 pt-4">
            <HabitTrackerScoopCard
              poopLogs={poopLogs}
              onAddLog={onAddPoopLog}
              petName={petName}
            />
          </div>
        </CardContent>
      )}
    </Card>
  );
};
