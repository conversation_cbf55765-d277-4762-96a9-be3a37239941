
import React from "react";
import { EnhancedShapeRatingCard } from "./EnhancedShapeRatingCard";
import { EnhancedPoopRatingCard } from "./EnhancedPoopRatingCard";

type ShapeRatings = {
  sleep: number;
  hydration: number;
  activity: number;
  poop: number;
  eating: number;
};

interface EnhancedShapeCategoriesListProps {
  shapeRatings: ShapeRatings;
  onCardClick: (category: keyof ShapeRatings, title: string, letter: string, description: string) => void;
  onPoopCardClick: () => void;
  petName: string;
}

export const EnhancedShapeCategoriesList: React.FC<EnhancedShapeCategoriesListProps> = ({
  shapeRatings,
  onCardClick,
  onPoopCardClick,
  petName
}) => {
  const shapeCategories = [
    {
      key: 'sleep' as keyof ShapeRatings,
      title: "Sleep",
      letter: "S",
      description: "Quality of rest and sleep patterns",
      color: "from-indigo-500 to-purple-600",
      lightColor: "from-indigo-50 to-purple-50",
      textColor: "text-indigo-700"
    },
    {
      key: 'hydration' as keyof ShapeRatings,
      title: "Hydration", 
      letter: "H",
      description: "Water intake and hydration levels",
      color: "from-blue-500 to-cyan-600",
      lightColor: "from-blue-50 to-cyan-50",
      textColor: "text-blue-700"
    },
    {
      key: 'activity' as keyof ShapeRatings,
      title: "Activity",
      letter: "A",
      description: "Exercise and physical activity",
      color: "from-orange-500 to-red-600",
      lightColor: "from-orange-50 to-red-50", 
      textColor: "text-orange-700"
    }
  ];

  // Helper function to get value only if it's completed (> 0)
  const getCompletedValue = (rating: number): number | undefined => {
    return rating > 0 ? rating : undefined;
  };

  return (
    <div className="px-4 md:px-6 md:py-6">
      {/* Mobile-optimized grid layout with full-width cards */}
      <div className="space-y-3 md:space-y-4">
        {shapeCategories.map((category) => (
          <EnhancedShapeRatingCard
            key={category.key}
            title={category.title}
            letter={category.letter}
            value={getCompletedValue(shapeRatings[category.key])}
            onClick={() => onCardClick(category.key, category.title, category.letter, category.description)}
            description={category.description}
            lastUpdated={new Date()}
            gradientColors={category.color}
            lightGradientColors={category.lightColor}
            textColor={category.textColor}
          />
        ))}
        
        {/* Enhanced Poop Card */}
        <EnhancedPoopRatingCard
          value={getCompletedValue(shapeRatings.poop)}
          onClick={onPoopCardClick}
          petName={petName}
        />

        {/* Enhanced Eating Card */}
        <EnhancedShapeRatingCard
          key="eating"
          title="Eating"
          letter="E"
          value={getCompletedValue(shapeRatings.eating)}
          onClick={() => onCardClick('eating', 'Eating', 'E', 'Appetite and food intake')}
          description="Appetite and food intake"
          lastUpdated={new Date()}
          gradientColors="from-green-500 to-emerald-600"
          lightGradientColors="from-green-50 to-emerald-50"
          textColor="text-green-700"
        />
      </div>

      {/* Quick Tips Section */}
      <div className="mt-6 mx-4 md:mx-0 p-4 bg-gray-50 rounded-xl">
        <h4 className="text-sm font-semibold text-gray-700 mb-2">💡 Quick Tips</h4>
        <p className="text-xs text-gray-600 leading-relaxed">
          Track each category daily for the most accurate health insights. Consistent logging helps our AI provide better recommendations for {petName}.
        </p>
      </div>
    </div>
  );
};
