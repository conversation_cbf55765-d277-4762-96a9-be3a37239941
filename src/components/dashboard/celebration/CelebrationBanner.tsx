
import React, { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Share2, <PERSON>, <PERSON>rkles, Loader2 } from "lucide-react";
import { usePetData } from "@/context/PetContext";
import { shareAchievement, triggerHapticFeedback } from "@/utils/shareUtils";

interface CelebrationBannerProps {
  petName: string;
  shapeScore: number;
  streak: number;
  onShare: () => void;
}

export const CelebrationBanner: React.FC<CelebrationBannerProps> = ({ 
  petName, 
  shapeScore, 
  streak, 
  onShare 
}) => {
  const { petData } = usePetData();
  const [isSharing, setIsSharing] = useState(false);

  const handleShare = async () => {
    setIsSharing(true);
    triggerHapticFeedback();
    
    try {
      await shareAchievement({
        petName,
        shapeScore,
        streak
      });
      onShare(); // Call the original onShare callback
    } catch (error) {
      console.error('Share error:', error);
    } finally {
      setIsSharing(false);
    }
  };

  return (
    <Card className="relative overflow-hidden bg-gradient-to-br from-green-400 via-green-500 to-green-600 text-white border-0 shadow-lg">
      {/* Animated background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-4 left-4 w-8 h-8 bg-white/20 rounded-full animate-pulse" />
        <div className="absolute top-8 right-8 w-6 h-6 bg-white/30 rounded-full animate-pulse delay-300" />
        <div className="absolute bottom-6 left-8 w-4 h-4 bg-white/25 rounded-full animate-pulse delay-700" />
        <div className="absolute bottom-4 right-12 w-10 h-10 bg-white/15 rounded-full animate-pulse delay-500" />
      </div>

      <div className="relative p-4 md:p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            {/* Pet Avatar */}
            <Avatar className="w-16 h-16 border-4 border-white/30">
              <AvatarImage src={petData.photo_url} alt={petName} />
              <AvatarFallback className="bg-white/20 text-white font-bold text-xl">
                {petName.charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            
            <div>
              <div className="flex items-center gap-2 mb-1">
                <Trophy className="w-5 h-5 text-yellow-300" />
                <h2 className="text-xl md:text-2xl font-bold">Fantastic Work!</h2>
              </div>
              <p className="text-white/90 text-sm md:text-base">
                {petName} completed today's SHAPE check-in with a score of {shapeScore}!
              </p>
              {streak > 0 && (
                <div className="flex items-center gap-1 mt-1">
                  <Sparkles className="w-4 h-4 text-yellow-300" />
                  <span className="text-white/90 text-sm">
                    {streak} day streak! Keep it up!
                  </span>
                </div>
              )}
            </div>
          </div>
          
          <Button 
            variant="ghost" 
            size="sm"
            onClick={handleShare}
            disabled={isSharing}
            className="bg-white/20 hover:bg-white/30 text-white border-white/30 hidden sm:flex"
          >
            {isSharing ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Share2 className="w-4 h-4 mr-2" />
            )}
            {isSharing ? 'Sharing...' : 'Share'}
          </Button>
        </div>
        
        {/* Mobile share button */}
        <div className="sm:hidden mt-4">
          <Button 
            variant="ghost" 
            size="sm"
            onClick={handleShare}
            disabled={isSharing}
            className="w-full bg-white/20 hover:bg-white/30 text-white border-white/30 transition-all duration-200 active:scale-95"
          >
            {isSharing ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Share2 className="w-4 h-4 mr-2" />
            )}
            {isSharing ? 'Sharing Achievement...' : 'Share Achievement'}
          </Button>
        </div>
      </div>
    </Card>
  );
};
