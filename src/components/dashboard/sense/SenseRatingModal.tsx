
import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Star } from "lucide-react";
import { cn } from "@/lib/utils";

interface SenseRatingModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  icon: React.ElementType;
  currentValue?: number;
  onRate: (rating: number) => void;
  description?: string;
}

export const SenseRatingModal: React.FC<SenseRatingModalProps> = ({
  open,
  onOpenChange,
  title,
  icon: Icon,
  currentValue,
  onRate,
  description
}) => {
  const [tempRating, setTempRating] = useState(currentValue || 0);

  const handleSave = () => {
    onRate(tempRating);
    onOpenChange(false);
  };

  const getRatingText = (rating: number) => {
    if (rating === 1) return "Poor";
    if (rating === 2) return "Fair";
    if (rating === 3) return "Good";
    if (rating === 4) return "Very Good";
    return "Excellent";
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <div className="mb-4">
            <DialogTitle className="text-xl text-gray-900">{title}</DialogTitle>
            {description && (
              <p className="text-sm text-gray-600 mt-1">
                {description}
              </p>
            )}
          </div>
        </DialogHeader>

        <div className="space-y-6">
          <div className="text-center">
            <div className="flex gap-2 justify-center mb-4">
              {[1, 2, 3, 4, 5].map((rating) => (
                <button
                  key={rating}
                  onClick={() => setTempRating(rating)}
                  className="p-3 hover:scale-110 transition-all duration-300 ease-out rounded-full hover:bg-gray-50"
                >
                  <Star
                    className={cn(
                      "h-10 w-10 transition-all duration-300 ease-out",
                      tempRating >= rating
                        ? "fill-yellow-400 text-yellow-400 scale-110 drop-shadow-sm"
                        : "text-gray-300 hover:text-yellow-400 hover:scale-105"
                    )}
                  />
                </button>
              ))}
            </div>
            {tempRating > 0 && (
              <p className="text-lg font-medium text-center text-gray-900 transition-colors duration-300">
                {getRatingText(tempRating)}
              </p>
            )}
          </div>

          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              onClick={handleSave}
              disabled={tempRating === 0}
              className="flex-1 bg-brand-primary hover:bg-brand-dark text-white"
            >
              Save Rating
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
