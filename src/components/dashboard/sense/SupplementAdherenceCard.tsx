
import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Pill, CheckCircle, AlertCircle } from "lucide-react";
import { cn } from "@/lib/utils";

interface Supplement {
  id: string;
  name: string;
  dosage: string;
  timing: string;
  type: 'supplement' | 'medication';
  given: boolean;
}

interface SupplementAdherenceCardProps {
  supplements: Supplement[];
  adherenceRate: number;
  onToggleSupplement: (supplementId: string) => void;
}

export const SupplementAdherenceCard: React.FC<SupplementAdherenceCardProps> = ({
  supplements,
  adherenceRate,
  onToggleSupplement
}) => {
  const getAdherenceColor = (rate: number) => {
    if (rate >= 80) return "text-green-600 bg-green-50";
    if (rate >= 60) return "text-yellow-600 bg-yellow-50";
    return "text-red-600 bg-red-50";
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold flex items-center gap-2">
            <Pill className="h-5 w-5 text-brand-primary" />
            Supplement Adherence
          </CardTitle>
          <Badge className={cn("font-semibold", getAdherenceColor(adherenceRate))}>
            {adherenceRate}%
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        {supplements.map((supplement) => (
          <div
            key={supplement.id}
            className="flex items-center justify-between p-3 border rounded-lg"
          >
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <span className="font-medium text-sm truncate">{supplement.name}</span>
                <Badge variant="outline" className="text-xs whitespace-nowrap flex-shrink-0">
                  {supplement.timing}
                </Badge>
              </div>
              <p className="text-xs text-muted-foreground">{supplement.dosage}</p>
            </div>
            
            <Button
              variant={supplement.given ? "default" : "outline"}
              size="sm"
              onClick={() => onToggleSupplement(supplement.id)}
              className={cn(
                "ml-3 flex-shrink-0",
                supplement.given
                  ? "bg-green-100 text-green-700 hover:bg-green-200"
                  : "hover:bg-gray-50"
              )}
            >
              {supplement.given ? (
                <CheckCircle className="h-4 w-4" />
              ) : (
                <AlertCircle className="h-4 w-4" />
              )}
              <span className="ml-1 text-xs">
                {supplement.given ? "Given" : "Give"}
              </span>
            </Button>
          </div>
        ))}
        
        <div className="pt-3 border-t">
          <p className="text-xs text-muted-foreground text-center">
            Tap "Give" when you administer each supplement
          </p>
        </div>
      </CardContent>
    </Card>
  );
};
