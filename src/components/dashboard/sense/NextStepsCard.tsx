
import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ArrowRight, Calendar, MessageSquare, TrendingUp } from "lucide-react";
import { PersonalizedRecommendation } from "@/types/sense";
import { useNavigate } from "react-router-dom";

interface NextStepsCardProps {
  recommendations: PersonalizedRecommendation[];
}

export const NextStepsCard: React.FC<NextStepsCardProps> = ({
  recommendations
}) => {
  const navigate = useNavigate();

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-700';
      case 'medium': return 'bg-yellow-100 text-yellow-700';
      default: return 'bg-blue-100 text-blue-700';
    }
  };

  const getPriorityIcon = (category: string) => {
    switch (category) {
      case 'health': return Calendar;
      case 'coaching': return MessageSquare;
      case 'activity': return TrendingUp;
      default: return ArrowRight;
    }
  };

  const handleActionClick = (recommendation: PersonalizedRecommendation) => {
    // Navigate to AI Coach page for coaching-related recommendations
    if (recommendation.category === 'coaching' || recommendation.action.toLowerCase().includes('chat')) {
      navigate('/coach');
    }
    // For other actions, you could add different navigation logic here
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-semibold">Next Steps</CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {recommendations.slice(0, 3).map((rec) => {
          const Icon = getPriorityIcon(rec.category);
          
          return (
            <div
              key={rec.id}
              className="flex items-start gap-3 p-3 border rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="bg-brand-light p-2 rounded-full flex-shrink-0">
                <Icon className="h-4 w-4 text-brand-primary" />
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <h4 className="font-medium text-sm truncate">{rec.title}</h4>
                  <Badge
                    variant="outline"
                    className={`text-xs ${getPriorityColor(rec.priority)}`}
                  >
                    {rec.priority}
                  </Badge>
                </div>
                <p className="text-xs text-muted-foreground mb-2">
                  {rec.description}
                </p>
                <Button 
                  variant="link" 
                  size="sm" 
                  className="p-0 h-auto text-xs"
                  onClick={() => handleActionClick(rec)}
                >
                  {rec.action}
                  <ArrowRight className="ml-1 h-3 w-3" />
                </Button>
              </div>
            </div>
          );
        })}
        
        {recommendations.length > 3 && (
          <div className="pt-3 border-t">
            <Button variant="outline" size="sm" className="w-full">
              View All Recommendations ({recommendations.length})
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
