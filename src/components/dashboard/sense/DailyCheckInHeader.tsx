
import React from "react";
import { Badge } from "@/components/ui/badge";
import { Flame, CheckCircle } from "lucide-react";

interface DailyCheckInHeaderProps {
  petName: string;
  petInfo: string;
  streakCount: number;
  todayCompleted: boolean;
  overallScore?: number;
}

export const DailyCheckInHeader: React.FC<DailyCheckInHeaderProps> = ({
  petName,
  petInfo,
  streakCount,
  todayCompleted,
  overallScore
}) => {
  const getScoreColor = (score?: number) => {
    if (!score) return "bg-gray-100 text-gray-600";
    if (score >= 4.5) return "bg-green-100 text-green-700";
    if (score >= 3.5) return "bg-blue-100 text-blue-700";
    if (score >= 2.5) return "bg-yellow-100 text-yellow-700";
    return "bg-red-100 text-red-700";
  };

  const currentDate = new Date().toLocaleDateString('en-US', { 
    month: 'short',
    day: 'numeric'
  });

  return (
    <div className="flex items-center justify-between py-3 px-1 bg-gray-50/50 rounded-lg border border-gray-100">
      {/* Left: Pet info and date */}
      <div className="flex items-center gap-3 text-sm text-gray-600">
        <span className="font-medium text-gray-900">{petName}</span>
        <span>•</span>
        <span>Bernedoodle</span>
        <span>•</span>
        <span>{currentDate}</span>
      </div>
      
      {/* Right: Metrics */}
      <div className="flex items-center gap-4">
        {/* Streak */}
        <div className="flex items-center gap-1">
          <Flame className="h-4 w-4 text-orange-500" />
          <span className="text-sm font-semibold text-gray-900">{streakCount}</span>
          <span className="text-xs text-gray-500">day streak</span>
        </div>
        
        {/* Score */}
        {overallScore && (
          <Badge variant="secondary" className={`text-xs ${getScoreColor(overallScore)}`}>
            {overallScore.toFixed(1)}/5
          </Badge>
        )}
        
        {/* Completion Status */}
        <div className="flex items-center gap-1">
          {todayCompleted ? (
            <>
              <CheckCircle className="w-4 h-4 text-green-500" />
              <span className="text-xs text-green-700 font-medium">Complete</span>
            </>
          ) : (
            <span className="text-xs text-gray-500">In Progress</span>
          )}
        </div>
      </div>
    </div>
  );
};
