
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Check, ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";

interface SenseRatingCardProps {
  title: string;
  icon: React.ElementType;
  value?: number;
  onRate: (rating: number) => void;
  onClick?: () => void;
  lastUpdated?: Date;
  description?: string;
  color?: string;
}

export const SenseRatingCard: React.FC<SenseRatingCardProps> = ({
  title,
  icon: Icon,
  value,
  onClick,
  color = "bg-brand-primary"
}) => {
  const getRatingText = (rating?: number) => {
    if (!rating) return "Not rated";
    if (rating === 1) return "Poor";
    if (rating === 2) return "Fair";
    if (rating === 3) return "Good";
    if (rating === 4) return "Very Good";
    return "Excellent";
  };

  const isCompleted = value && value > 0;

  return (
    <Card 
      className="cursor-pointer hover:shadow-sm transition-shadow border-gray-100 bg-white"
      onClick={onClick}
    >
      <CardContent className="p-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3 flex-1">
            {/* Category Circle with Letter */}
            <div className={cn(
              "w-10 h-10 rounded-lg flex items-center justify-center font-bold text-lg",
              isCompleted 
                ? "bg-brand-primary text-white" 
                : "bg-gray-200 text-gray-400"
            )}>
              {title.charAt(0)}
            </div>
            
            <div className="flex-1 min-w-0">
              {/* Title and Status */}
              <div className="flex items-center gap-2 mb-1">
                <h3 className="font-semibold text-gray-900 text-base">{title}</h3>
                <span className={cn("text-sm font-medium", 
                  value === 5 ? "text-brand-primary" : 
                  value === 4 ? "text-brand-secondary" :
                  value === 3 ? "text-yellow-600" :
                  value === 2 ? "text-orange-600" :
                  value === 1 ? "text-red-600" : "text-gray-500"
                )}>
                  {getRatingText(value)}
                </span>
              </div>
            </div>
          </div>
          
          {/* Right side indicators */}
          <div className="flex items-center gap-2">
            {isCompleted ? (
              <div className="w-6 h-6 rounded-full bg-brand-primary flex items-center justify-center">
                <Check className="h-4 w-4 text-white" />
              </div>
            ) : (
              <Button
                variant="outline"
                size="sm"
                className="h-7 px-3 text-xs text-brand-primary border-brand-primary hover:bg-brand-primary hover:text-white"
                onClick={(e) => {
                  e.stopPropagation();
                  onClick?.();
                }}
              >
                Check-in
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
