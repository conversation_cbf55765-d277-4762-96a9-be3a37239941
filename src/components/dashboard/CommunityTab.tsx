
import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>escription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";

export const CommunityTab: React.FC = () => {
  const navigate = useNavigate();
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Community Insights</CardTitle>
        <CardDescription>Learn from other pet parents with similar health journeys</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="font-medium">Similar Health Journeys</h3>
          <Button variant="link" className="text-brand-primary" size="sm" onClick={() => navigate("/community")}>
            View All
          </Button>
        </div>
        
        <div className="space-y-3">
          <div className="border rounded-lg p-3">
            <div className="flex justify-between">
              <h4 className="font-medium text-sm"><PERSON>'s gut health improvement</h4>
              <Badge variant="outline" className="text-xs">92% similar</Badge>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              "Adding fermented vegetables and rotating probiotics increased diversity score by 15 points in 6 weeks"
            </p>
          </div>
          
          <div className="border rounded-lg p-3">
            <div className="flex justify-between">
              <h4 className="font-medium text-sm">Cooper's inflammation protocol</h4>
              <Badge variant="outline" className="text-xs">87% similar</Badge>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              "Omega supplement plus novel protein diet resolved seasonal skin issues in 3-4 weeks"
            </p>
          </div>
          
          <div className="border rounded-lg p-3">
            <div className="flex justify-between">
              <h4 className="font-medium text-sm">Lucy's joint support regimen</h4>
              <Badge variant="outline" className="text-xs">78% similar</Badge>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              "Combination of glucosamine, green-lipped mussel extract and hydrotherapy shows significant mobility improvement"
            </p>
          </div>
        </div>
        
        <Button className="w-full" onClick={() => navigate("/community")}>
          Join the Community
        </Button>
        
        <div className="bg-muted/50 rounded-lg p-4 mt-4">
          <h4 className="font-medium mb-2">Trending Protocol for Labs with Digestive Issues</h4>
          <div className="space-y-2 text-sm">
            <div className="flex items-start gap-2">
              <span className="text-brand-primary">•</span>
              <p>Multi-strain probiotic with S. boulardii (93% report improvement)</p>
            </div>
            <div className="flex items-start gap-2">
              <span className="text-brand-primary">•</span>
              <p>Limited ingredient diet avoiding chicken (87% report improvement)</p>
            </div>
            <div className="flex items-start gap-2">
              <span className="text-brand-primary">•</span>
              <p>Slippery elm supplement before meals (76% report improvement)</p>
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button className="w-full" variant="outline" onClick={() => navigate("/community")}>
          Explore All Community Insights
        </Button>
      </CardFooter>
    </Card>
  );
};
