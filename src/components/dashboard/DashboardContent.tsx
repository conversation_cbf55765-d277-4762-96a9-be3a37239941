
import React from "react";
import { Layout } from "@/components/Layout";
import { EnhancedDashboardHeader } from "@/components/dashboard/header/EnhancedDashboardHeader";
import { UnifiedCheckInModal } from "@/components/dashboard/shape/UnifiedCheckInModal";
import { DashboardMainContent } from "@/components/dashboard/DashboardMainContent";
import { DashboardMobileElements } from "@/components/dashboard/DashboardMobileElements";
import { useDashboardContent } from "@/hooks/useDashboardContent";

const userName = "Dan";

export const DashboardContent: React.FC = () => {
  const dashboardData = useDashboardContent();

  return (
    <Layout showMobileHeader={false}>
      <div className="min-h-screen min-h-[100dvh] flex flex-col bg-gray-50">
        {/* Enhanced Header */}
        <EnhancedDashboardHeader
          userName={userName}
          activePet={dashboardData.activePet}
          pets={dashboardData.pets}
          onSelectPet={dashboardData.handleSelectPet}
          onAddPet={dashboardData.handleAddPet}
        />
        
        {/* Main Content */}
        <div className="flex-1 overflow-y-auto">
          <DashboardMainContent
            activePet={dashboardData.activePet}
            isFullyCompleted={dashboardData.isFullyCompleted}
            shapeScore={dashboardData.shapeScore}
            streak={dashboardData.streak}
            hasAnyEngagement={dashboardData.hasAnyEngagement()}
            completedCategories={dashboardData.completedCategories}
            totalCategories={dashboardData.totalCategories}
            completionRate={dashboardData.completionRate}
            motivationalMessage={dashboardData.motivationalMessage}
            shapeRatings={dashboardData.shapeRatings}
            overallScore={dashboardData.overallScore}
            poopLogs={dashboardData.poopLogs}
            recommendations={dashboardData.recommendations}
            dashboardState={dashboardData.dashboardState}
            onCardClick={dashboardData.handleCardClick}
            onQuickAction={dashboardData.handleQuickAction}
            onAddPoopLog={dashboardData.handleAddPoopLog}
            startSupplement={dashboardData.startSupplement}
            dismissRecommendation={dashboardData.dismissRecommendation}
          />

          {/* Unified Check-In Modal */}
          <UnifiedCheckInModal
            open={dashboardData.checkInModalOpen}
            onOpenChange={dashboardData.setCheckInModalOpen}
            shapeRatings={dashboardData.shapeRatings}
            onRate={dashboardData.handleRating}
            onAddPoopLog={dashboardData.handleAddPoopLog}
            petName={dashboardData.activePet.name}
            petId={dashboardData.activePet.id}
            existingPoopLog={dashboardData.todayLog}
          />

          {/* Mobile Elements */}
          <DashboardMobileElements
            isAICoachOpen={dashboardData.isAICoachOpen}
            setIsAICoachOpen={dashboardData.setIsAICoachOpen}
            messages={dashboardData.messages}
            isTyping={dashboardData.isTyping}
            handleSendMessage={dashboardData.handleSendMessage}
          />
        </div>
      </div>
    </Layout>
  );
};
