
import React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Shield } from "lucide-react";
import { RiskFactorsSection } from "./preventative/RiskFactorsSection";
import { PreventativePlanSection } from "./preventative/PreventativePlanSection";
import { mockRiskFactors, mockPreventativePlan } from "./preventative/mockData";

interface PreventativeDashboardProps {
  petName?: string;
}

export const PreventativeDashboard: React.FC<PreventativeDashboardProps> = ({ 
  petName = "Bella" 
}) => {
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-brand-primary" />
              Health Forecast
            </CardTitle>
            <CardDescription>
              Preventative analysis based on {petName}'s specific health profile
            </CardDescription>
          </div>
          <Badge variant="outline" className="font-normal">Updated: 3 days ago</Badge>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Risk Factors */}
        <RiskFactorsSection riskFactors={mockRiskFactors} />
        
        {/* Preventative Plan */}
        <PreventativePlanSection plan={mockPreventativePlan} />
      </CardContent>
    </Card>
  );
};
