
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Di<PERSON>, DialogContent, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, TrendingUp, AlertCircle } from "lucide-react";

interface SupplementRecommendation {
  id: string;
  name: string;
  reason: string;
  description: string;
  dosage: string;
  benefits: string[];
  urgency: 'low' | 'medium' | 'high';
  basedOn: string[];
}

interface SupplementRecommendationCardProps {
  recommendation: SupplementRecommendation;
  onStartSupplement?: (supplementName: string) => void;
  onDismiss?: (recommendationId: string) => void;
}

export const SupplementRecommendationCard: React.FC<SupplementRecommendationCardProps> = ({
  recommendation,
  onStartSupplement,
  onDismiss
}) => {
  const [showDetails, setShowDetails] = useState(false);

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default: return 'bg-blue-100 text-blue-800 border-blue-200';
    }
  };

  const getUrgencyIcon = (urgency: string) => {
    switch (urgency) {
      case 'high': return <AlertCircle className="h-4 w-4" />;
      case 'medium': return <TrendingUp className="h-4 w-4" />;
      default: return <Sparkles className="h-4 w-4" />;
    }
  };

  return (
    <>
      <Card className="border-l-4 border-l-brand-primary">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-2">
              <Pill className="h-5 w-5 text-brand-primary" />
              <CardTitle className="text-lg">{recommendation.name}</CardTitle>
            </div>
            <Badge className={`gap-1 ${getUrgencyColor(recommendation.urgency)}`}>
              {getUrgencyIcon(recommendation.urgency)}
              {recommendation.urgency}
            </Badge>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <div>
            <p className="text-sm font-medium text-muted-foreground mb-1">
              AI Recommendation:
            </p>
            <p className="text-sm">{recommendation.reason}</p>
          </div>

          <div className="flex flex-wrap gap-1">
            {recommendation.basedOn.map((factor, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {factor}
              </Badge>
            ))}
          </div>

          <div className="flex gap-2">
            <Button 
              size="sm" 
              onClick={() => setShowDetails(true)}
              variant="outline"
            >
              Learn More
            </Button>
            <Button 
              size="sm" 
              onClick={() => onStartSupplement?.(recommendation.name)}
            >
              Start Supplement
            </Button>
            {onDismiss && (
              <Button 
                size="sm" 
                variant="ghost"
                onClick={() => onDismiss(recommendation.id)}
              >
                Dismiss
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      <Dialog open={showDetails} onOpenChange={setShowDetails}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Pill className="h-5 w-5" />
              {recommendation.name}
            </DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">Description</h4>
              <p className="text-sm text-muted-foreground">
                {recommendation.description}
              </p>
            </div>

            <div>
              <h4 className="font-medium mb-2">Recommended Dosage</h4>
              <p className="text-sm bg-muted p-2 rounded">
                {recommendation.dosage}
              </p>
            </div>

            <div>
              <h4 className="font-medium mb-2">Benefits</h4>
              <ul className="text-sm space-y-1">
                {recommendation.benefits.map((benefit, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <span className="text-green-500 mt-0.5">•</span>
                    {benefit}
                  </li>
                ))}
              </ul>
            </div>

            <div>
              <h4 className="font-medium mb-2">Based On</h4>
              <div className="flex flex-wrap gap-1">
                {recommendation.basedOn.map((factor, index) => (
                  <Badge key={index} variant="secondary" className="text-xs">
                    {factor}
                  </Badge>
                ))}
              </div>
            </div>

            <div className="flex gap-2 pt-2">
              <Button 
                className="flex-1"
                onClick={() => {
                  onStartSupplement?.(recommendation.name);
                  setShowDetails(false);
                }}
              >
                Start Supplement
              </Button>
              <Button 
                variant="outline"
                onClick={() => setShowDetails(false)}
              >
                Close
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};
