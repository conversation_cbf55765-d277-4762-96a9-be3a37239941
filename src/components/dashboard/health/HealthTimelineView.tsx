
import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Calendar, TrendingUp, Pill, AlertCircle } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { usePetData } from "@/context/PetContext";
import { format, subDays, isToday, isYesterday } from "date-fns";

interface HealthEntry {
  id: string;
  date: string;
  stool_type?: number;
  appetite_level?: number;
  energy_level?: number;
  scratching_frequency?: number;
  vomiting?: boolean;
  notes?: string;
  ai_response?: string;
}

interface SupplementEvent {
  id: string;
  supplement_name: string;
  event_type: string;
  event_date: string;
  dosage?: string;
}

export const HealthTimelineView: React.FC = () => {
  const { petData } = usePetData();
  const [timelineData, setTimelineData] = useState<HealthEntry[]>([]);
  const [supplementEvents, setSupplementEvents] = useState<SupplementEvent[]>([]);
  const [viewDays, setViewDays] = useState(7);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchTimelineData();
  }, [petData.id, viewDays]);

  const fetchTimelineData = async () => {
    setIsLoading(true);
    try {
      const startDate = format(subDays(new Date(), viewDays - 1), 'yyyy-MM-dd');
      
      // Fetch health check-ins
      const { data: healthData, error: healthError } = await supabase
        .from('daily_health_checkins')
        .select('*')
        .eq('pet_id', petData.id)
        .gte('date', startDate)
        .order('date', { ascending: false });

      if (healthError) throw healthError;

      // Fetch supplement events
      const { data: supplementData, error: supplementError } = await supabase
        .from('supplement_events')
        .select('*')
        .eq('pet_id', petData.id)
        .gte('event_date', startDate)
        .order('event_date', { ascending: false });

      if (supplementError) throw supplementError;

      setTimelineData(healthData || []);
      setSupplementEvents(supplementData || []);
    } catch (error) {
      console.error('Error fetching timeline data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getStoolEmoji = (type: number) => {
    const emojis = ['', '🟤', '🌰', '🥜', '🌭', '🍯', '🥣', '💧'];
    return emojis[type] || '❓';
  };

  const getLevelEmoji = (level: number) => {
    const emojis = ['', '😰', '😟', '😐', '😊', '😄'];
    return emojis[level] || '❓';
  };

  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    if (isToday(date)) return 'Today';
    if (isYesterday(date)) return 'Yesterday';
    return format(date, 'MMM d');
  };

  const getSupplementsForDate = (date: string) => {
    return supplementEvents.filter(event => event.event_date === date);
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-muted rounded w-1/4"></div>
            <div className="space-y-2">
              <div className="h-16 bg-muted rounded"></div>
              <div className="h-16 bg-muted rounded"></div>
              <div className="h-16 bg-muted rounded"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Health Timeline
          </CardTitle>
          <div className="flex gap-2">
            <Button
              variant={viewDays === 7 ? "default" : "outline"}
              size="sm"
              onClick={() => setViewDays(7)}
            >
              7 days
            </Button>
            <Button
              variant={viewDays === 30 ? "default" : "outline"}
              size="sm"
              onClick={() => setViewDays(30)}
            >
              30 days
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {Array.from({ length: viewDays }, (_, i) => {
            const date = format(subDays(new Date(), i), 'yyyy-MM-dd');
            const entry = timelineData.find(item => item.date === date);
            const supplements = getSupplementsForDate(date);
            
            return (
              <div key={date} className="flex gap-4 p-3 rounded-lg border">
                <div className="flex-shrink-0 text-center">
                  <div className="text-sm font-medium">{formatDate(date)}</div>
                  <div className="text-xs text-muted-foreground">
                    {format(new Date(date), 'EEE')}
                  </div>
                </div>
                
                <div className="flex-1 space-y-2">
                  {entry ? (
                    <div className="flex flex-wrap gap-2">
                      {entry.stool_type && (
                        <Badge variant="outline" className="gap-1">
                          {getStoolEmoji(entry.stool_type)} Stool {entry.stool_type}
                        </Badge>
                      )}
                      {entry.appetite_level && (
                        <Badge variant="outline" className="gap-1">
                          {getLevelEmoji(entry.appetite_level)} Appetite
                        </Badge>
                      )}
                      {entry.energy_level && (
                        <Badge variant="outline" className="gap-1">
                          {getLevelEmoji(entry.energy_level)} Energy
                        </Badge>
                      )}
                      {entry.vomiting && (
                        <Badge variant="destructive" className="gap-1">
                          <AlertCircle className="h-3 w-3" /> Vomiting
                        </Badge>
                      )}
                    </div>
                  ) : (
                    <div className="text-sm text-muted-foreground">No check-in recorded</div>
                  )}
                  
                  {supplements.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {supplements.map((supplement) => (
                        <Badge key={supplement.id} variant="secondary" className="gap-1">
                          <Pill className="h-3 w-3" />
                          {supplement.supplement_name} ({supplement.event_type})
                        </Badge>
                      ))}
                    </div>
                  )}
                  
                  {entry?.notes && (
                    <div className="text-sm text-muted-foreground italic">
                      "{entry.notes}"
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
};
