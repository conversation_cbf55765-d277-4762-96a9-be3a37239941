
import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Upload, Camera, X } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

interface PetPhotoUploadProps {
  petId: string;
  petName: string;
  currentPhotoUrl?: string;
  onPhotoUpdate: (photoUrl: string) => void;
}

export const PetPhotoUpload: React.FC<PetPhotoUploadProps> = ({
  petId,
  petName,
  currentPhotoUrl,
  onPhotoUpdate
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast.error('Please select an image file');
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.error('Image must be smaller than 5MB');
      return;
    }

    setIsUploading(true);

    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Create unique filename
      const fileExt = file.name.split('.').pop();
      const fileName = `${user.id}/${petId}_${Date.now()}.${fileExt}`;

      // Upload to Supabase Storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('pet-photos')
        .upload(fileName, file);

      if (uploadError) throw uploadError;

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('pet-photos')
        .getPublicUrl(fileName);

      // Update pet record
      const { error: updateError } = await supabase
        .from('pets')
        .update({ photo_url: publicUrl })
        .eq('id', petId);

      if (updateError) throw updateError;

      onPhotoUpdate(publicUrl);
      setPreviewUrl(null);
      toast.success('Photo updated successfully!');

    } catch (error) {
      console.error('Error uploading photo:', error);
      toast.error('Failed to upload photo. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  const handleRemovePhoto = async () => {
    try {
      const { error } = await supabase
        .from('pets')
        .update({ photo_url: null })
        .eq('id', petId);

      if (error) throw error;

      onPhotoUpdate('');
      toast.success('Photo removed successfully!');

    } catch (error) {
      console.error('Error removing photo:', error);
      toast.error('Failed to remove photo. Please try again.');
    }
  };

  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex flex-col items-center space-y-4">
          <Avatar className="h-24 w-24">
            <AvatarImage src={currentPhotoUrl} alt={petName} />
            <AvatarFallback className="text-2xl">
              {petName.charAt(0)}
            </AvatarFallback>
          </Avatar>

          <div className="flex gap-2">
            <label htmlFor="photo-upload">
              <Button 
                variant="outline" 
                size="sm" 
                disabled={isUploading}
                className="cursor-pointer"
                asChild
              >
                <span>
                  {isUploading ? (
                    "Uploading..."
                  ) : (
                    <>
                      <Camera className="h-4 w-4 mr-2" />
                      {currentPhotoUrl ? 'Change Photo' : 'Add Photo'}
                    </>
                  )}
                </span>
              </Button>
            </label>
            
            <input
              id="photo-upload"
              type="file"
              accept="image/*"
              onChange={handleFileSelect}
              className="hidden"
              disabled={isUploading}
            />

            {currentPhotoUrl && (
              <Button 
                variant="outline" 
                size="sm"
                onClick={handleRemovePhoto}
              >
                <X className="h-4 w-4 mr-2" />
                Remove
              </Button>
            )}
          </div>

          <p className="text-xs text-muted-foreground text-center">
            Upload a photo of {petName} to personalize their profile
          </p>
        </div>
      </CardContent>
    </Card>
  );
};
