
import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Heart, Zap, Utensils, AlertTriangle, MessageSquare } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { usePetData } from "@/context/PetContext";
import { toast } from "sonner";

interface DailyHealthCheckInProps {
  open: boolean;
  onClose: () => void;
  onComplete: (checkInData: any) => void;
}

const stoolTypes = [
  { id: 1, emoji: "🟤", label: "Hard lumps", description: "Separate hard lumps" },
  { id: 2, emoji: "🌰", label: "Lumpy sausage", description: "Sausage-shaped but lumpy" },
  { id: 3, emoji: "🥜", label: "Cracked sausage", description: "Like a sausage with cracks" },
  { id: 4, emoji: "🌭", label: "Smooth sausage", description: "Like a sausage or snake" },
  { id: 5, emoji: "🍯", label: "Soft blobs", description: "Soft blobs with clear edges" },
  { id: 6, emoji: "🥣", label: "Mushy", description: "Fluffy pieces with ragged edges" },
  { id: 7, emoji: "💧", label: "Watery", description: "Entirely liquid" }
];

const levelOptions = [
  { value: 1, emoji: "😰", label: "Very Poor" },
  { value: 2, emoji: "😟", label: "Poor" },
  { value: 3, emoji: "😐", label: "Fair" },
  { value: 4, emoji: "😊", label: "Good" },
  { value: 5, emoji: "😄", label: "Excellent" }
];

export const DailyHealthCheckIn: React.FC<DailyHealthCheckInProps> = ({
  open,
  onClose,
  onComplete
}) => {
  const { petData } = usePetData();
  const [step, setStep] = useState(1);
  const [checkInData, setCheckInData] = useState({
    stool_type: null,
    stool_consistency: "",
    stool_color: "",
    appetite_level: null,
    energy_level: null,
    scratching_frequency: null,
    vomiting: false,
    notes: ""
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleStoolTypeSelect = (typeId: number) => {
    setCheckInData(prev => ({ ...prev, stool_type: typeId }));
  };

  const handleLevelSelect = (field: string, value: number) => {
    setCheckInData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error("User not authenticated");

      // Insert daily check-in
      const { data, error } = await supabase
        .from('daily_health_checkins')
        .insert({
          pet_id: petData.id,
          user_id: user.id,
          tenant_id: petData.tenant_id,
          date: new Date().toISOString().split('T')[0],
          ...checkInData
        })
        .select()
        .single();

      if (error) throw error;

      toast.success("Daily check-in completed!");
      onComplete(data);
      onClose();
      
      // Reset form
      setCheckInData({
        stool_type: null,
        stool_consistency: "",
        stool_color: "",
        appetite_level: null,
        energy_level: null,
        scratching_frequency: null,
        vomiting: false,
        notes: ""
      });
      setStep(1);
      
    } catch (error) {
      console.error('Error submitting check-in:', error);
      toast.error("Failed to submit check-in. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const nextStep = () => setStep(step + 1);
  const prevStep = () => setStep(step - 1);

  const canProceed = () => {
    switch (step) {
      case 1: return checkInData.stool_type !== null;
      case 2: return checkInData.appetite_level !== null;
      case 3: return checkInData.energy_level !== null;
      case 4: return checkInData.scratching_frequency !== null;
      default: return true;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-md mx-auto">
        <DialogHeader>
          <DialogTitle className="text-center">
            Daily Health Check-In
          </DialogTitle>
          <p className="text-sm text-muted-foreground text-center">
            How is {petData.name} doing today?
          </p>
        </DialogHeader>

        <div className="space-y-4">
          {/* Progress indicator */}
          <div className="flex justify-center space-x-2">
            {[1, 2, 3, 4, 5].map((i) => (
              <div
                key={i}
                className={`w-2 h-2 rounded-full ${
                  i <= step ? 'bg-brand-primary' : 'bg-muted'
                }`}
              />
            ))}
          </div>

          {/* Step 1: Stool Type */}
          {step === 1 && (
            <div className="space-y-4">
              <div className="text-center">
                <h3 className="font-medium">How was {petData.name}'s stool today?</h3>
                <p className="text-sm text-muted-foreground">Tap the type that best matches</p>
              </div>
              
              <div className="grid grid-cols-1 gap-2">
                {stoolTypes.map((type) => (
                  <Card
                    key={type.id}
                    className={`cursor-pointer transition-colors ${
                      checkInData.stool_type === type.id
                        ? 'ring-2 ring-brand-primary bg-brand-primary/5'
                        : 'hover:bg-muted/50'
                    }`}
                    onClick={() => handleStoolTypeSelect(type.id)}
                  >
                    <CardContent className="p-3">
                      <div className="flex items-center space-x-3">
                        <span className="text-2xl">{type.emoji}</span>
                        <div className="flex-1">
                          <div className="font-medium">Type {type.id}: {type.label}</div>
                          <div className="text-sm text-muted-foreground">{type.description}</div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {/* Step 2: Appetite */}
          {step === 2 && (
            <div className="space-y-4">
              <div className="text-center">
                <Utensils className="h-8 w-8 mx-auto mb-2 text-brand-primary" />
                <h3 className="font-medium">How was {petData.name}'s appetite?</h3>
              </div>
              
              <div className="grid grid-cols-1 gap-2">
                {levelOptions.map((option) => (
                  <Card
                    key={option.value}
                    className={`cursor-pointer transition-colors ${
                      checkInData.appetite_level === option.value
                        ? 'ring-2 ring-brand-primary bg-brand-primary/5'
                        : 'hover:bg-muted/50'
                    }`}
                    onClick={() => handleLevelSelect('appetite_level', option.value)}
                  >
                    <CardContent className="p-3">
                      <div className="flex items-center space-x-3">
                        <span className="text-2xl">{option.emoji}</span>
                        <span className="font-medium">{option.label}</span>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {/* Step 3: Energy Level */}
          {step === 3 && (
            <div className="space-y-4">
              <div className="text-center">
                <Zap className="h-8 w-8 mx-auto mb-2 text-brand-primary" />
                <h3 className="font-medium">How was {petData.name}'s energy level?</h3>
              </div>
              
              <div className="grid grid-cols-1 gap-2">
                {levelOptions.map((option) => (
                  <Card
                    key={option.value}
                    className={`cursor-pointer transition-colors ${
                      checkInData.energy_level === option.value
                        ? 'ring-2 ring-brand-primary bg-brand-primary/5'
                        : 'hover:bg-muted/50'
                    }`}
                    onClick={() => handleLevelSelect('energy_level', option.value)}
                  >
                    <CardContent className="p-3">
                      <div className="flex items-center space-x-3">
                        <span className="text-2xl">{option.emoji}</span>
                        <span className="font-medium">{option.label}</span>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {/* Step 4: Scratching */}
          {step === 4 && (
            <div className="space-y-4">
              <div className="text-center">
                <Heart className="h-8 w-8 mx-auto mb-2 text-brand-primary" />
                <h3 className="font-medium">How much scratching/itching today?</h3>
              </div>
              
              <div className="grid grid-cols-1 gap-2">
                {[
                  { value: 1, emoji: "😌", label: "None" },
                  { value: 2, emoji: "🤏", label: "Minimal" },
                  { value: 3, emoji: "😐", label: "Some" },
                  { value: 4, emoji: "😰", label: "Frequent" },
                  { value: 5, emoji: "😵", label: "Constant" }
                ].map((option) => (
                  <Card
                    key={option.value}
                    className={`cursor-pointer transition-colors ${
                      checkInData.scratching_frequency === option.value
                        ? 'ring-2 ring-brand-primary bg-brand-primary/5'
                        : 'hover:bg-muted/50'
                    }`}
                    onClick={() => handleLevelSelect('scratching_frequency', option.value)}
                  >
                    <CardContent className="p-3">
                      <div className="flex items-center space-x-3">
                        <span className="text-2xl">{option.emoji}</span>
                        <span className="font-medium">{option.label}</span>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {/* Step 5: Additional Notes */}
          {step === 5 && (
            <div className="space-y-4">
              <div className="text-center">
                <MessageSquare className="h-8 w-8 mx-auto mb-2 text-brand-primary" />
                <h3 className="font-medium">Anything else to note?</h3>
                <p className="text-sm text-muted-foreground">Optional additional details</p>
              </div>

              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="vomiting"
                    checked={checkInData.vomiting}
                    onChange={(e) => setCheckInData(prev => ({ ...prev, vomiting: e.target.checked }))}
                    className="rounded"
                  />
                  <label htmlFor="vomiting" className="text-sm font-medium">
                    Vomiting today
                  </label>
                </div>

                <Textarea
                  placeholder="Any other observations about your pet's health today..."
                  value={checkInData.notes}
                  onChange={(e) => setCheckInData(prev => ({ ...prev, notes: e.target.value }))}
                  rows={3}
                />
              </div>
            </div>
          )}

          <Separator />

          {/* Navigation buttons */}
          <div className="flex justify-between">
            <Button
              variant="outline"
              onClick={prevStep}
              disabled={step === 1}
            >
              Previous
            </Button>

            {step < 5 ? (
              <Button
                onClick={nextStep}
                disabled={!canProceed()}
              >
                Next
              </Button>
            ) : (
              <Button
                onClick={handleSubmit}
                disabled={isSubmitting}
              >
                {isSubmitting ? "Submitting..." : "Complete Check-In"}
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
