
import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ArrowDownIcon, ArrowRightIcon, ArrowUpIcon } from "lucide-react";

interface Test {
  id: string;
  date: string;
  status: string;
  type: string;
  score: number;
  change: string;
  changeType: string;
}

interface TestHistoryTableProps {
  tests: Test[];
  onViewReport: (testId: string) => void;
}

export const TestHistoryTable: React.FC<TestHistoryTableProps> = ({ tests, onViewReport }) => {
  // Helper function for rendering change indicators
  const renderChangeIndicator = (changeType: string, change: string) => {
    if (changeType === "positive") {
      return (
        <span className="inline-flex items-center text-green-500">
          <ArrowUpIcon className="h-4 w-4 mr-1" />
          {change}
        </span>
      );
    } else if (changeType === "negative") {
      return (
        <span className="inline-flex items-center text-red-500">
          <ArrowDownIcon className="h-4 w-4 mr-1" />
          {change}
        </span>
      );
    } else {
      return <span className="text-muted-foreground">{change}</span>;
    }
  };

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Test ID</TableHead>
          <TableHead>Date</TableHead>
          <TableHead>Type</TableHead>
          <TableHead>Status</TableHead>
          <TableHead>Score</TableHead>
          <TableHead>Change</TableHead>
          <TableHead className="text-right">Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {tests.map((test) => (
          <TableRow key={test.id}>
            <TableCell className="font-medium">{test.id}</TableCell>
            <TableCell>{test.date}</TableCell>
            <TableCell>{test.type}</TableCell>
            <TableCell>
              <Badge
                variant="outline"
                className="bg-green-50 text-green-700 border-green-200"
              >
                {test.status}
              </Badge>
            </TableCell>
            <TableCell className="font-medium">{test.score}/100</TableCell>
            <TableCell>{renderChangeIndicator(test.changeType, test.change)}</TableCell>
            <TableCell className="text-right">
              <Button variant="ghost" size="sm" onClick={() => onViewReport(test.id)}>
                View Report
                <ArrowRightIcon className="ml-2 h-4 w-4" />
              </Button>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
};
