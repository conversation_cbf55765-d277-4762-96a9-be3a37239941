
import React from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { InfoIcon } from "lucide-react";

export const TestSummaryTab = () => {
  return (
    <div className="grid md:grid-cols-2 gap-6">
      <div>
        <h3 className="text-lg font-medium mb-4">Key Findings</h3>
        <div className="space-y-4">
          <div className="border rounded-lg p-4">
            <div className="flex justify-between mb-2">
              <span className="font-medium">Overall Microbiome Health</span>
              <Badge variant="outline">82/100</Badge>
            </div>
            <p className="text-sm text-muted-foreground mb-2">
              <PERSON>'s overall microbiome health has improved by 5 points since the last test.
              This indicates the current supplement protocol is effective.
            </p>
            <div className="health-progress">
              <div
                className="health-progress-bar bg-green-500"
                style={{ width: "82%" }}
              ></div>
            </div>
          </div>

          <div className="border rounded-lg p-4">
            <div className="flex justify-between mb-2">
              <span className="font-medium">Gut Inflammation</span>
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                Reduced
              </Badge>
            </div>
            <p className="text-sm text-muted-foreground">
              Inflammation markers have decreased significantly, showing a 30% reduction
              compared to the baseline test.
            </p>
          </div>

          <div className="border rounded-lg p-4">
            <div className="flex justify-between mb-2">
              <span className="font-medium">Digestive Efficiency</span>
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                Improved
              </Badge>
            </div>
            <p className="text-sm text-muted-foreground">
              Digestive enzyme activity has increased by 22%, indicating better nutrient
              absorption and utilization.
            </p>
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-medium mb-4">Progress Summary</h3>
        <div className="bg-muted rounded-lg p-4 mb-4">
          <h4 className="font-medium flex items-center mb-2">
            <InfoIcon className="h-4 w-4 mr-1 text-brand-primary" />
            Testing Insights
          </h4>
          <p className="text-sm text-muted-foreground">
            Since starting the personalized protocol 1 year ago, Bella has shown significant
            improvement across all health markers. The microbiome diversity has increased by
            37%, and pathogenic bacteria have decreased by 45%.
          </p>
        </div>

        <div className="space-y-4">
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <h4 className="font-medium text-green-700 mb-2">Success Metrics</h4>
            <ul className="space-y-2 text-sm text-green-700">
              <li className="flex items-start">
                <span className="mr-2">•</span>
                <span>22-point increase in overall microbiome score since baseline</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2">•</span>
                <span>
                  Significant reduction in inflammatory markers detected in stool samples
                </span>
              </li>
              <li className="flex items-start">
                <span className="mr-2">•</span>
                <span>
                  Increased beneficial bacteria populations, particularly Lactobacillus and
                  Bifidobacterium species
                </span>
              </li>
            </ul>
          </div>

          <div className="border rounded-lg p-4">
            <h4 className="font-medium mb-2">Next Test Recommendation</h4>
            <p className="text-sm text-muted-foreground mb-3">
              Based on current progress, we recommend scheduling the next microbiome test in
              3 months to continue tracking Bella's gut health improvements.
            </p>
            <Button size="sm">Schedule Next Test</Button>
          </div>
        </div>
      </div>
    </div>
  );
};
