
import React from "react";
import { <PERSON><PERSON><PERSON>, Line, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts";
import { useIsMobile } from "@/hooks/use-mobile";

interface MicrobiomeProgressChartProps {
  data: Array<{
    date: string;
    score: number;
    gi: number;
    inflammation: number;
  }>;
}

export const MicrobiomeProgressChart: React.FC<MicrobiomeProgressChartProps> = ({ data }) => {
  const isMobile = useIsMobile();
  
  return (
    <div className="h-[300px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart 
          data={data} 
          margin={{ 
            top: 20, 
            right: isMobile ? 10 : 30, 
            left: isMobile ? -20 : 0, 
            bottom: 0 
          }}
        >
          <CartesianGrid strokeDasharray="3 3" vertical={false} />
          <XAxis 
            dataKey="date" 
            tick={{ fontSize: isMobile ? 10 : 12 }}
            tickMargin={isMobile ? 5 : 10}
          />
          <YAxis 
            domain={[0, 100]} 
            tick={{ fontSize: isMobile ? 10 : 12 }}
            width={isMobile ? 25 : 30}
          />
          <Tooltip
            formatter={(value) => [`${value}/100`, ""]}
            labelFormatter={(label) => `Date: ${label}`}
            contentStyle={{
              fontSize: isMobile ? '11px' : '12px',
              padding: isMobile ? '6px' : '8px',
            }}
          />
          <Line
            type="monotone"
            dataKey="score"
            name="Microbiome"
            stroke="hsl(var(--brand-primary))"
            strokeWidth={2}
            dot={{ fill: "hsl(var(--brand-primary))", r: isMobile ? 3 : 4 }}
            activeDot={{ r: isMobile ? 5 : 6 }}
          />
          <Line
            type="monotone"
            dataKey="gi"
            name="GI Function"
            stroke="hsl(var(--brand-secondary))"
            strokeWidth={2}
            dot={{ fill: "hsl(var(--brand-secondary))", r: isMobile ? 3 : 4 }}
          />
          <Line
            type="monotone"
            dataKey="inflammation"
            name="Inflammation"
            stroke="hsl(var(--brand-accent))"
            strokeWidth={2}
            dot={{ fill: "hsl(var(--brand-accent))", r: isMobile ? 3 : 4 }}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};
