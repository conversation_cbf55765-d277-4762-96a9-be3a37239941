
import React from "react";
import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowRight, TrendingUp, Calendar, FileText } from "lucide-react";
import { usePetData } from "@/context/PetContext";

interface LatestTestDetailsProps {
  onViewReport: (testId: string) => void;
  testId: string;
}

export const LatestTestDetails: React.FC<LatestTestDetailsProps> = ({
  onViewReport,
  testId
}) => {
  const { petData } = usePetData();

  const latestResults = [
    {
      category: "Overall Health Score",
      value: "82/100",
      change: "+5 points",
      status: "improved",
      description: "Significant improvement from last test"
    },
    {
      category: "Digestive Health",
      value: "89/100", 
      change: "+12 points",
      status: "excellent",
      description: "Best score yet - probiotic plan working well"
    },
    {
      category: "Inflammation Markers",
      value: "76/100",
      change: "+3 points", 
      status: "good",
      description: "Steady improvement with anti-inflammatory supplements"
    },
    {
      category: "Microbiome Diversity",
      value: "85/100",
      change: "+8 points",
      status: "excellent", 
      description: "Diverse gut bacteria ecosystem established"
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "excellent":
        return "bg-green-100 text-green-700";
      case "improved":
        return "bg-blue-100 text-blue-700";
      case "good":
        return "bg-purple-100 text-purple-700";
      default:
        return "bg-gray-100 text-gray-700";
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Latest Test Results
            </CardTitle>
            <CardDescription>
              {petData.name}'s most recent health assessment from July 25, 2025
            </CardDescription>
          </div>
          <Button 
            onClick={() => onViewReport(testId)}
            className="gap-2"
          >
            View Full Report
            <ArrowRight className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {latestResults.map((result, index) => (
            <div key={index} className="p-4 border rounded-lg space-y-2">
              <div className="flex items-center justify-between">
                <h4 className="font-medium text-sm">{result.category}</h4>
                <Badge variant="secondary" className={getStatusColor(result.status)}>
                  {result.status}
                </Badge>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-2xl font-bold text-brand-primary">{result.value}</span>
                <div className="flex items-center gap-1 text-green-600">
                  <TrendingUp className="h-4 w-4" />
                  <span className="text-sm font-medium">{result.change}</span>
                </div>
              </div>
              <p className="text-xs text-muted-foreground">{result.description}</p>
            </div>
          ))}
        </div>
        
        <div className="mt-6 p-4 bg-brand-light/20 rounded-lg border border-brand-light">
          <div className="flex items-start gap-3">
            <Calendar className="h-5 w-5 text-brand-primary mt-0.5" />
            <div>
              <h4 className="font-medium text-brand-primary mb-1">Next Test Recommended</h4>
              <p className="text-sm text-gray-600 mb-2">
                Based on {petData.name}'s progress, the next microbiome test is recommended in 12-16 weeks to track continued improvement.
              </p>
              <Button variant="outline" size="sm" className="text-brand-primary border-brand-primary hover:bg-brand-primary hover:text-white">
                Schedule Next Test
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
