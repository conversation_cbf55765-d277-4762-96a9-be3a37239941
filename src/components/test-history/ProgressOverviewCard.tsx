
import React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { MicrobiomeProgressChart } from "./MicrobiomeProgressChart";
import { useIsMobile } from "@/hooks/use-mobile";

interface ProgressOverviewCardProps {
  chartData: Array<{
    date: string;
    score: number;
    gi: number;
    inflammation: number;
  }>;
}

export const ProgressOverviewCard: React.FC<ProgressOverviewCardProps> = ({ chartData }) => {
  const isMobile = useIsMobile();

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex flex-col sm:flex-row gap-2 sm:gap-0 justify-between items-start sm:items-center">
          <div>
            <CardTitle>Health Progress</CardTitle>
            <CardDescription>Track metrics over time</CardDescription>
          </div>
          <div className={`${isMobile ? 'grid grid-cols-3 gap-2 w-full' : 'flex items-center'} text-xs sm:text-sm`}>
            <div className="flex items-center mr-3">
              <div className="w-3 h-3 bg-brand-primary rounded-full mr-1 flex-shrink-0"></div>
              <span>Microbiome</span>
            </div>
            <div className="flex items-center mr-3">
              <div className="w-3 h-3 bg-brand-secondary rounded-full mr-1 flex-shrink-0"></div>
              <span>GI Function</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-brand-accent rounded-full mr-1 flex-shrink-0"></div>
              <span>Inflammation</span>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <MicrobiomeProgressChart data={chartData} />
      </CardContent>
    </Card>
  );
};
