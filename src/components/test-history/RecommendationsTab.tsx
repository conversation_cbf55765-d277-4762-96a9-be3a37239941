
import React from "react";
import { Button } from "@/components/ui/button";

export const RecommendationsTab = () => {
  return (
    <div className="grid md:grid-cols-2 gap-6">
      <div>
        <h3 className="text-lg font-medium mb-4">Protocol Adjustments</h3>
        <div className="space-y-4">
          <div className="border rounded-lg p-4 bg-green-50 border-green-200">
            <h4 className="font-medium text-green-700 mb-1">Continue Current Supplements</h4>
            <p className="text-sm text-green-700 mb-2">
              The current supplement protocol is working well. Continue with:
            </p>
            <ul className="list-disc pl-5 text-sm text-green-700 space-y-1">
              <li>Gut Restore (probiotic blend)</li>
              <li>Digestive Enzymes</li>
              <li>Omega Support</li>
            </ul>
          </div>

          <div className="border rounded-lg p-4">
            <h4 className="font-medium mb-1">Consider Adding</h4>
            <p className="text-sm text-muted-foreground mb-2">
              Based on the latest test results, consider adding:
            </p>
            <ul className="list-disc pl-5 text-sm text-muted-foreground space-y-1">
              <li>
                Prebiotic fiber supplement to further support beneficial bacterial growth
              </li>
              <li>
                L-glutamine supplement to support gut barrier integrity and reduce
                inflammation
              </li>
            </ul>
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-medium mb-4">Lifestyle Recommendations</h3>
        <div className="space-y-4">
          <div className="border rounded-lg p-4">
            <h4 className="font-medium mb-1">Nutritional Modifications</h4>
            <ul className="list-disc pl-5 text-sm text-muted-foreground space-y-1">
              <li>
                Increase diversity of protein sources (add fish, eggs if not currently
                feeding)
              </li>
              <li>
                Include small amounts of fermented foods like plain yogurt (1 tsp daily)
              </li>
              <li>Consider adding pumpkin or sweet potato for fiber (2-3 tbsp daily)</li>
            </ul>
          </div>

          <div className="border rounded-lg p-4">
            <h4 className="font-medium mb-1">Exercise & Stress Management</h4>
            <ul className="list-disc pl-5 text-sm text-muted-foreground space-y-1">
              <li>
                Maintain current exercise routine (30-45 minutes daily) as it's supporting gut
                motility
              </li>
              <li>
                Continue providing mental stimulation through puzzle toys and training
                sessions
              </li>
              <li>
                Consider adding 5-10 minutes of gentle massage to help reduce stress and
                support digestion
              </li>
            </ul>
          </div>

          <div className="border rounded-lg p-4">
            <h4 className="font-medium mb-1">Follow-up Testing</h4>
            <p className="text-sm text-muted-foreground mb-2">
              To continue monitoring Bella's progress:
            </p>
            <ul className="list-disc pl-5 text-sm text-muted-foreground space-y-1">
              <li>Schedule next microbiome test in 3 months (October 2025)</li>
              <li>
                Consider adding inflammation panel to next test for more comprehensive
                assessment
              </li>
            </ul>
            <div className="mt-3">
              <Button size="sm">Schedule Follow-up Test</Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
