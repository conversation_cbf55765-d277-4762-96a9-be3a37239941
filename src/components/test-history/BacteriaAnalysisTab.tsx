
import React from "react";
import { Badge } from "@/components/ui/badge";

export const BacteriaAnalysisTab = () => {
  return (
    <div className="grid md:grid-cols-2 gap-6">
      <div>
        <h3 className="text-lg font-medium mb-4">Beneficial Bacteria</h3>
        <div className="space-y-4">
          <div className="border rounded-lg p-4">
            <div className="flex justify-between mb-2">
              <span className="font-medium">Lactobacillus Species</span>
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                Increased (+45%)
              </Badge>
            </div>
            <p className="text-sm text-muted-foreground">
              These probiotic bacteria help maintain a healthy gut barrier and produce lactic
              acid which inhibits harmful bacteria.
            </p>
          </div>

          <div className="border rounded-lg p-4">
            <div className="flex justify-between mb-2">
              <span className="font-medium">Bifidobacterium Species</span>
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                Increased (+38%)
              </Badge>
            </div>
            <p className="text-sm text-muted-foreground">
              These bacteria help produce vitamins, prevent infections, and aid in digestion
              of fiber.
            </p>
          </div>

          <div className="border rounded-lg p-4">
            <div className="flex justify-between mb-2">
              <span className="font-medium">Faecalibacterium</span>
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                Increased (+27%)
              </Badge>
            </div>
            <p className="text-sm text-muted-foreground">
              Known for its anti-inflammatory effects and butyrate production, which supports
              gut barrier function.
            </p>
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-medium mb-4">Potential Concerns</h3>
        <div className="space-y-4">
          <div className="border rounded-lg p-4">
            <div className="flex justify-between mb-2">
              <span className="font-medium">Clostridium perfringens</span>
              <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
                Detected (Low)
              </Badge>
            </div>
            <p className="text-sm text-muted-foreground">
              Some strains can produce toxins that may cause digestive upset. Current levels
              are low but should be monitored.
            </p>
          </div>

          <div className="border rounded-lg p-4">
            <div className="flex justify-between mb-2">
              <span className="font-medium">Diversity Score</span>
              <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
                Moderate (65/100)
              </Badge>
            </div>
            <p className="text-sm text-muted-foreground">
              While improving, bacterial diversity could be higher. Increasing dietary variety
              may help enhance microbiome diversity.
            </p>
          </div>

          <div className="border rounded-lg p-4 bg-green-50 border-green-200">
            <div className="flex justify-between mb-2">
              <span className="font-medium">E. coli</span>
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                Normal Levels
              </Badge>
            </div>
            <p className="text-sm text-green-700">
              Commensal E. coli levels are now within normal range, down from elevated levels
              in previous tests.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
