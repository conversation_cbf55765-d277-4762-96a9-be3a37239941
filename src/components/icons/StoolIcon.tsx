
import React from "react";

interface StoolIconProps {
  className?: string;
  size?: number;
}

export const StoolIcon: React.FC<StoolIconProps> = ({ className = "", size = 16 }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      {/* Main poop shape - recreating the uploaded design */}
      <path d="M6 18c0-1.5 1-3 3-3 0-1.5 1-3 3-3s3 1.5 3 3c2 0 3 1.5 3 3 0 1.5-1.5 3-3 3H9c-1.5 0-3-1.5-3-3z" />
      
      {/* Left wavy stink line */}
      <path d="M8 10c0.5-0.5 0-1 0.5-1.5s0-1 0.5-1.5" />
      
      {/* Middle wavy stink line */}
      <path d="M12 9c0.5-0.5 0-1 0.5-1.5s0-1 0.5-1.5" />
      
      {/* Right wavy stink line */}
      <path d="M16 10c0.5-0.5 0-1 0.5-1.5s0-1 0.5-1.5" />
    </svg>
  );
};
