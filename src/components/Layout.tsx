
import React from "react";
import { NavLink } from "react-router-dom";
import { cn } from "@/lib/utils";
import { 
  MessageSquare, 
  Calendar, 
  Settings, 
  ChartBar, 
  Package,
  Heart
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import Header from "@/components/header";
import Logo from "@/components/ui/logo";

type LayoutProps = {
  children: React.ReactNode;
  showMobileHeader?: boolean;
};

const navItems = [
  {
    icon: ChartBar,
    label: "Dashboard",
    path: "/dashboard",
  },
  {
    icon: Heart,
    label: "Pet Portal",
    path: "/pet-portal",
  },
  {
    icon: MessageSquare,
    label: "AI Coach",
    path: "/coach",
  },
  {
    icon: Package,
    label: "Supplements",
    path: "/supplements",
  },
  {
    icon: Calendar,
    label: "Test History",
    path: "/tests",
  },
  {
    icon: Settings,
    label: "Settings",
    path: "/settings",
  },
];

export function Layout({ children, showMobileHeader = true }: LayoutProps) {
  return (
    <div className="min-h-screen min-h-[100dvh] flex flex-col">
      {/* Header - Show on mobile only if showMobileHeader is true */}
      {showMobileHeader && (
        <div className="md:hidden">
          <Header />
        </div>
      )}
      
      {/* Main content */}
      <div className="flex-1 flex min-h-0">
        {/* Sidebar navigation - Desktop only */}
        <aside className="hidden md:block w-[240px] shrink-0 border-r p-4">
          <div className="mb-8 px-2">
            <NavLink to="/">
              <Logo size="medium" responsive={false} />
            </NavLink>
          </div>
          
          <nav className="space-y-2">
            {navItems.map((item) => (
              <NavLink 
                key={item.path} 
                to={item.path} 
                className={({ isActive }) => cn(
                  "flex items-center gap-3 px-3 py-2 rounded-md transition-colors hover:bg-muted",
                  isActive ? "bg-muted font-medium text-foreground" : "text-muted-foreground"
                )}
              >
                <item.icon className="w-5 h-5" />
                <span>{item.label}</span>
              </NavLink>
            ))}
          </nav>
          
          <div className="mt-8 pt-6 border-t">
            <Button variant="outline" className="w-full justify-start gap-2">
              <MessageSquare className="w-4 w-4" />
              <span>Need Help?</span>
            </Button>
          </div>
        </aside>
        
        {/* Main content area with improved mobile scrolling */}
        <main className="flex-1 min-w-0">
          {showMobileHeader ? (
            <div 
              className="h-full overflow-y-auto overscroll-contain p-3 md:p-6 lg:p-8 dashboard-content"
              style={{
                WebkitOverflowScrolling: 'touch',
                overscrollBehavior: 'contain',
                paddingBottom: 'max(1.5rem, env(safe-area-inset-bottom))'
              }}
            >
              {children}
            </div>
          ) : (
            children
          )}
        </main>
      </div>
    </div>
  );
}

export default Layout;
