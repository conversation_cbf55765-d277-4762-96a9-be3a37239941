
import React from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { CreditCard } from "lucide-react";

export const BillingTab: React.FC = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Membership Plan</CardTitle>
        <CardDescription>
          Manage your subscription and billing details.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="bg-brand-light/50 border border-brand-primary/20 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Badge className="bg-brand-primary">Current Plan</Badge>
              <h3 className="font-bold text-lg">Premium Membership</h3>
            </div>
            <div className="text-right">
              <span className="font-bold text-lg">$89/month</span>
              <p className="text-sm text-muted-foreground">Renews on August 15, 2025</p>
            </div>
          </div>
          <ul className="mt-4 space-y-1 text-sm">
            <li className="flex items-center gap-2">
              <svg className="h-4 w-4 text-brand-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
              </svg>
              <span>Quarterly microbiome testing</span>
            </li>
            <li className="flex items-center gap-2">
              <svg className="h-4 w-4 text-brand-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
              </svg>
              <span>Monthly personalized supplements</span>
            </li>
            <li className="flex items-center gap-2">
              <svg className="h-4 w-4 text-brand-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
              </svg>
              <span>Unlimited AI wellness coaching</span>
            </li>
          </ul>
          <div className="mt-4 flex gap-2">
            <Button variant="outline" size="sm">
              Upgrade
            </Button>
            <Button variant="outline" size="sm" className="text-destructive hover:text-destructive">
              Cancel Membership
            </Button>
          </div>
        </div>

        <Separator />

        <div>
          <h3 className="text-lg font-medium mb-4">Payment Method</h3>
          <div className="border rounded-md p-4 mb-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <CreditCard className="h-6 w-6 text-muted-foreground" />
                <div>
                  <p className="font-medium">Visa ending in 4242</p>
                  <p className="text-sm text-muted-foreground">Expires 12/25</p>
                </div>
              </div>
              <Badge variant="outline">Default</Badge>
            </div>
          </div>
          <Button variant="outline" size="sm">
            + Add Payment Method
          </Button>
        </div>

        <Separator />

        <div>
          <h3 className="text-lg font-medium mb-4">Billing History</h3>
          <div className="border rounded-md divide-y">
            {[
              { date: "July 15, 2025", amount: "$89.00", status: "Paid" },
              { date: "June 15, 2025", amount: "$89.00", status: "Paid" },
              { date: "May 15, 2025", amount: "$89.00", status: "Paid" },
            ].map((invoice, i) => (
              <div key={i} className="flex items-center justify-between p-4">
                <div>
                  <p className="font-medium">Premium Membership</p>
                  <p className="text-sm text-muted-foreground">{invoice.date}</p>
                </div>
                <div className="text-right">
                  <p className="font-medium">{invoice.amount}</p>
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    {invoice.status}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
          <Button variant="link" className="p-0 h-auto mt-2">
            View all billing history
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
