
import React from "react";
import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Package } from "lucide-react";

export const MembershipTab: React.FC = () => {
  return (
    <Card>
      <CardHeader className="bg-brand-primary text-white rounded-t-lg">
        <CardTitle className="flex items-center gap-2">
          <Package className="h-5 w-5" />
          Premium Membership
        </CardTitle>
        <CardDescription className="text-brand-light">Active until May 2026</CardDescription>
      </CardHeader>
      <CardContent className="pt-6 space-y-6">
        <div className="bg-brand-light/50 border border-brand-primary/20 rounded-lg p-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <Badge className="bg-brand-primary">Current Plan</Badge>
              <h3 className="font-bold text-lg">Premium Membership</h3>
            </div>
            <div className="text-right">
              <span className="font-bold text-lg">$89/month</span>
              <p className="text-sm text-muted-foreground">Renews on August 15, 2025</p>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <h4 className="font-semibold">Membership Benefits</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center gap-3 p-3 border rounded-lg">
              <div className="bg-green-100 rounded-full p-2">
                <svg className="h-4 w-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                </svg>
              </div>
              <div>
                <p className="font-medium">Quarterly Testing</p>
                <p className="text-sm text-muted-foreground">Comprehensive microbiome analysis</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3 p-3 border rounded-lg">
              <div className="bg-green-100 rounded-full p-2">
                <svg className="h-4 w-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                </svg>
              </div>
              <div>
                <p className="font-medium">Monthly Supplements</p>
                <p className="text-sm text-muted-foreground">Personalized supplement delivery</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3 p-3 border rounded-lg">
              <div className="bg-green-100 rounded-full p-2">
                <svg className="h-4 w-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                </svg>
              </div>
              <div>
                <p className="font-medium">Unlimited AI Coaching</p>
                <p className="text-sm text-muted-foreground">24/7 wellness guidance</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3 p-3 border rounded-lg">
              <div className="bg-green-100 rounded-full p-2">
                <svg className="h-4 w-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                </svg>
              </div>
              <div>
                <p className="font-medium">Daily Health Tracking</p>
                <p className="text-sm text-muted-foreground">SENSE check-ins and trends</p>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-muted rounded-lg p-3 text-sm">
          <p className="font-medium mb-2">Next Shipment</p>
          <p className="text-muted-foreground">
            Your monthly supplements will ship on July 28th, 2025
          </p>
        </div>
      </CardContent>
      <CardFooter className="flex gap-2">
        <Button variant="outline" className="flex-1">
          Upgrade Plan
        </Button>
        <Button variant="outline" className="flex-1 text-destructive hover:text-destructive">
          Pause Membership
        </Button>
      </CardFooter>
    </Card>
  );
};
