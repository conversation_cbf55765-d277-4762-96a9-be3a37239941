
import React, { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import { useIsMobile } from "@/hooks/use-mobile";
import { Skeleton } from "./skeleton";

interface LogoProps {
  className?: string;
  variant?: "primary" | "light";
  responsive?: boolean;
  size?: "small" | "medium" | "large";
}

const Logo = ({ 
  className, 
  variant = "primary", 
  responsive = true,
  size = "medium" 
}: LogoProps) => {
  const isMobile = useIsMobile();
  
  // Size increased by 10% from previous dimensions
  const logoSize = () => {
    if (!responsive) {
      switch(size) {
        case "small": return "h-[3.3rem]"; // ~10% increase from h-12
        case "large": return "h-[5.5rem]"; // ~10% increase from h-20
        case "medium":
        default: return "h-[3.85rem]"; // ~10% increase from h-14
      }
    } else {
      switch(size) {
        case "small": return isMobile ? "h-[2.75rem]" : "h-[3.3rem]"; // ~10% increase from h-10/h-12
        case "large": return isMobile ? "h-[3.85rem]" : "h-[5.5rem]"; // ~10% increase from h-14/h-20
        case "medium":
        default: return isMobile ? "h-[3.3rem]" : "h-[3.85rem]"; // ~10% increase from h-12/h-14
      }
    }
  };

  // Max-width constraints updated to maintain proper aspect ratio with increased sizes
  const logoMaxWidth = () => {
    if (!responsive) {
      switch(size) {
        case "small": return "max-w-[13.2rem]"; // ~10% increase from max-w-48
        case "large": return "max-w-[22rem]"; // ~10% increase from max-w-80
        case "medium":
        default: return "max-w-[15.4rem]"; // ~10% increase from max-w-56
      }
    } else {
      switch(size) {
        case "small": return isMobile ? "max-w-[11rem]" : "max-w-[13.2rem]"; // ~10% increase from max-w-40/max-w-48
        case "large": return isMobile ? "max-w-[15.4rem]" : "max-w-[22rem]"; // ~10% increase from max-w-56/max-w-80
        case "medium":
        default: return isMobile ? "max-w-[13.2rem]" : "max-w-[15.4rem]"; // ~10% increase from max-w-48/max-w-56
      }
    }
  };

  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  
  // Force logo to reload on component mount
  useEffect(() => {
    setIsLoading(true);
    setHasError(false);
    
    // Preload the image
    const img = new Image();
    img.src = "/lovable-uploads/17633670-38c1-4afb-8d52-0de6b35a3363.png";
    img.onload = () => setIsLoading(false);
    img.onerror = () => {
      console.error("Failed to load logo");
      setHasError(true);
      setIsLoading(false);
    };
    
    return () => {
      img.onload = null;
      img.onerror = null;
    };
  }, []);
  
  return (
    <div className={cn("flex items-center", className)}>
      <div className={cn("relative flex items-center", logoSize(), logoMaxWidth())}>
        {isLoading && <Skeleton className={`w-auto ${logoSize()}`} />}
        
        <div className={cn("w-auto", isLoading ? "opacity-0" : "opacity-100", "transition-opacity duration-300")}>
          <img 
            src="/lovable-uploads/17633670-38c1-4afb-8d52-0de6b35a3363.png" 
            alt="AnimalBiome Logo" 
            className={cn(
              "w-auto h-full object-contain",
              variant === "light" ? "brightness-0 invert" : "",
              hasError ? "hidden" : "block"
            )}
          />
        </div>
        
        {hasError && (
          <div className={`flex items-center justify-center text-brand-primary font-semibold ${logoSize()} w-auto`}>
            AnimalBiome
          </div>
        )}
      </div>
    </div>
  );
};

export default Logo;
