
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import Logo from "@/components/ui/logo";
import { Menu, X } from "lucide-react";
import { 
  DropdownMenu, 
  DropdownMenuTrigger, 
  DropdownMenuContent,
  DropdownMenuItem 
} from "@/components/ui/dropdown-menu";
import { useIsMobile } from "@/hooks/use-mobile";

const HomeHeader = () => {
  const navigate = useNavigate();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const isMobile = useIsMobile();
  
  // Function to handle menu toggle
  const toggleMenu = () => {
    setIsMenuOpen(prev => !prev);
  };

  // Function to close menu and navigate
  const handleGetStartedClick = () => {
    setIsMenuOpen(false);
    navigate("/onboarding");
  };
  
  return (
    <header className="absolute top-0 left-0 w-full z-50 py-3 md:py-4">
      <div className="container flex justify-between items-center">
        <div className="flex items-center gap-2 z-20">
          <Logo 
            variant="light"
            size="small" 
            responsive={true}
          />
        </div>
        
        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center gap-6 lg:gap-8">
          <a href="#hero" className="text-white text-xs hover:text-white/80 transition-colors">Home</a>
          <a href="#how-it-works" className="text-white text-xs hover:text-white/80 transition-colors">How It Works</a>
          <a href="#science" className="text-white text-xs hover:text-white/80 transition-colors">Science</a>
          <a href="#testimonials" className="text-white text-xs hover:text-white/80 transition-colors">Testimonials</a>
          <a href="#about" className="text-white text-xs hover:text-white/80 transition-colors">About</a>
        </nav>
        
        {/* Desktop Actions */}
        <div className="hidden md:flex items-center gap-4 z-20">
          <Button 
            variant="ghost" 
            onClick={() => navigate("/login")} 
            className="text-white hover:text-brand-primary hover:bg-white/10 transition-colors rounded-full h-10"
          >
            Login
          </Button>
          <Button 
            onClick={() => navigate("/onboarding")} 
            className="bg-brand-primary text-white hover:bg-brand-dark rounded-full h-10"
          >
            Get Started
          </Button>
        </div>
        
        {/* Mobile Menu */}
        <div className="md:hidden z-50">
          <div className="flex items-center">
            <Button 
              variant="ghost" 
              onClick={() => navigate("/login")} 
              className="mr-2 text-white hover:text-brand-primary hover:bg-white/10 transition-colors rounded-full h-10"
            >
              Login
            </Button>
            
            <Button 
              variant="outline" 
              size="icon" 
              onClick={toggleMenu}
              className="rounded-full h-10 w-10 border-white/30 bg-transparent text-white hover:bg-white/10"
            >
              {isMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </Button>
          </div>
          
          {/* Mobile Menu Content */}
          {isMenuOpen && (
            <div className="fixed inset-0 z-50 bg-brand-primary mt-16">
              <div className="container pt-10 flex flex-col gap-4 text-white">
                <a 
                  href="#hero" 
                  className="text-base py-4 border-b border-white/20 text-white hover:bg-brand-dark/20"
                  onClick={() => setIsMenuOpen(false)}
                >
                  Home
                </a>
                <a 
                  href="#how-it-works" 
                  className="text-base py-4 border-b border-white/20 text-white hover:bg-brand-dark/20"
                  onClick={() => setIsMenuOpen(false)}
                >
                  How It Works
                </a>
                <a 
                  href="#science" 
                  className="text-base py-4 border-b border-white/20 text-white hover:bg-brand-dark/20"
                  onClick={() => setIsMenuOpen(false)}
                >
                  Science
                </a>
                <a 
                  href="#testimonials" 
                  className="text-base py-4 border-b border-white/20 text-white hover:bg-brand-dark/20"
                  onClick={() => setIsMenuOpen(false)}
                >
                  Testimonials
                </a>
                <a 
                  href="#about" 
                  className="text-base py-4 border-b border-white/20 text-white hover:bg-brand-dark/20"
                  onClick={() => setIsMenuOpen(false)}
                >
                  About
                </a>
                
                <Button 
                  onClick={handleGetStartedClick}
                  className="mt-6 bg-white text-brand-primary hover:bg-white/90 rounded-full h-12"
                >
                  Get Started
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </header>
  );
};

export default HomeHeader;
