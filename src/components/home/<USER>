import React from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { AspectRatio } from "@/components/ui/aspect-ratio";
import { Card, CardContent } from "@/components/ui/card";

const Features = () => {
  const navigate = useNavigate();
  
  const features = [
    {
      icon: "test-tube",
      title: "Microbiome Testing",
      description: "Regular diagnostics to track your pet's gut health and identify potential issues before they become serious.",
      image: "/lovable-uploads/c11cf6c4-4dba-4487-807c-142cd0d2d95f.png"
    }, 
    {
      icon: "pill",
      title: "Targeted Supplements",
      description: "Custom-formulated supplements delivered monthly, adjusted based on your pet's evolving health needs.",
      image: "/lovable-uploads/cf0c017c-a581-4ff3-b3dc-96dfe9b767e1.png"
    }, 
    {
      icon: "user-round",
      title: "Expert Health Guidance",
      description: "Connect with our veterinary experts for personalized advice about your pet's health, supported by our Smart Health Coach.",
      image: "/lovable-uploads/be88a41e-6b37-4e79-9660-91bfa445e7a1.png"
    }
  ];

  return (
    <section className="py-10 bg-white" id="features">
      <div className="container">
        <div className="text-center mb-8 max-w-2xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 font-serif text-brand-primary">The Complete Pet Health & Wellness Program</h2>
          <Separator className="w-24 h-1 bg-[#DDD0DD] mx-auto mb-4" />
          <p className="text-xl text-muted-foreground">Everything your pet needs for a healthier, happier life</p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8">
          {features.map((feature, index) => (
            <FeatureCard 
              key={index} 
              title={feature.title} 
              description={feature.description} 
              image={feature.image} 
              icon={feature.icon} 
            />
          ))}
        </div>
        
        <div className="mt-10 bg-brand-light rounded-3xl p-6 md:p-8 text-center">
          <h3 className="text-2xl md:text-3xl font-bold mb-4 font-serif text-brand-primary">Ready to Give Your Pet the Gift of Better Health?</h3>
          <p className="text-lg mb-6 max-w-2xl mx-auto text-brand-primary/80">
            Start your pet on the path to a healthier life with our expert-guided, comprehensive membership program.
          </p>
          <Button 
            className="bg-brand-primary text-white hover:bg-brand-dark rounded-full px-8 h-12 text-lg" 
            onClick={() => navigate("/onboarding")}
          >
            Get Started
          </Button>
        </div>
      </div>
    </section>
  );
};

interface FeatureCardProps {
  title: string;
  description: string;
  image: string;
  icon: string;
}

const FeatureCard: React.FC<FeatureCardProps> = ({
  title,
  description,
  image,
  icon
}) => {
  const renderIcon = () => {
    switch (icon) {
      case 'user-round':
        return <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-6 w-6 text-brand-primary"><circle cx="12" cy="8" r="5" /><path d="M20 21a8 8 0 0 0-16 0" /></svg>;
      case 'pill':
        return <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-6 w-6 text-brand-primary"><path d="m10.5 20.5 10-10a4.95 4.95 0 1 0-7-7l-10 10a4.95 4.95 0 1 0 7 7Z" /><path d="m8.5 8.5 7 7" /></svg>;
      case 'test-tube':
        return <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-6 w-6 text-brand-primary"><path d="M14.5 2v17.5c0 1.4-1.1 2.5-2.5 2.5h0c-1.4 0-2.5-1.1-2.5-2.5V2" /><path d="M8.5 2h7" /><path d="M14.5 16h-5" /></svg>;
      default:
        return null;
    }
  };

  return (
    <Card className="bg-white border border-gray-100 rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300">
      <CardContent className="p-0">
        <div className="flex flex-col h-full">
          {/* Icon and Title at top */}
          <div className="p-5 flex flex-col items-center text-center">
            <div className="bg-brand-light p-3 rounded-lg mb-3">
              {renderIcon()}
            </div>
            <h3 className="text-xl font-bold mb-2 font-serif text-brand-primary">{title}</h3>
          </div>
          
          {/* Image in middle */}
          <div className="relative w-full overflow-hidden">
            <AspectRatio ratio={16 / 12}>
              <img 
                src={image} 
                alt={title} 
                className="w-full h-full object-cover object-center" 
                loading="eager" 
                onError={e => {
                  console.error(`Failed to load image: ${image}`);
                  e.currentTarget.src = "/placeholder.svg";
                }} 
              />
            </AspectRatio>
          </div>
          
          {/* Description and button at bottom */}
          <div className="p-5 flex flex-col items-center text-center flex-grow">
            <p className="text-base text-muted-foreground mb-4">
              {description}
            </p>
            <Button variant="link" className="text-brand-primary p-0 font-medium mt-auto">
              Learn more
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default Features;
