
import React from "react";
import { useNavigate } from "react-router-dom";
import { ArrowRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { useIsMobile } from "@/hooks/use-mobile";

const CTA = () => {
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  
  return (
    <section id="about" className="py-16 bg-white">
      <div className="container max-w-5xl">
        <div className="bg-brand-light rounded-3xl p-6 md:p-10 text-center relative overflow-hidden">
          {/* Decorative elements */}
          <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-brand-primary via-brand-secondary to-brand-accent"></div>
          <div className="absolute -top-20 -right-20 w-40 h-40 bg-brand-accent/10 rounded-full"></div>
          <div className="absolute -bottom-20 -left-20 w-40 h-40 bg-brand-primary/10 rounded-full"></div>
          
          <div className="relative z-10">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 font-serif text-brand-primary">Give Your Pet the Gift of Health</h2>
            <Separator className="w-24 h-1 bg-[#DDD0DD] mx-auto mb-6" />
            <p className="text-lg mb-8 max-w-2xl mx-auto text-brand-primary/80 font-serif">
              Join the membership program that combines expert guidance, science, and personalized care for a healthier, happier pet life.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                className="bg-brand-primary text-white hover:bg-brand-dark rounded-full px-8 h-12" 
                size="lg" 
                onClick={() => navigate("/onboarding")}
              >
                {isMobile ? "Get Started" : "Start Your Pet's Health Journey"}
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
              <Button 
                variant="outline" 
                className="border-brand-primary text-brand-primary hover:bg-brand-primary/10 rounded-full px-8 h-12" 
                size="lg"
              >
                Book a Consultation
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CTA;
