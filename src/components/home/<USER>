
import React from "react";
import { Separator } from "@/components/ui/separator";
import { Card, CardContent } from "@/components/ui/card";
import { Users, TestTube, ClipboardCheck, Phone } from "lucide-react";

const HowItWorks = () => {
  const steps = [
    {
      icon: <Users className="h-8 w-8 text-brand-primary" />,
      title: "Initial Assessment",
      description: "Connect with a health advisor for a comprehensive review of your pet's health history, needs, and goals."
    },
    {
      icon: <TestTube className="h-8 w-8 text-brand-primary" />,
      title: "Microbiome Testing",
      description: "Receive your test kit, collect a sample, and get insights into your pet's gut health and microbiome."
    },
    {
      icon: <ClipboardCheck className="h-8 w-8 text-brand-primary" />,
      title: "Personalized Plan",
      description: "Our veterinary experts create a tailored health plan with supplement recommendations based on your pet's test results."
    },
    {
      icon: <Phone className="h-8 w-8 text-brand-primary" />,
      title: "Ongoing Support",
      description: "Access monthly advisor calls, quarterly reviews, and 24/7 Smart Health Coach to keep your pet's health on track."
    }
  ];

  return (
    <section id="how-it-works" className="py-16 bg-gray-50">
      <div className="container">
        <div className="text-center mb-12 max-w-2xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 font-serif text-brand-primary">How It Works</h2>
          <Separator className="w-24 h-1 bg-[#DDD0DD] mx-auto mb-6" />
          <p className="text-lg text-muted-foreground">
            Our expert-guided approach combines veterinary expertise with cutting-edge science to deliver personalized care for your pet.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {steps.map((step, index) => (
            <Card key={index} className="border-brand-primary/10 hover:shadow-md transition-all duration-300">
              <CardContent className="p-6 flex flex-col items-center text-center">
                <div className="bg-brand-light p-4 rounded-full mb-4">
                  {step.icon}
                </div>
                <div className="text-xl font-bold mb-1 text-brand-primary font-serif">
                  {step.title}
                </div>
                <div className="text-muted-foreground">
                  {step.description}
                </div>
                
                {index < steps.length - 1 && (
                  <div className="hidden lg:flex absolute -right-3 top-1/2 transform -translate-y-1/2">
                    <ArrowConnector />
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

// Arrow connector component for desktop view
const ArrowConnector = () => (
  <div className="text-brand-primary/40">
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
      <path d="M5 12h14" />
      <path d="m12 5 7 7-7 7" />
    </svg>
  </div>
);

export default HowItWorks;
