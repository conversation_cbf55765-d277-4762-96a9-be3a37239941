import React from "react";
import Logo from "@/components/ui/logo";
import { Separator } from "@/components/ui/separator";
import { useIsMobile } from "@/hooks/use-mobile";
const Footer = () => {
  const isMobile = useIsMobile();
  return <footer className="bg-brand-light border-t mt-auto">
      <div className="container py-8 md:py-16">
        <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-12">
          <div className="col-span-2 md:col-span-1 mb-4 md:mb-0">
            <div className="flex items-center gap-2 mb-4">
              <Logo size="small" />
            </div>
            <p className="text-brand-primary/80 text-sm md:text-base">
              Personalized daily health plans for your pet—powered by data, guided by care.
            </p>
          </div>
          
          <div>
            <h5 className="font-serif text-base md:text-lg font-bold mb-3 md:mb-4 text-brand-primary">Program</h5>
            <ul className="space-y-2 md:space-y-3">
              <li>
                <a href="#" className="text-brand-primary/70 hover:text-brand-primary transition-colors block py-1 md:py-0">
                  How It Works
                </a>
              </li>
              <li>
                <a href="#" className="text-brand-primary/70 hover:text-brand-primary transition-colors block py-1 md:py-0">
                  Membership Benefits
                </a>
              </li>
              <li>
                <a href="#" className="text-brand-primary/70 hover:text-brand-primary transition-colors block py-1 md:py-0">
                  Scientific Approach
                </a>
              </li>
              <li>
                <a href="#" className="text-brand-primary/70 hover:text-brand-primary transition-colors block py-1 md:py-0">
                  Success Stories
                </a>
              </li>
            </ul>
          </div>
          
          <div className="col-span-2 md:col-span-1">
            <h5 className="font-serif text-base md:text-lg font-bold mb-3 md:mb-4 text-brand-primary">Company</h5>
            <ul className="space-y-2 md:space-y-3">
              <li>
                
              </li>
              <li>
                <a href="#" className="text-brand-primary/70 hover:text-brand-primary transition-colors block py-1 md:py-0">
                  About AnimalBiome
                </a>
              </li>
              <li>
                <a href="#" className="text-brand-primary/70 hover:text-brand-primary transition-colors block py-1 md:py-0">
                  Our Research
                </a>
              </li>
              <li>
                <a href="#" className="text-brand-primary/70 hover:text-brand-primary transition-colors block py-1 md:py-0">
                  Contact Us
                </a>
              </li>
            </ul>
          </div>
          
          <div className="col-span-2 md:col-span-1 lg:col-span-1">
            <h5 className="font-serif text-base md:text-lg font-bold mb-3 md:mb-4 text-brand-primary">Legal</h5>
            <ul className="space-y-2 md:space-y-3">
              <li>
                <a href="#" className="text-brand-primary/70 hover:text-brand-primary transition-colors block py-1 md:py-0">
                  Terms of Service
                </a>
              </li>
              <li>
                <a href="#" className="text-brand-primary/70 hover:text-brand-primary transition-colors block py-1 md:py-0">
                  Privacy Policy
                </a>
              </li>
              <li>
                <a href="#" className="text-brand-primary/70 hover:text-brand-primary transition-colors block py-1 md:py-0">
                  Cookie Policy
                </a>
              </li>
            </ul>
          </div>
        </div>
        
        <Separator className="my-6 md:my-8 bg-[#DDD0DD]" />
        
        <div className="text-center text-brand-primary/60 text-sm">
          <p>&copy; {new Date().getFullYear()} AnimalBiome x SENSE. All rights reserved.</p>
        </div>
      </div>
    </footer>;
};
export default Footer;