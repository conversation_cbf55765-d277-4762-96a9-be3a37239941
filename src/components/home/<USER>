
import React from "react";
import { <PERSON>, <PERSON> } from "lucide-react";
import { 
  Carousel, 
  CarouselContent, 
  CarouselItem,
  CarouselPrevious,
  CarouselNext
} from "@/components/ui/carousel";
import { useIsMobile } from "@/hooks/use-mobile";
import { Card } from "@/components/ui/card";

interface TestimonialCardProps {
  name: string;
  petInfo: string;
  testimonial: string;
  icon: "dog" | "cat";
}

const TestimonialCard: React.FC<TestimonialCardProps> = ({ name, petInfo, testimonial, icon }) => {
  return (
    <Card className="bg-white rounded-xl p-6 md:p-8 shadow-md border-0 h-full transition-all duration-300 hover:shadow-lg hover:translate-y-[-4px]">
      <div className="flex items-center gap-4 mb-6">
        <div className="w-12 h-12 rounded-full bg-[#F1F0FB] flex items-center justify-center">
          {icon === "dog" ? 
            <Dog className="h-6 w-6 text-brand-primary" /> : 
            <Cat className="h-6 w-6 text-brand-secondary" />
          }
        </div>
        <div>
          <h4 className="font-bold text-brand-primary font-serif">{name}</h4>
          <p className="text-sm text-muted-foreground">{petInfo}</p>
        </div>
      </div>
      <p className="text-muted-foreground text-base md:text-lg italic font-serif leading-relaxed">
        {testimonial}
      </p>
    </Card>
  );
};

const Testimonials = () => {
  const isMobile = useIsMobile();
  const testimonials = [
    {
      name: "Max's Mom",
      petInfo: "Golden Retriever, 7 years",
      testimonial: "\"Max's digestive issues have completely disappeared after 3 months on the program. The AI coach helped me identify food triggers I never knew about!\"",
      icon: "dog" as const
    },
    {
      name: "Luna's Dad",
      petInfo: "Maine Coon, 5 years",
      testimonial: "\"Luna's energy levels have skyrocketed since starting the supplements. I love seeing her test results improve with each check-up.\"",
      icon: "cat" as const
    },
    {
      name: "Charlie's Parents",
      petInfo: "Poodle Mix, 10 years",
      testimonial: "\"We joined hoping to improve Charlie's senior years, and we're amazed at how much more active and playful he's become!\"",
      icon: "dog" as const
    },
    {
      name: "Sophie's Mom",
      petInfo: "Tabby Cat, 8 years",
      testimonial: "\"The personalized supplement regime has made such a difference in Sophie's coat quality. She's shinier than ever!\"",
      icon: "cat" as const
    }
  ];
  
  return (
    <section className="py-16 md:py-24 bg-gradient-to-b from-brand-beige to-[#F5F2EE] relative overflow-hidden">
      {/* Decorative elements */}
      <div className="absolute top-10 left-10 w-40 h-40 bg-[#DDD0DD]/30 rounded-full blur-3xl"></div>
      <div className="absolute bottom-10 right-10 w-60 h-60 bg-brand-primary/10 rounded-full blur-3xl"></div>
      <div className="absolute top-1/4 right-1/4 w-20 h-20 bg-brand-accent/20 rounded-full blur-2xl"></div>
      
      <div className="container relative z-10">
        <div className="text-center mb-10 md:mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 font-serif text-brand-primary">What Pet Parents Say</h2>
          <div className="flex justify-center mb-4">
            <div className="w-24 h-1 bg-[#DDD0DD] rounded-full"></div>
          </div>
          <p className="text-lg md:text-xl text-muted-foreground max-w-2xl mx-auto">
            Real stories from real pet owners who've seen the difference our program makes
          </p>
        </div>
        
        <div className="relative px-4 md:px-12 -mx-2 md:mx-0">
          <Carousel opts={{ loop: true, align: isMobile ? "start" : "center" }}>
            <CarouselContent className="-ml-2 md:-ml-4">
              {testimonials.map((testimonial, index) => (
                <CarouselItem key={index} className="pl-2 md:pl-4 md:basis-1/2 lg:basis-1/3">
                  <div className="h-full">
                    <TestimonialCard 
                      name={testimonial.name}
                      petInfo={testimonial.petInfo}
                      testimonial={testimonial.testimonial}
                      icon={testimonial.icon}
                    />
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
            <div className="hidden md:block">
              <CarouselPrevious className="left-0 border-brand-primary text-brand-primary hover:bg-brand-primary hover:text-white" />
              <CarouselNext className="right-0 border-brand-primary text-brand-primary hover:bg-brand-primary hover:text-white" />
            </div>
          </Carousel>
          
          {/* Mobile indicator dots */}
          {isMobile && (
            <div className="flex justify-center gap-2 mt-6">
              {testimonials.map((_, index) => (
                <div 
                  key={index} 
                  className="w-2 h-2 rounded-full bg-brand-primary/30"
                  // This is just for visual representation
                ></div>
              ))}
            </div>
          )}
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
