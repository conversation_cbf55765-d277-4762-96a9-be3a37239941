
import React from "react";
import { Separator } from "@/components/ui/separator";
import { Card, CardContent } from "@/components/ui/card";
import { AspectRatio } from "@/components/ui/aspect-ratio";

const OurExperts = () => {
  const experts = [
    {
      name: "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, MSc, PhD",
      title: "Veterinary Nutritionist",
      photo: "https://i.shgcdn.com/b81284a9-3f76-4723-881a-5a1f1e72e450/-/format/auto/-/preview/3000x3000/-/quality/lighter/",
      description: "With over 15 years of experience in veterinary nutrition, Dr<PERSON> <PERSON> specializes in creating personalized diet and supplement plans for pets with chronic health conditions."
    },
    {
      name: "<PERSON>, PhD",
      title: "Microbiome Specialist",
      photo: "https://i.shgcdn.com/a8af0346-69f3-40ed-ac31-5b90cd22fdb2/-/format/auto/-/preview/3000x3000/-/quality/lighter/",
      description: "As a leading researcher in animal microbiome science, Dr<PERSON> helps translate complex test results into actionable health recommendations."
    },
    {
      name: "<PERSON>, <PERSON>, LVMT, VTS (ECC)",
      title: "Holistic Veterinarian",
      photo: "https://i.shgcdn.com/8433925b-260a-4db9-bf29-8b68f8af2288/-/format/auto/-/preview/3000x3000/-/quality/lighter/",
      description: "Combining traditional veterinary medicine with holistic approaches, Becky Smith guides pet parents through comprehensive preventative care strategies."
    }
  ];

  return (
    <section id="science" className="py-16 bg-white">
      <div className="container">
        <div className="text-center mb-10 max-w-2xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 font-serif text-brand-primary">Our Expert Team</h2>
          <Separator className="w-24 h-1 bg-[#DDD0DD] mx-auto mb-6" />
          <p className="text-lg text-muted-foreground">
            Meet the veterinary experts who will guide your pet's health journey with personalized care and science-backed recommendations.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8">
          {experts.map((expert, index) => (
            <Card key={index} className="overflow-hidden border-brand-primary/10 hover:shadow-md transition-all duration-300">
              <div className="relative">
                <AspectRatio ratio={3/2}>
                  <img 
                    src={expert.photo} 
                    alt={expert.name} 
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      console.error(`Failed to load expert image: ${expert.photo}`);
                      e.currentTarget.src = "/placeholder.svg";
                    }}
                  />
                </AspectRatio>
              </div>
              <CardContent className="p-6">
                <h3 className="text-xl font-bold mb-1 text-brand-primary font-serif">{expert.name}</h3>
                <p className="text-brand-primary/70 font-medium mb-3">{expert.title}</p>
                <p className="text-muted-foreground text-sm">{expert.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default OurExperts;
