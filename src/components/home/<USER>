
import React, { useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { ArrowRight, Shield, Users, Award } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useIsMobile } from "@/hooks/use-mobile";
import { useAuth } from "@/contexts/AuthContext";

const Hero = () => {
  const navigate = useNavigate();
  const heroRef = useRef<HTMLDivElement>(null);
  const isMobile = useIsMobile();
  const { user } = useAuth();

  // Staggered animation effect on scroll
  useEffect(() => {
    const observer = new IntersectionObserver(entries => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add("animate-in");
        }
      });
    }, {
      threshold: 0.1
    });
    const animatedElements = heroRef.current?.querySelectorAll(".animate-element");
    animatedElements?.forEach(el => observer.observe(el));
    return () => {
      animatedElements?.forEach(el => observer.unobserve(el));
    };
  }, []);

  return (
    <section ref={heroRef} id="hero" className="relative w-full min-h-[90vh] md:min-h-[80vh] bg-brand-light overflow-hidden">
      {/* Full-bleed hero image background */}
      <div className="absolute inset-0 w-full h-full z-0">
        <img 
          src="/lovable-uploads/7867bef0-8053-485c-a87e-6a7b1db0137d.png" 
          alt="Fluffy dog in tall grass" 
          className="w-full h-full object-cover object-center" 
          onError={e => {
            console.error("Failed to load hero image");
            e.currentTarget.src = "/placeholder.svg";
          }} 
          loading="eager" 
        />
      </div>
      
      {/* Content container */}
      <div className="container relative h-full py-24 md:py-16 z-20">
        <div className="grid md:grid-cols-2 gap-8 md:gap-12 h-full items-center py-[36px] my-[20px]">
          {/* Content column */}
          <div className="space-y-6 md:space-y-8 max-w-lg animate-element">
            <h1 className="text-4xl lg:text-6xl tracking-tight font-serif leading-tight animate-element text-[#F1ECE4] drop-shadow-md px-0 mx-0 font-light md:text-6xl">
              A better way to care for your pet's health and wellbeing.
            </h1>
            
            <p className="text-lg md:text-xl text-[#F1ECE4] font-sans animate-element drop-shadow-md">
              Stop worrying about hidden health issues. Catch problems early, prevent costly vet bills, and give your pet the longest, healthiest life possible with personalized care based on real diagnostic data.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 pt-2 md:pt-4 animate-element">
              <Button size="lg" onClick={() => navigate("/onboarding")} className="bg-gradient-to-r from-brand-primary to-brand-dark hover:brightness-110 text-white rounded-full shadow-lg shadow-brand-primary/20 transition-all duration-300 transform hover:translate-y-[-2px] group h-12">
                Get Your Pet's Health Plan Today
                <ArrowRight className="ml-1 h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
              </Button>
              
              {user ? (
                <Button size="lg" variant="outline" onClick={() => navigate("/dashboard")} className="bg-[#F1ECE4] border-2 border-[#F1ECE4] text-[#335341] hover:text-[#335341] hover:bg-[#F1ECE4]/90 rounded-full transition-all duration-300 transform hover:translate-y-[-2px] h-12">
                  Go to Dashboard
                </Button>
              ) : (
                <Button size="lg" variant="outline" onClick={() => navigate("/login")} className="bg-[#F1ECE4] border-2 border-[#F1ECE4] text-[#335341] hover:text-[#335341] hover:bg-[#F1ECE4]/90 rounded-full transition-all duration-300 transform hover:translate-y-[-2px] h-12">
                  Sign In
                </Button>
              )}
            </div>

            {/* Trust indicators - moved below CTA */}
            <div className="flex flex-wrap gap-4 animate-element">
              <div className="flex items-center gap-2 bg-white/20 backdrop-blur-sm rounded-full px-3 py-1 text-white text-sm">
                <Shield className="h-4 w-4" />
                <span>Veterinarian-Approved</span>
              </div>
              <div className="flex items-center gap-2 bg-white/20 backdrop-blur-sm rounded-full px-3 py-1 text-white text-sm">
                <Award className="h-4 w-4" />
                <span>Backed by 25 yrs of Microbiome Science</span>
              </div>
              <div className="flex items-center gap-2 bg-white/20 backdrop-blur-sm rounded-full px-3 py-1 text-white text-sm">
                <Users className="h-4 w-4" />
                <span>Trusted by over 100,000 Pet Parents</span>
              </div>
            </div>

            {/* Urgency indicator */}
            <div className="text-sm text-[#F1ECE4]/90 animate-element">
              <span className="bg-orange-500/80 text-white px-2 py-1 rounded text-xs font-medium mr-2">
                LIMITED TIME
              </span>
              Join 500+ pets who started their health journey this month
            </div>
          </div>
          
          {/* Right side content - empty in this design to let the background image be more visible */}
          <div className="hidden md:block"></div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
