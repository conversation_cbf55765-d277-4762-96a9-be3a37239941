
import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { Menu, User, X } from "lucide-react";
import Logo from "@/components/ui/logo";
import { Button } from "@/components/ui/button";
import { useIsMobile } from "@/hooks/use-mobile";
import { cn } from "@/lib/utils";
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger, DropdownMenuItem } from "@/components/ui/dropdown-menu";
import { 
  MessageSquare, 
  Calendar, 
  Settings, 
  ChartBar, 
  Package,
  Heart
} from "lucide-react";

const Header: React.FC = () => {
  const isMobile = useIsMobile();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  
  // Navigation items matching the desktop sidebar
  const navItems = [
    {
      icon: ChartBar,
      label: "Dashboard",
      path: "/dashboard",
    },
    {
      icon: Heart,
      label: "Pet Portal",
      path: "/pet-portal",
    },
    {
      icon: MessageSquare,
      label: "AI Coach",
      path: "/coach",
    },
    {
      icon: Package,
      label: "Supplements",
      path: "/supplements",
    },
    {
      icon: Calendar,
      label: "Test History",
      path: "/tests",
    },
    {
      icon: Settings,
      label: "Settings",
      path: "/settings",
    },
  ];
  
  return (
    <header className="sticky top-0 z-50 border-b border-brand-beige/50 bg-white/95 backdrop-blur-sm">
      <div className="container py-3 md:py-4 flex justify-between items-center">
        <Link to="/" className="flex items-center z-20">
          <Logo size="small" responsive={true} />
        </Link>
        
        {/* Desktop Navigation - Updated to match sidebar navigation */}
        <nav className="hidden md:flex items-center space-x-6 lg:space-x-8">
          {navItems.slice(0, 4).map((item) => (
            <Link 
              key={item.path} 
              to={item.path} 
              className="text-foreground hover:text-brand-primary transition-colors font-medium"
            >
              {item.label}
            </Link>
          ))}
          <Link to="/community" className="text-foreground hover:text-brand-primary transition-colors font-medium">
            Community
          </Link>
        </nav>
        
        {/* Desktop Actions */}
        <div className="hidden md:flex items-center gap-4">
          <Link to="/login">
            <Button variant="ghost" size="icon" className="rounded-full">
              <User className="h-5 w-5" />
              <span className="sr-only">Account</span>
            </Button>
          </Link>
        </div>
        
        {/* Mobile Menu */}
        <div className="flex md:hidden items-center gap-2 z-20">
          <DropdownMenu open={isMenuOpen} onOpenChange={setIsMenuOpen}>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="rounded-full">
                {isMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
                <span className="sr-only">Menu</span>
              </Button>
            </DropdownMenuTrigger>
            
            <DropdownMenuContent align="end" className="w-screen bg-white border-b border-brand-beige/50 mt-4 -mr-4 rounded-none p-0 shadow-lg" sideOffset={8}>
              <div className="py-6 px-6 flex flex-col gap-4">
                {navItems.map((item) => (
                  <DropdownMenuItem key={item.path} asChild className="py-3 border-b border-brand-beige/30 min-h-12 text-base focus:bg-brand-beige/10">
                    <Link to={item.path} onClick={() => setIsMenuOpen(false)} className="flex items-center gap-3">
                      <item.icon className="h-5 w-5" />
                      <span>{item.label}</span>
                    </Link>
                  </DropdownMenuItem>
                ))}
                <DropdownMenuItem asChild className="py-3 border-b border-brand-beige/30 min-h-12 text-base focus:bg-brand-beige/10">
                  <Link to="/community" onClick={() => setIsMenuOpen(false)} className="flex items-center gap-3">
                    <MessageSquare className="h-5 w-5" />
                    <span>Community</span>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild className="py-3 flex items-center gap-2 min-h-12 text-base focus:bg-brand-beige/10">
                  <Link to="/login" onClick={() => setIsMenuOpen(false)} className="flex items-center gap-3">
                    <User className="h-5 w-5" />
                    <span>Account</span>
                  </Link>
                </DropdownMenuItem>
              </div>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
};

export default Header;
