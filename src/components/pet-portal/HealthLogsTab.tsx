
import React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Calendar, Clock, TrendingUp, FileText, Stethoscope } from "lucide-react";
import { usePetData } from "@/context/PetContext";

export const HealthLogsTab = () => {
  const { petData } = usePetData();

  const recentLogs = [
    {
      id: "1",
      date: "2024-03-15",
      time: "09:30 AM",
      type: "Daily Check-in",
      status: "Normal",
      icon: Calendar,
      summary: `${petData.name} had a great morning with high energy and normal appetite.`,
      aiSummary: "All vitals within normal range. Digestive health shows positive trends.",
      color: "green"
    },
    {
      id: "2", 
      date: "2024-03-14",
      time: "07:45 PM",
      type: "Supplement Log",
      status: "Completed",
      icon: FileText,
      summary: "Evening supplements administered as scheduled.",
      aiSummary: "Supplement adherence remains excellent at 95% this week.",
      color: "blue"
    },
    {
      id: "3",
      date: "2024-03-14",
      time: "02:15 PM", 
      type: "Vet Visit",
      status: "Follow-up",
      icon: Stethoscope,
      summary: "Routine checkup with Dr. <PERSON>. All vitals normal.",
      aiSummary: `Weight stable at ${petData.weight || 'target'} lbs. Recommended continuing current wellness plan.`,
      color: "purple"
    },
    {
      id: "4",
      date: "2024-03-13",
      time: "11:20 AM",
      type: "Daily Check-in", 
      status: "Alert",
      icon: TrendingUp,
      summary: "Slight decrease in appetite noted.",
      aiSummary: "Minor digestive sensitivity detected. Monitoring recommended for 24-48 hours.",
      color: "orange"
    }
  ];

  const getStatusColor = (color: string) => {
    const colors = {
      green: "bg-green-100 text-green-700",
      blue: "bg-blue-100 text-blue-700", 
      purple: "bg-purple-100 text-purple-700",
      orange: "bg-orange-100 text-orange-700"
    };
    return colors[color as keyof typeof colors] || "bg-gray-100 text-gray-700";
  };

  const getIconColor = (color: string) => {
    const colors = {
      green: "text-green-600",
      blue: "text-blue-600",
      purple: "text-purple-600", 
      orange: "text-orange-600"
    };
    return colors[color as keyof typeof colors] || "text-gray-600";
  };

  return (
    <div className="space-y-6">
      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <Calendar className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">This Week</p>
                <p className="text-xl font-bold">7 Logs</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <TrendingUp className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Health Score</p>
                <p className="text-xl font-bold">92%</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Clock className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Last Updated</p>
                <p className="text-xl font-bold">Today</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Health Logs */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Health Logs</CardTitle>
          <CardDescription>Latest health tracking entries for {petData.name}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentLogs.map((log) => {
              const IconComponent = log.icon;
              return (
                <div key={log.id} className="flex gap-4 p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                  <div className={`p-2 rounded-lg bg-gray-50 ${getIconColor(log.color)}`}>
                    <IconComponent className="h-5 w-5" />
                  </div>
                  
                  <div className="flex-1 space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <h4 className="font-medium">{log.type}</h4>
                        <Badge variant="secondary" className={getStatusColor(log.color)}>
                          {log.status}
                        </Badge>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {log.date} at {log.time}
                      </div>
                    </div>
                    
                    <p className="text-sm text-gray-700">{log.summary}</p>
                    
                    <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                      <p className="text-sm font-medium text-blue-900 mb-1">AI Health Summary</p>
                      <p className="text-sm text-blue-700">{log.aiSummary}</p>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
