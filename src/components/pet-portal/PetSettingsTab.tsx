
import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { 
  User, 
  Download, 
  Share2,
  Trash2,
  Shield
} from "lucide-react";
import { usePetData } from "@/context/PetContext";

export const PetSettingsTab = () => {
  const { petData } = usePetData();

  return (
    <div className="max-w-2xl space-y-4 md:space-y-6">
      {/* Pet Profile Settings */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2">
            <User className="w-5 h-5" />
            Pet Profile
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="pet-name">Pet Name</Label>
              <Input id="pet-name" defaultValue={petData.name} className="mt-1" />
            </div>
            <div>
              <Label htmlFor="pet-breed">Breed</Label>
              <Input id="pet-breed" defaultValue={petData.breed} className="mt-1" />
            </div>
          </div>
          
          <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="pet-age">Age (months)</Label>
              <Input id="pet-age" type="number" defaultValue={petData.age} className="mt-1" />
            </div>
            <div>
              <Label htmlFor="pet-weight">Weight (lbs)</Label>
              <Input id="pet-weight" type="number" defaultValue={petData.weight} className="mt-1" />
            </div>
            <div>
              <Label htmlFor="pet-gender">Gender</Label>
              <select id="pet-gender" className="w-full px-3 py-2 mt-1 border border-gray-300 rounded-md text-sm">
                <option value="male">Male</option>
                <option value="female">Female</option>
                <option value="neutered">Neutered</option>
                <option value="spayed">Spayed</option>
              </select>
            </div>
          </div>
          
          <div>
            <Label htmlFor="health-conditions">Health Conditions</Label>
            <Textarea 
              id="health-conditions" 
              placeholder="List any known health conditions..."
              rows={3}
              className="mt-1"
            />
          </div>
          
          <div>
            <Label htmlFor="dietary-info">Dietary Information</Label>
            <Input 
              id="dietary-info" 
              placeholder="e.g., grain-free, raw, prescription diet"
              className="mt-1"
            />
          </div>
          
          <Button className="w-full sm:w-auto">Save Changes</Button>
        </CardContent>
      </Card>

      {/* Data Management */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            Data Management
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div className="p-4 border rounded-lg">
              <div className="flex items-center gap-3 mb-3">
                <Download className="w-5 h-5 text-blue-500" />
                <div>
                  <div className="font-medium">Export Data</div>
                  <div className="text-sm text-gray-600">Download complete health profile</div>
                </div>
              </div>
              <Button variant="outline" className="w-full">
                Export Health Data
              </Button>
            </div>
            
            <div className="p-4 border rounded-lg">
              <div className="flex items-center gap-3 mb-3">
                <Share2 className="w-5 h-5 text-green-500" />
                <div>
                  <div className="font-medium">Share with Vet</div>
                  <div className="text-sm text-gray-600">Generate shareable report</div>
                </div>
              </div>
              <Button variant="outline" className="w-full">
                Generate Report
              </Button>
            </div>
          </div>
          
          <div className="border-t pt-4">
            <div className="flex items-center gap-3 mb-3">
              <Trash2 className="w-5 h-5 text-red-500" />
              <div>
                <div className="font-medium text-red-700">Delete Pet Profile</div>
                <div className="text-sm text-gray-600">
                  Permanently delete all data for {petData.name}
                </div>
              </div>
            </div>
            <Button variant="destructive" className="w-full sm:w-auto">
              Delete Profile
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
