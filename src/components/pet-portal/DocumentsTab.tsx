
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Upload, 
  FileText, 
  Download, 
  Eye, 
  Plus,
  Search,
  Filter,
  Calendar,
  Stethoscope
} from "lucide-react";
import { usePetData } from "@/context/PetContext";

export const DocumentsTab = () => {
  const { petData } = usePetData();
  const [showUploadForm, setShowUploadForm] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  // Mock data for demonstration
  const documents = [
    {
      id: 1,
      name: "Annual Wellness Exam - 2024",
      category: "vet_report",
      vetName: "<PERSON><PERSON> <PERSON>",
      serviceDate: "2024-01-15",
      fileType: "pdf",
      uploadDate: "2024-01-16",
      tags: ["wellness", "annual", "bloodwork"]
    },
    {
      id: 2,
      name: "Vaccination Records",
      category: "vaccination_proof",
      vetName: "Animal Care Clinic",
      serviceDate: "2023-06-15",
      fileType: "pdf",
      uploadDate: "2023-06-16",
      tags: ["rabies", "dhpp", "vaccines"]
    },
    {
      id: 3,
      name: "Microbiome Test Results",
      category: "microbiome_result",
      vetName: "Lab Corp",
      serviceDate: "2023-12-01",
      fileType: "pdf",
      uploadDate: "2023-12-05",
      tags: ["microbiome", "gut-health", "bacteria"]
    },
    {
      id: 4,
      name: "Blood Panel Results",
      category: "lab_test",
      vetName: "Dr. Johnson",
      serviceDate: "2024-01-10",
      fileType: "pdf",
      uploadDate: "2024-01-12",
      tags: ["bloodwork", "chemistry", "cbc"]
    }
  ];

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'vet_report':
        return <Stethoscope className="w-4 h-4" />;
      case 'vaccination_proof':
        return <Calendar className="w-4 h-4" />;
      default:
        return <FileText className="w-4 h-4" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'vet_report':
        return 'bg-blue-100 text-blue-800';
      case 'vaccination_proof':
        return 'bg-green-100 text-green-800';
      case 'lab_test':
        return 'bg-purple-100 text-purple-800';
      case 'microbiome_result':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatCategoryName = (category: string) => {
    return category.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const filteredDocuments = documents.filter(doc =>
    doc.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    doc.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  return (
    <div className="space-y-6">
      {/* Controls */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex items-center gap-2 flex-1">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search documents..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Button variant="outline" size="sm">
            <Filter className="w-4 h-4 mr-2" />
            Filter
          </Button>
        </div>
        
        <Button onClick={() => setShowUploadForm(!showUploadForm)}>
          <Plus className="w-4 h-4 mr-2" />
          Upload Document
        </Button>
      </div>

      {/* Upload Form */}
      {showUploadForm && (
        <Card>
          <CardHeader>
            <CardTitle>Upload New Document</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <Label htmlFor="file">Select File</Label>
                <Input id="file" type="file" accept=".pdf,.jpg,.jpeg,.png" className="mt-2" />
              </div>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="category">Category</Label>
                  <select id="category" className="w-full mt-2 px-3 py-2 border border-gray-300 rounded-md">
                    <option value="">Select category</option>
                    <option value="vet_report">Vet Report</option>
                    <option value="lab_test">Lab Test</option>
                    <option value="microbiome_result">Microbiome Result</option>
                    <option value="vaccination_proof">Vaccination Proof</option>
                    <option value="other">Other</option>
                  </select>
                </div>
                
                <div>
                  <Label htmlFor="vet-name">Vet/Clinic Name</Label>
                  <Input id="vet-name" placeholder="Enter vet or clinic name" className="mt-2" />
                </div>
              </div>
              
              <div>
                <Label htmlFor="service-date">Service Date</Label>
                <Input id="service-date" type="date" className="mt-2" />
              </div>
              
              <div>
                <Label htmlFor="tags">Tags (comma separated)</Label>
                <Input id="tags" placeholder="e.g., wellness, bloodwork, annual" className="mt-2" />
              </div>
              
              <div className="flex gap-2">
                <Button>
                  <Upload className="w-4 h-4 mr-2" />
                  Upload Document
                </Button>
                <Button variant="outline" onClick={() => setShowUploadForm(false)}>
                  Cancel
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Documents List */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredDocuments.map((document) => (
          <Card key={document.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-4">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center gap-2">
                  {getCategoryIcon(document.category)}
                  <Badge variant="secondary" className={getCategoryColor(document.category)}>
                    {formatCategoryName(document.category)}
                  </Badge>
                </div>
                <div className="text-xs text-gray-500">
                  {document.fileType.toUpperCase()}
                </div>
              </div>
              
              <h3 className="font-medium text-gray-900 mb-2 line-clamp-2">
                {document.name}
              </h3>
              
              <div className="space-y-1 text-sm text-gray-600 mb-3">
                {document.vetName && (
                  <div>Vet: {document.vetName}</div>
                )}
                <div>Service: {document.serviceDate}</div>
                <div>Uploaded: {document.uploadDate}</div>
              </div>
              
              {document.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mb-3">
                  {document.tags.slice(0, 3).map((tag, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                  {document.tags.length > 3 && (
                    <Badge variant="outline" className="text-xs">
                      +{document.tags.length - 3}
                    </Badge>
                  )}
                </div>
              )}
              
              <div className="flex gap-2">
                <Button size="sm" variant="outline" className="flex-1">
                  <Eye className="w-3 h-3 mr-1" />
                  View
                </Button>
                <Button size="sm" variant="outline" className="flex-1">
                  <Download className="w-3 h-3 mr-1" />
                  Download
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredDocuments.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <FileText className="w-16 h-16 mx-auto text-gray-300 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No documents found
            </h3>
            <p className="text-gray-600 mb-4">
              {searchTerm ? 
                "No documents match your search criteria." : 
                `Upload ${petData.name}'s medical records, test results, and other important documents.`
              }
            </p>
            <Button onClick={() => setShowUploadForm(true)}>
              <Upload className="w-4 h-4 mr-2" />
              Upload First Document
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
