
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { 
  Weight, 
  Shield, 
  Pill, 
  Plus, 
  Phone,
  Mail,
  MapPin,
  Upload,
  FileText,
  Download,
  Eye,
  Check,
  AlertCircle
} from "lucide-react";
import { usePetData } from "@/context/PetContext";

export const MedicalRecordsTab = () => {
  const { petData } = usePetData();
  const [showUploadForm, setShowUploadForm] = useState(false);
  
  // Mock data
  const weightHistory = [
    { date: "2024-03-15", weight: 45, notes: "Routine checkup" },
    { date: "2024-03-01", weight: 44.5, notes: "Monthly check" },
    { date: "2024-02-15", weight: 44, notes: "Wellness visit" },
  ];

  const vaccines = [
    { name: "Rabies", status: "current", lastDate: "2023-06-15", dueDate: "2024-06-15" },
    { name: "DHPP", status: "current", lastDate: "2023-06-15", dueDate: "2024-06-15" },
    { name: "Bordetella", status: "due", lastDate: "2023-03-15", dueDate: "2024-03-15" },
  ];

  const medications = [
    { name: "Heartgard Plus", dosage: "1 tablet", frequency: "Monthly", status: "active" },
    { name: "NexGard", dosage: "1 chew", frequency: "Monthly", status: "active" },
  ];

  const vetClinic = {
    name: "City Animal Hospital",
    phone: "(*************",
    email: "<EMAIL>",
    address: "123 Pet Street, City, ST 12345"
  };

  const documents = [
    { name: "Annual Wellness Exam - 2024", date: "2024-01-15", type: "pdf" },
    { name: "Vaccination Records", date: "2023-06-15", type: "pdf" },
    { name: "Blood Panel Results", date: "2024-01-10", type: "pdf" },
  ];

  return (
    <div className="space-y-4 md:space-y-6">
      {/* Vet Contact Info */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle>Veterinary Clinic</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="font-medium text-gray-900">{vetClinic.name}</div>
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <Phone className="w-4 h-4 text-gray-400" />
                <a href={`tel:${vetClinic.phone}`} className="text-blue-600 hover:underline">
                  {vetClinic.phone}
                </a>
              </div>
              <div className="flex items-center gap-2">
                <Mail className="w-4 h-4 text-gray-400" />
                <a href={`mailto:${vetClinic.email}`} className="text-blue-600 hover:underline">
                  {vetClinic.email}
                </a>
              </div>
              <div className="flex items-start gap-2">
                <MapPin className="w-4 h-4 text-gray-400 mt-0.5" />
                <span className="text-gray-600">{vetClinic.address}</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6">
        {/* Weight History */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2">
              <Weight className="w-5 h-5 text-blue-500" />
              Weight History
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {weightHistory.map((entry, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <div className="font-medium">{entry.weight} lbs</div>
                    <div className="text-sm text-gray-600">{entry.date}</div>
                    {entry.notes && (
                      <div className="text-xs text-gray-500">{entry.notes}</div>
                    )}
                  </div>
                  {index === 0 && (
                    <Badge variant="secondary">Current</Badge>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Vaccination Records */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2">
              <Shield className="w-5 h-5 text-green-500" />
              Vaccinations
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {vaccines.map((vaccine, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <div className="font-medium">{vaccine.name}</div>
                    <div className="text-sm text-gray-600">Due: {vaccine.dueDate}</div>
                  </div>
                  <Badge 
                    variant={vaccine.status === 'current' ? 'secondary' : 'destructive'}
                    className={vaccine.status === 'current' ? 'bg-green-100 text-green-800' : ''}
                  >
                    {vaccine.status === 'current' ? (
                      <>
                        <Check className="w-3 h-3 mr-1" />
                        Current
                      </>
                    ) : (
                      <>
                        <AlertCircle className="w-3 h-3 mr-1" />
                        Due
                      </>
                    )}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Current Medications */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2">
              <Pill className="w-5 h-5 text-purple-500" />
              Medications
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {medications.map((medication, index) => (
                <div key={index} className="p-3 bg-gray-50 rounded-lg">
                  <div className="font-medium">{medication.name}</div>
                  <div className="text-sm text-gray-600">
                    {medication.dosage} • {medication.frequency}
                  </div>
                  <Badge variant="secondary" className="bg-green-100 text-green-800 mt-2">
                    Active
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Document Upload & Management */}
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <FileText className="w-5 h-5 text-orange-500" />
                Reports & Documents
              </CardTitle>
              <Button 
                size="sm" 
                onClick={() => setShowUploadForm(!showUploadForm)}
              >
                <Plus className="w-4 h-4 mr-2" />
                Upload
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {showUploadForm && (
              <div className="mb-4 p-3 bg-gray-50 rounded-lg space-y-3">
                <Input type="file" accept=".pdf,.jpg,.jpeg,.png" />
                <Input placeholder="Document name" />
                <div className="flex gap-2">
                  <Button size="sm">Upload</Button>
                  <Button size="sm" variant="outline" onClick={() => setShowUploadForm(false)}>
                    Cancel
                  </Button>
                </div>
              </div>
            )}
            
            <div className="space-y-2">
              {documents.map((doc, index) => (
                <div key={index} className="flex items-center justify-between p-2 hover:bg-gray-50 rounded">
                  <div className="flex-1 min-w-0">
                    <div className="text-sm font-medium truncate">{doc.name}</div>
                    <div className="text-xs text-gray-500">{doc.date}</div>
                  </div>
                  <div className="flex gap-1 ml-2">
                    <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
                      <Eye className="w-3 h-3" />
                    </Button>
                    <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
                      <Download className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
