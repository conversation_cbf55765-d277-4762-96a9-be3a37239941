
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { 
  Weight, 
  Shield, 
  Pill, 
  Plus, 
  Edit, 
  Calendar,
  TrendingUp,
  AlertCircle,
  Check
} from "lucide-react";
import { usePetData } from "@/context/PetContext";

export const VitalsTab = () => {
  const { petData } = usePetData();
  const [showWeightForm, setShowWeightForm] = useState(false);
  const [showMedicationForm, setShowMedicationForm] = useState(false);
  
  // Mock data for demonstration
  const weightHistory = [
    { date: "2024-01-15", weight: 45, notes: "Routine checkup" },
    { date: "2024-01-01", weight: 44.5, notes: "New Year weigh-in" },
    { date: "2023-12-15", weight: 44, notes: "Monthly check" },
  ];

  const vaccines = [
    { name: "Rabies", status: "current", lastDate: "2023-06-15", dueDate: "2024-06-15" },
    { name: "DHPP", status: "current", lastDate: "2023-06-15", dueDate: "2024-06-15" },
    { name: "Bordetella", status: "due", lastDate: "2023-03-15", dueDate: "2024-03-15" },
  ];

  const medications = [
    { name: "Heartgard Plus", dosage: "1 tablet", frequency: "Monthly", status: "active" },
    { name: "NexGard", dosage: "1 chew", frequency: "Monthly", status: "active" },
  ];

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Weight Tracking */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Weight className="w-5 h-5 text-blue-500" />
              Weight History
            </CardTitle>
            <Button 
              size="sm" 
              onClick={() => setShowWeightForm(!showWeightForm)}
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Weight
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {showWeightForm && (
            <div className="mb-4 p-4 bg-gray-50 rounded-lg space-y-3">
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <Label htmlFor="weight">Weight (lbs)</Label>
                  <Input id="weight" type="number" placeholder="45.0" />
                </div>
                <div>
                  <Label htmlFor="date">Date</Label>
                  <Input id="date" type="date" />
                </div>
              </div>
              <div>
                <Label htmlFor="notes">Notes (optional)</Label>
                <Textarea id="notes" placeholder="Any observations..." rows={2} />
              </div>
              <div className="flex gap-2">
                <Button size="sm">Save</Button>
                <Button size="sm" variant="outline" onClick={() => setShowWeightForm(false)}>
                  Cancel
                </Button>
              </div>
            </div>
          )}
          
          <div className="space-y-3">
            {weightHistory.map((entry, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <div className="font-medium">{entry.weight} lbs</div>
                  <div className="text-sm text-gray-600">{entry.date}</div>
                  {entry.notes && (
                    <div className="text-xs text-gray-500">{entry.notes}</div>
                  )}
                </div>
                {index === 0 && (
                  <Badge variant="secondary">
                    <TrendingUp className="w-3 h-3 mr-1" />
                    Current
                  </Badge>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Vaccination Records */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5 text-green-500" />
            Vaccination Records
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {vaccines.map((vaccine, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <div className="font-medium">{vaccine.name}</div>
                  <div className="text-sm text-gray-600">
                    Last: {vaccine.lastDate}
                  </div>
                  <div className="text-sm text-gray-600">
                    Due: {vaccine.dueDate}
                  </div>
                </div>
                <Badge 
                  variant={vaccine.status === 'current' ? 'secondary' : 'destructive'}
                  className={vaccine.status === 'current' ? 'bg-green-100 text-green-800' : ''}
                >
                  {vaccine.status === 'current' ? (
                    <>
                      <Check className="w-3 h-3 mr-1" />
                      Current
                    </>
                  ) : (
                    <>
                      <AlertCircle className="w-3 h-3 mr-1" />
                      Due
                    </>
                  )}
                </Badge>
              </div>
            ))}
          </div>
          <Button variant="outline" className="w-full mt-4">
            <Plus className="w-4 h-4 mr-2" />
            Add Vaccination Record
          </Button>
        </CardContent>
      </Card>

      {/* Current Medications */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Pill className="w-5 h-5 text-purple-500" />
              Current Medications
            </CardTitle>
            <Button 
              size="sm" 
              onClick={() => setShowMedicationForm(!showMedicationForm)}
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Medication
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {showMedicationForm && (
            <div className="mb-4 p-4 bg-gray-50 rounded-lg space-y-3">
              <div>
                <Label htmlFor="med-name">Medication Name</Label>
                <Input id="med-name" placeholder="Enter medication name" />
              </div>
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <Label htmlFor="dosage">Dosage</Label>
                  <Input id="dosage" placeholder="e.g., 1 tablet" />
                </div>
                <div>
                  <Label htmlFor="frequency">Frequency</Label>
                  <Input id="frequency" placeholder="e.g., Daily" />
                </div>
              </div>
              <div className="flex gap-2">
                <Button size="sm">Save</Button>
                <Button size="sm" variant="outline" onClick={() => setShowMedicationForm(false)}>
                  Cancel
                </Button>
              </div>
            </div>
          )}
          
          <div className="space-y-3">
            {medications.map((medication, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <div className="font-medium">{medication.name}</div>
                  <div className="text-sm text-gray-600">
                    {medication.dosage} • {medication.frequency}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary" className="bg-green-100 text-green-800">
                    Active
                  </Badge>
                  <Button size="sm" variant="ghost">
                    <Edit className="w-3 h-3" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Body Condition & Microchip */}
      <Card>
        <CardHeader>
          <CardTitle>Additional Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label>Body Condition Score (1-9)</Label>
            <div className="flex items-center gap-2 mt-2">
              <Input type="number" min="1" max="9" placeholder="5" className="w-20" />
              <span className="text-sm text-gray-600">Current: Not set</span>
            </div>
          </div>
          
          <div>
            <Label>Microchip ID</Label>
            <Input placeholder="Enter microchip number" className="mt-2" />
          </div>
          
          <Button className="w-full">
            Update Information
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};
