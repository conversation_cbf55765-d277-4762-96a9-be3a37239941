
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { 
  Activity, 
  Heart, 
  Calendar, 
  TrendingUp, 
  AlertCircle,
  CheckCircle,
  Clock,
  Zap
} from "lucide-react";
import { usePetData } from "@/context/PetContext";

export const OverviewTab = () => {
  const { petData } = usePetData();

  // Mock data for demonstration
  const todayShapeScore = 4.2;
  const lastCheckIn = "2 hours ago";
  const recentActivity = [
    { type: "check-in", description: "Completed daily SHAPE check-in", time: "2 hours ago", status: "completed" },
    { type: "supplement", description: "Gave morning supplements", time: "5 hours ago", status: "completed" },
    { type: "weight", description: "Weight recorded: 45 lbs", time: "3 days ago", status: "completed" },
  ];

  const healthS<PERSON><PERSON>y = {
    sleep: 4,
    hydration: 5,
    activity: 3,
    poop: 4,
    eating: 5
  };

  const upcomingReminders = [
    { type: "vaccine", description: "Rabies vaccine due", date: "In 2 weeks", priority: "medium" },
    { type: "checkup", description: "Annual wellness exam", date: "Next month", priority: "low" },
  ];

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Health Status Summary */}
      <div className="lg:col-span-2 space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Heart className="w-5 h-5 text-red-500" />
              Today's Health Summary
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between mb-4">
              <div>
                <div className="text-3xl font-bold text-brand-primary">
                  {Math.round(todayShapeScore * 20)}
                </div>
                <div className="text-sm text-gray-600">SHAPE Score</div>
              </div>
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                <TrendingUp className="w-3 h-3 mr-1" />
                Improving
              </Badge>
            </div>
            
            <div className="grid grid-cols-5 gap-4 mb-4">
              {Object.entries(healthSummary).map(([category, score]) => (
                <div key={category} className="text-center">
                  <div className="text-lg font-bold text-gray-900">{score}</div>
                  <div className="text-xs text-gray-600 capitalize">{category}</div>
                  <Progress value={score * 20} className="h-1 mt-1" />
                </div>
              ))}
            </div>
            
            <div className="text-sm text-gray-600">
              Last check-in: {lastCheckIn}
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="w-5 h-5 text-blue-500" />
              Recent Activity
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivity.map((activity, index) => (
                <div key={index} className="flex items-start gap-3">
                  <div className="mt-1">
                    {activity.status === 'completed' ? (
                      <CheckCircle className="w-4 h-4 text-green-500" />
                    ) : (
                      <Clock className="w-4 h-4 text-gray-400" />
                    )}
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">
                      {activity.description}
                    </p>
                    <p className="text-xs text-gray-500">{activity.time}</p>
                  </div>
                </div>
              ))}
            </div>
            <Button variant="outline" className="w-full mt-4">
              View Full Activity Log
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Sidebar */}
      <div className="space-y-6">
        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="w-5 h-5 text-yellow-500" />
              Quick Actions
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button className="w-full justify-start">
              <Activity className="w-4 h-4 mr-2" />
              Daily Check-In
            </Button>
            <Button variant="outline" className="w-full justify-start">
              <Calendar className="w-4 h-4 mr-2" />
              Log Weight
            </Button>
            <Button variant="outline" className="w-full justify-start">
              <Heart className="w-4 h-4 mr-2" />
              Add Health Note
            </Button>
          </CardContent>
        </Card>

        {/* Upcoming Reminders */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertCircle className="w-5 h-5 text-orange-500" />
              Upcoming Reminders
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {upcomingReminders.map((reminder, index) => (
                <div key={index} className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                  <AlertCircle className={`w-4 h-4 mt-0.5 ${
                    reminder.priority === 'high' ? 'text-red-500' : 
                    reminder.priority === 'medium' ? 'text-orange-500' : 
                    'text-blue-500'
                  }`} />
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">
                      {reminder.description}
                    </p>
                    <p className="text-xs text-gray-500">{reminder.date}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* AI Insights */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Ask AI About {petData.name}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-xs">
              <Button variant="ghost" size="sm" className="w-full justify-start text-xs h-8">
                "How is {petData.name}'s activity level?"
              </Button>
              <Button variant="ghost" size="sm" className="w-full justify-start text-xs h-8">
                "Any health concerns this week?"
              </Button>
              <Button variant="ghost" size="sm" className="w-full justify-start text-xs h-8">
                "Supplement recommendations?"
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
