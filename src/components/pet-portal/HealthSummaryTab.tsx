
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Activity, 
  Heart, 
  Calendar, 
  TrendingUp, 
  AlertCircle,
  CheckCircle,
  Clock,
  Plus,
  Weight
} from "lucide-react";
import { usePetData } from "@/context/PetContext";

export const HealthSummaryTab = () => {
  const { petData } = usePetData();
  const [showWeightForm, setShowWeightForm] = useState(false);

  // Mock data - in real app would come from context/database
  const averageShapeScore = 85;
  const currentWeight = petData.weight || 45;
  const lastWeightDate = "2024-03-15";
  
  const recentActivity = [
    { type: "check-in", description: "Completed daily SHAPE check-in", time: "2 hours ago", status: "completed" },
    { type: "weight", description: `Weight recorded: ${currentWeight} lbs`, time: "3 days ago", status: "completed" },
    { type: "supplement", description: "Gave morning supplements", time: "5 hours ago", status: "completed" },
  ];

  const upcomingReminders = [
    { type: "vaccine", description: "Rabies vaccine due", date: "In 2 weeks", priority: "medium" },
    { type: "checkup", description: "Annual wellness exam", date: "Next month", priority: "low" },
    { type: "weight", description: "Weekly weight check", date: "Tomorrow", priority: "high" },
  ];

  const handleWeightSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setShowWeightForm(false);
    // Handle weight submission
  };

  return (
    <div className="space-y-4 md:space-y-6">
      {/* SHAPE Score Card */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2">
            <Heart className="w-5 h-5 text-red-500" />
            Average SHAPE Score
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-4">
            <div>
              <div className="text-3xl md:text-4xl font-bold text-brand-primary">
                {averageShapeScore}
              </div>
              <div className="text-sm text-gray-600">Last 30 days</div>
            </div>
            <Badge variant="secondary" className="bg-green-100 text-green-800">
              <TrendingUp className="w-3 h-3 mr-1" />
              Excellent
            </Badge>
          </div>
          <div className="text-sm text-gray-600">
            Based on daily Sleep, Hydration, Activity, Poop, and Eating scores
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6">
        {/* Quick Weight Logging */}
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Weight className="w-5 h-5 text-blue-500" />
                Weight Tracking
              </CardTitle>
              <Button 
                size="sm" 
                onClick={() => setShowWeightForm(!showWeightForm)}
              >
                <Plus className="w-4 h-4 mr-2" />
                Log Weight
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {showWeightForm && (
              <form onSubmit={handleWeightSubmit} className="mb-4 p-3 bg-gray-50 rounded-lg space-y-3">
                <div className="grid grid-cols-2 gap-3">
                  <Input type="number" step="0.1" placeholder="Weight (lbs)" />
                  <Input type="date" />
                </div>
                <div className="flex gap-2">
                  <Button type="submit" size="sm">Save</Button>
                  <Button type="button" size="sm" variant="outline" onClick={() => setShowWeightForm(false)}>
                    Cancel
                  </Button>
                </div>
              </form>
            )}
            
            <div className="space-y-2">
              <div className="text-2xl font-bold">{currentWeight} lbs</div>
              <div className="text-sm text-gray-600">Last recorded: {lastWeightDate}</div>
              <div className="text-xs text-green-600">↗ +0.5 lbs from last month</div>
            </div>
          </CardContent>
        </Card>

        {/* Upcoming Reminders */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2">
              <AlertCircle className="w-5 h-5 text-orange-500" />
              Upcoming Reminders
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {upcomingReminders.map((reminder, index) => (
                <div key={index} className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                  <AlertCircle className={`w-4 h-4 mt-0.5 flex-shrink-0 ${
                    reminder.priority === 'high' ? 'text-red-500' : 
                    reminder.priority === 'medium' ? 'text-orange-500' : 
                    'text-blue-500'
                  }`} />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {reminder.description}
                    </p>
                    <p className="text-xs text-gray-500">{reminder.date}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Health Activity */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2">
            <Activity className="w-5 h-5 text-blue-500" />
            Recent Health Activity
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {recentActivity.map((activity, index) => (
              <div key={index} className="flex items-start gap-3 py-2">
                <div className="mt-1">
                  {activity.status === 'completed' ? (
                    <CheckCircle className="w-4 h-4 text-green-500" />
                  ) : (
                    <Clock className="w-4 h-4 text-gray-400" />
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900">
                    {activity.description}
                  </p>
                  <p className="text-xs text-gray-500">{activity.time}</p>
                </div>
              </div>
            ))}
          </div>
          <Button variant="outline" className="w-full mt-4">
            View Complete History
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};
