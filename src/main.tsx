
import { createRoot } from 'react-dom/client'
import App from './App.tsx'
import './index.css'
import { AuthProvider } from '@/contexts/AuthContext'

// Add Google Fonts
const googleFonts = document.createElement('link')
googleFonts.rel = 'stylesheet'
googleFonts.href = 'https://fonts.googleapis.com/css2?family=Averia+Serif+Libre:wght@400;700&family=Merriweather:wght@400;700&display=swap'
document.head.appendChild(googleFonts)

// Create a preloaded image for the logo to ensure it loads
const preloadLogo = document.createElement('link')
preloadLogo.rel = 'preload'
preloadLogo.as = 'image'
preloadLogo.href = '/lovable-uploads/a64807e2-afb3-4b32-9ace-929a37277b41.png'
document.head.appendChild(preloadLogo)

createRoot(document.getElementById("root")!).render(
  <AuthProvider>
    <App />
  </AuthProvider>
);
