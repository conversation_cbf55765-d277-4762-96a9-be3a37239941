
import { Step, PetType, PetSex } from "../types";

export const validateStep = (
  step: Step,
  { petType, petName, petAge, petSex }: { 
    petType: PetType | null; 
    petName: string; 
    petAge: string; 
    petSex: PetSex | null;
  }
): boolean => {
  switch (step) {
    case 1:
      if (!petType) {
        return false;
      }
      if (!petName.trim()) {
        return false;
      }
      return true;
      
    case 2:
      if (!petAge) {
        return false;
      }
      if (!petSex) {
        return false;
      }
      return true;
      
    case 3:
      // Membership selection doesn't have required fields
      return true;
      
    case 4:
      // Account validation is now handled by the form with real-time validation
      return true;
      
    case 5:
      // Success screen has no validation
      return true;
      
    default:
      return true;
  }
};

export const getStepCompletionMessage = (step: number, petName: string): string => {
  switch (step) {
    case 1:
      return `${petName}'s basic information saved.`;
    case 2:
      return `${petName}'s health profile created.`;
    case 3:
      return "Membership selection saved.";
    case 4:
      return "Account created successfully!";
    default:
      return "Step completed successfully.";
  }
};

// Helper function to check if email is valid
export const isValidEmail = (email: string): boolean => {
  const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return regex.test(email);
};

// Helper function to check if password meets minimum requirements
export const isValidPassword = (password: string): boolean => {
  return password.length >= 8;
};
