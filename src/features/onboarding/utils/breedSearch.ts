
import Fuse from 'fuse.js';

// Main list of dog breeds
export const DOG_BREEDS = [
  "Airedale Terrier", "Akita", "Alaskan Malamute", "American Bulldog", "American Cocker Spaniel",
  "American Eskimo Dog", "American Foxhound", "American Pit Bull Terrier", "American Staffordshire Terrier",
  "Anatolian Shepherd", "Australian Cattle Dog", "Australian Shepherd", "Australian Terrier", "Basenji",
  "Basset Hound", "Beagle", "Bearded Collie", "Beauceron", "Bedlington Terrier", "Belgian Malinois",
  "Belgian Sheepdog", "Belgian Tervuren", "Bernedoodle", "Bernese Mountain Dog", "Bichon Frise",
  "Black and Tan Coonhound", "Bloodhound", "Border Collie", "Border Terrier", "Borzoi",
  "Boston Terrier", "Bouvier des Flandres", "Boxer", "Boykin Spaniel", "Briard",
  "Brittany", "Brussels Griffon", "Bull Terrier", "Bulldog (English)", "Bullmastiff",
  "Cairn Terrier", "Cane Corso", "Cardigan Welsh Corgi", "Cavalier King Charles Spaniel", "Chesapeake Bay Retriever",
  "Chihuahua", "Chinese Crested", "Chinese Shar-Pei", "Chow Chow", "Clumber Spaniel",
  "Cockapoo", "Cocker Spaniel (American)", "Collie", "Coonhound", "Coton de Tulear",
  "Curly-Coated Retriever", "Dachshund", "Dalmatian", "Dandie Dinmont Terrier", "Doberman Pinscher",
  "Dogo Argentino", "Dutch Shepherd", "English Cocker Spaniel", "English Foxhound", "English Setter",
  "English Springer Spaniel", "English Toy Spaniel", "Entlebucher Mountain Dog", "Field Spaniel", "Finnish Lapphund",
  "Finnish Spitz", "Flat-Coated Retriever", "French Bulldog", "German Pinscher", "German Shepherd Dog",
  "German Shorthaired Pointer", "German Wirehaired Pointer", "Giant Schnauzer", "Glen of Imaal Terrier", "Goldador",
  "Golden Retriever", "Goldendoodle", "Gordon Setter", "Great Dane", "Great Pyrenees",
  "Greater Swiss Mountain Dog", "Greyhound", "Havanese", "Irish Setter", "Irish Terrier",
  "Irish Wolfhound", "Italian Greyhound", "Jack Russell Terrier", "Japanese Chin", "Keeshond",
  "Kerry Blue Terrier", "King Charles Spaniel", "Komondor", "Kuvasz", "Labradoodle",
  "Labrador Retriever"
];

export const CAT_BREEDS = [
  "Abyssinian", "American Bobtail", "American Curl", "American Shorthair", "American Wirehair",
  "Balinese", "Bengal", "Birman", "Bombay", "British Shorthair", 
  "Burmese", "Chartreux", "Cornish Rex", "Devon Rex", "Egyptian Mau",
  "European Burmese", "Exotic Shorthair", "Havana Brown", "Himalayan", "Japanese Bobtail",
  "Korat", "Maine Coon", "Manx", "Norwegian Forest Cat", "Ocicat",
  "Oriental", "Persian", "Ragdoll", "Russian Blue", "Scottish Fold",
  "Siamese", "Siberian", "Singapura", "Somali", "Sphynx",
  "Tonkinese", "Turkish Angora", "Turkish Van"
];

// Common aliases and nicknames for dog breeds
const DOG_BREED_ALIASES: Record<string, string[]> = {
  "Labrador Retriever": ["Lab", "Labrador", "Yellow Lab", "Chocolate Lab", "Black Lab", "Labrodor"],
  "Golden Retriever": ["Golden", "GR", "Goldie"],
  "German Shepherd Dog": ["German Shepherd", "GSD", "Alsatian", "Shepherd"],
  "Bernese Mountain Dog": ["Berner", "BMD"],
  "Bulldog (English)": ["English Bulldog", "Bulldog", "British Bulldog"],
  "Poodle": ["Standard Poodle", "Miniature Poodle", "Toy Poodle"],
  "Dachshund": ["Wiener Dog", "Sausage Dog", "Hot Dog", "Doxie"],
  "Yorkshire Terrier": ["Yorkie"],
  "Chihuahua": ["Chi"],
  "Great Dane": ["Dane"],
  "Doberman Pinscher": ["Doberman", "Dobie"],
  "Australian Shepherd": ["Aussie"],
  "Siberian Husky": ["Husky"],
  "Boxer": ["Box"],
  "Cavalier King Charles Spaniel": ["Cavalier", "CKCS"],
  "Shih Tzu": ["Shitzu"],
  "Boston Terrier": ["Boston"],
  "Pomeranian": ["Pom"],
  "French Bulldog": ["Frenchie"],
  "Cocker Spaniel (American)": ["Cocker Spaniel", "Cocker"]
};

// Additional custom breeds/options
export const CUSTOM_BREEDS = ["Mixed Breed", "Other"];

// Define the interface for breed results
export interface BreedResult {
  name: string;
  type: 'standard' | 'alias' | 'custom';
  canonical?: string; // Optional property for aliased breeds
}

// Define the interface for breed data used in fuzzy search
interface BreedData {
  name: string;
  type: 'standard' | 'alias' | 'custom';
  canonical?: string;
}

// Initialize Fuse for dog breeds
const fuseOptionsDog = {
  includeScore: true,
  threshold: 0.4,
  keys: ['name']
};

// Setup dog breed data for fuzzy search
const dogBreedData: BreedData[] = [
  ...DOG_BREEDS.map(breed => ({ name: breed, type: 'standard' as const })),
  ...CUSTOM_BREEDS.map(breed => ({ name: breed, type: 'custom' as const })),
];

// Add aliases to the searchable data
Object.entries(DOG_BREED_ALIASES).forEach(([breed, aliases]) => {
  aliases.forEach(alias => {
    dogBreedData.push({ name: alias, type: 'alias' as const, canonical: breed });
  });
});

const dogFuse = new Fuse(dogBreedData, fuseOptionsDog);

// Initialize Fuse for cat breeds
const fuseOptionsCat = {
  includeScore: true,
  threshold: 0.4,
  keys: ['name']
};

const catBreedData: BreedData[] = [
  ...CAT_BREEDS.map(breed => ({ name: breed, type: 'standard' as const })),
  ...CUSTOM_BREEDS.map(breed => ({ name: breed, type: 'custom' as const })),
];

const catFuse = new Fuse(catBreedData, fuseOptionsCat);

// Helper to debounce function calls
export function debounce<F extends (...args: any[]) => any>(
  func: F, 
  waitFor: number
): (...args: Parameters<F>) => void {
  let timeout: ReturnType<typeof setTimeout> | null = null;
  
  return (...args: Parameters<F>): void => {
    if (timeout !== null) {
      clearTimeout(timeout);
    }
    timeout = setTimeout(() => func(...args), waitFor);
  };
}

export function searchBreeds(query: string, petType: "dog" | "cat" | null): BreedResult[] {
  if (!query.trim()) {
    return [];
  }

  const fuse = petType === "cat" ? catFuse : dogFuse;
  const results = fuse.search(query);
  
  // Map the results to our BreedResult interface
  return results.map(result => {
    const item = result.item;
    
    // If it's an alias, return the canonical name
    if (item.type === 'alias' && item.canonical) {
      return {
        name: item.canonical,
        type: 'standard',
      };
    }
    
    return {
      name: item.name,
      type: item.type as 'standard' | 'custom',
    };
  });
}

// Get the canonical breed name (resolving aliases)
export function getCanonicalBreedName(breed: string, petType: "dog" | "cat" | null): string {
  if (!breed || !petType) return breed;
  
  // First check if it's already a standard breed
  const standardBreeds = petType === "dog" ? DOG_BREEDS : CAT_BREEDS;
  if (standardBreeds.includes(breed) || CUSTOM_BREEDS.includes(breed)) {
    return breed;
  }
  
  // Then check if it's an alias
  if (petType === "dog") {
    for (const [canonical, aliases] of Object.entries(DOG_BREED_ALIASES)) {
      if (aliases.some(alias => alias.toLowerCase() === breed.toLowerCase())) {
        return canonical;
      }
    }
  }
  
  // If not found, return the original
  return breed;
}
