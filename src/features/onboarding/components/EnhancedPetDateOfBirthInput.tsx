
import React from "react";
import SimpleDateInput from "./SimpleDateInput";
import { useIsMobile } from "@/hooks/use-mobile";

interface EnhancedPetDateOfBirthInputProps {
  petName: string;
  dateOfBirth: Date | null;
  onDateChange: (date: Date | null) => void;
}

const EnhancedPetDateOfBirthInput: React.FC<EnhancedPetDateOfBirthInputProps> = ({
  petName,
  dateOfBirth,
  onDateChange
}) => {
  const isMobile = useIsMobile();

  return (
    <div className="w-full">
      <SimpleDateInput
        petName={petName}
        dateOfBirth={dateOfBirth}
        onDateChange={onDateChange}
        className={isMobile ? "px-4" : ""}
      />
    </div>
  );
};

export default EnhancedPetDateOfBirthInput;
