
import { z } from "zod";

// Enhanced validation schema with better error messages
export const accountFormSchema = z.object({
  fullName: z.string()
    .min(2, "Full name must be at least 2 characters")
    .max(50, "Full name must be less than 50 characters")
    .regex(/^[a-zA-Z\s]+$/, "Full name can only contain letters and spaces"),
  email: z.string()
    .email("Please enter a valid email address (e.g., <EMAIL>)")
    .min(5, "Email address is required"),
  password: z.string()
    .min(8, "Password must be at least 8 characters")
    .regex(/[A-Za-z]/, "Password must contain at least one letter")
    .regex(/[0-9]/, "Password must contain at least one number"),
  phoneNumber: z.string()
    .min(10, "Phone number must be at least 10 digits")
    .regex(/^[\+]?[1-9][\d]{0,15}$/, "Please enter a valid phone number"),
  addressLine1: z.string()
    .min(5, "Street address is required (minimum 5 characters)")
    .max(100, "Street address is too long"),
  addressLine2: z.string().optional(),
  city: z.string()
    .min(2, "City name is required")
    .max(50, "City name is too long"),
  state: z.string()
    .min(2, "Please select your state")
    .max(2, "State must be a 2-letter abbreviation"),
  zipCode: z.string()
    .min(5, "ZIP code must be at least 5 digits")
    .regex(/^\d{5}(-\d{4})?$/, "ZIP code must be in format 12345 or 12345-6789"),
  marketingConsent: z.enum(["yes", "no"], {
    required_error: "Please select your marketing preference"
  })
});

export type AccountFormValues = z.infer<typeof accountFormSchema>;
