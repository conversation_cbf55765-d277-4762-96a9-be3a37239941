
import React from "react";
import { CheckCircle2 } from "lucide-react";

interface FormProgressIndicatorProps {
  completionPercentage: number;
}

const FormProgressIndicator: React.FC<FormProgressIndicatorProps> = ({ 
  completionPercentage 
}) => {
  return (
    <div className="mt-4 p-3 bg-muted rounded-lg">
      <div className="flex items-center justify-between mb-2">
        <span className="text-sm font-medium">Progress</span>
        <span className="text-sm text-muted-foreground">{completionPercentage}% complete</span>
      </div>
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div 
          className="bg-brand-primary h-2 rounded-full transition-all duration-300" 
          style={{ width: `${completionPercentage}%` }}
        />
      </div>
      {completionPercentage === 100 && (
        <div className="flex items-center mt-2 text-green-600 text-sm">
          <CheckCircle2 className="h-4 w-4 mr-1" />
          All required fields completed!
        </div>
      )}
    </div>
  );
};

export default FormProgressIndicator;
