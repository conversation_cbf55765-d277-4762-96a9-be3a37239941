
import React, { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { FormField, FormItem, FormControl, FormMessage, FormDescription, FormLabel } from "@/components/ui/form";
import { Eye, EyeOff, Lock, CheckCircle2, XCircle, Shield } from "lucide-react";
import { Control, useWatch } from "react-hook-form";
import { useIsMobile } from "@/hooks/use-mobile";
import { Progress } from "@/components/ui/progress";
import { cn } from "@/lib/utils";

interface PasswordInputProps {
  control: Control<any>;
}

const PasswordInput: React.FC<PasswordInputProps> = ({ control }) => {
  const [showPassword, setShowPassword] = useState(false);
  const isMobile = useIsMobile();
  
  // Watch password field for real-time validation
  const password = useWatch({
    control,
    name: "password",
  });
  
  // Password strength calculation
  const [strength, setStrength] = useState(0);
  
  useEffect(() => {
    if (!password) {
      setStrength(0);
      return;
    }
    
    let currentStrength = 0;
    
    // Basic length check
    if (password.length >= 8) {
      currentStrength += 50; // Base 50% for minimum length
      
      // Optional additional strength factors
      if (password.length > 12) currentStrength += 10;
      if (/[A-Z]/.test(password)) currentStrength += 15;
      if (/[0-9]/.test(password)) currentStrength += 15;
      if (/[^A-Za-z0-9]/.test(password)) currentStrength += 10;
    } else {
      // Partial progress toward minimum length
      currentStrength = Math.min((password.length / 8) * 40, 40);
    }
    
    setStrength(Math.min(currentStrength, 100));
  }, [password]);
  
  const getStrengthLabel = () => {
    if (strength === 0) return "Enter password";
    if (strength < 40) return "Weak";
    if (strength < 75) return "Good";
    return "Strong";
  };
  
  const getStrengthColor = () => {
    if (strength === 0) return "bg-gray-300";
    if (strength < 40) return "bg-red-500";
    if (strength < 75) return "bg-yellow-500";
    return "bg-green-500";
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };
  
  const meetsMinimumRequirements = password && password.length >= 8;

  return (
    <div className="space-y-6">
      <div className="space-y-1 mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Account Security</h3>
        <p className="text-sm text-gray-600 flex items-center gap-1">
          <Shield className="h-4 w-4 text-brand-primary" />
          Create a secure password to protect your account
        </p>
      </div>

      <FormField
        control={control}
        name="password"
        render={({ field, fieldState }) => (
          <FormItem>
            <FormLabel className="text-sm font-medium text-gray-700">Password</FormLabel>
            <FormControl>
              <div className="relative group">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  placeholder="Create a secure password (minimum 8 characters)"
                  className={cn(
                    "pr-20 pl-11 h-12 text-base transition-all duration-200",
                    "border-gray-300 focus:border-brand-primary focus:ring-2 focus:ring-brand-primary/20",
                    "group-hover:border-gray-400",
                    fieldState.error && "border-red-300 focus:border-red-500 focus:ring-red-200",
                    !fieldState.error && meetsMinimumRequirements && "border-green-300 focus:border-green-500"
                  )}
                  {...field}
                />
                <Lock className={cn(
                  "absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 transition-colors duration-200",
                  fieldState.error ? "text-red-400" : 
                  !fieldState.error && meetsMinimumRequirements ? "text-green-500" : "text-gray-400"
                )} />
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center gap-2">
                  {meetsMinimumRequirements && (
                    <CheckCircle2 className="h-5 w-5 text-green-500" />
                  )}
                  <button
                    type="button"
                    onClick={togglePasswordVisibility}
                    className="text-gray-400 hover:text-gray-600 transition-colors duration-200"
                    aria-label={showPassword ? "Hide password" : "Show password"}
                  >
                    {showPassword ? (
                      <EyeOff className="h-5 w-5" />
                    ) : (
                      <Eye className="h-5 w-5" />
                    )}
                  </button>
                </div>
              </div>
            </FormControl>
            
            {/* Password strength meter */}
            <div className="mt-3 space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-xs font-medium text-gray-600">
                  Password strength: <span className={cn(
                    "font-semibold",
                    strength === 0 ? "text-gray-500" :
                    strength < 40 ? "text-red-600" :
                    strength < 75 ? "text-yellow-600" : "text-green-600"
                  )}>{getStrengthLabel()}</span>
                </span>
                {meetsMinimumRequirements && (
                  <span className="flex items-center gap-1 text-xs text-green-600 font-medium">
                    <CheckCircle2 className="h-3 w-3" /> Secure
                  </span>
                )}
              </div>
              <div className="relative">
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className={cn(
                      "h-2 rounded-full transition-all duration-300",
                      getStrengthColor()
                    )}
                    style={{ width: `${strength}%` }}
                  />
                </div>
              </div>
            </div>
            
            {/* Requirements list */}
            <FormDescription className="mt-3 space-y-1">
              <span className="flex items-center gap-2 text-xs">
                {password && password.length >= 8 ? (
                  <CheckCircle2 className="h-3.5 w-3.5 text-green-600 flex-shrink-0" />
                ) : (
                  <XCircle className={cn(
                    "h-3.5 w-3.5 flex-shrink-0",
                    password ? 'text-red-500' : 'text-gray-400'
                  )} />
                )}
                <span className={cn(
                  password && password.length >= 8 ? "text-green-700 font-medium" : "text-gray-600"
                )}>
                  At least 8 characters
                </span>
              </span>
              <p className="text-xs text-gray-500 mt-2">
                💡 Tip: Use a mix of letters, numbers, and symbols for better security
              </p>
            </FormDescription>
            
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};

export default PasswordInput;
