
import React from "react";
import { Card } from "@/components/ui/card";
import { useIsMobile } from "@/hooks/use-mobile";
import HealthIssuesSelector from "../HealthIssuesSelector";

interface HealthIssuesSectionProps {
  petName: string;
  healthIssues: string[];
  handleCheckboxChange: (issue: string) => void;
}

const HealthIssuesSection: React.FC<HealthIssuesSectionProps> = ({
  petName,
  healthIssues,
  handleCheckboxChange
}) => {
  const isMobile = useIsMobile();

  return (
    <Card className="bg-white p-6">
      <div className={`${isMobile ? 'space-y-3' : 'space-y-4'}`}>
        <HealthIssuesSelector
          petName={petName}
          healthIssues={healthIssues}
          handleCheckboxChange={handleCheckboxChange}
        />
      </div>
    </Card>
  );
};

export default HealthIssuesSection;
