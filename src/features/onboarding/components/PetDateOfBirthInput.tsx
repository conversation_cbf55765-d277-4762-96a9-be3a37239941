
import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { useIsMobile } from "@/hooks/use-mobile";

interface PetDateOfBirthInputProps {
  petName: string;
  dateOfBirth: Date | null;
  onDateChange: (date: Date | null) => void;
}

const PetDateOfBirthInput: React.FC<PetDateOfBirthInputProps> = ({
  petName,
  dateOfBirth,
  onDateChange
}) => {
  const isMobile = useIsMobile();
  const [showPicker, setShowPicker] = useState(false);
  const [selectedMonth, setSelectedMonth] = useState(dateOfBirth?.getMonth() || new Date().getMonth());
  const [selectedDay, setSelectedDay] = useState(dateOfBirth?.getDate() || 1);
  const [selectedYear, setSelectedYear] = useState(dateOfBirth?.getFullYear() || new Date().getFullYear());

  const months = [
    "Jan", "Feb", "Mar", "Apr", "May", "Jun",
    "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"
  ];

  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: currentYear - 1999 }, (_, i) => currentYear - i);
  
  const getDaysInMonth = (month: number, year: number) => {
    return new Date(year, month + 1, 0).getDate();
  };

  const days = Array.from({ length: getDaysInMonth(selectedMonth, selectedYear) }, (_, i) => i + 1);

  // Calculate age in decimal years
  const calculateAge = (birthDate: Date): string => {
    const today = new Date();
    const ageInMonths = (today.getFullYear() - birthDate.getFullYear()) * 12 + 
                       (today.getMonth() - birthDate.getMonth());
    
    if (ageInMonths < 12) {
      return `${ageInMonths} month${ageInMonths !== 1 ? 's' : ''} old`;
    } else {
      const years = ageInMonths / 12;
      const roundedYears = Math.round(years * 10) / 10; // Round to 1 decimal place
      return `${roundedYears} year${roundedYears !== 1 ? 's' : ''} old`;
    }
  };

  useEffect(() => {
    if (dateOfBirth) {
      setSelectedMonth(dateOfBirth.getMonth());
      setSelectedDay(dateOfBirth.getDate());
      setSelectedYear(dateOfBirth.getFullYear());
    }
  }, [dateOfBirth]);

  const handleConfirm = () => {
    const newDate = new Date(selectedYear, selectedMonth, selectedDay);
    onDateChange(newDate);
    setShowPicker(false);
  };

  const WheelPicker = ({ items, selectedValue, onValueChange, label }: {
    items: (string | number)[];
    selectedValue: string | number;
    onValueChange: (value: any) => void;
    label: string;
  }) => (
    <div className="flex flex-col items-center">
      <div className="text-xs text-muted-foreground mb-2 font-medium">{label}</div>
      <div className="relative h-32 w-20 overflow-hidden">
        <div className="absolute inset-x-0 top-12 h-8 bg-gradient-to-r from-brand-primary/10 to-brand-primary/10 border-y border-brand-primary/20 rounded"></div>
        <div className="h-full overflow-y-auto scrollbar-hide">
          <div className="py-12">
            {items.map((item, index) => (
              <div
                key={index}
                className={`h-8 flex items-center justify-center cursor-pointer transition-all duration-200 ${
                  item === selectedValue
                    ? 'text-brand-primary font-semibold text-lg'
                    : 'text-gray-400 text-base hover:text-gray-600'
                }`}
                onClick={() => onValueChange(typeof item === 'string' ? index : item)}
              >
                {item}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-4">
      <div className="text-center">
        <h3 className={`font-medium text-foreground mb-4 ${isMobile ? 'text-base' : 'text-lg'}`}>
          When was {petName} born?
        </h3>
        <p className="text-sm text-muted-foreground mb-6">
          Choose your pet's date of birth. You can always make this private later.
        </p>
        
        {!showPicker ? (
          <Button
            variant="outline"
            onClick={() => setShowPicker(true)}
            className="w-full max-w-sm h-auto py-4 px-6 bg-white border-2 border-gray-200 hover:border-brand-primary hover:bg-gray-50 transition-all duration-200"
          >
            <div className="flex flex-col items-center space-y-2">
              <CalendarIcon className="h-6 w-6 text-brand-primary" />
              <div className="text-center">
                {dateOfBirth ? (
                  <>
                    <div className="font-semibold text-lg text-foreground">
                      {format(dateOfBirth, "MMMM dd, yyyy")}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Click to change date
                    </div>
                  </>
                ) : (
                  <>
                    <div className="font-medium text-base">
                      Select Birthday
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Tap to choose date
                    </div>
                  </>
                )}
              </div>
            </div>
          </Button>
        ) : (
          <div className="bg-white rounded-lg border-2 border-gray-200 p-6 max-w-sm mx-auto">
            <h4 className="font-medium text-center text-foreground mb-6">
              What's {petName}'s birthday?
            </h4>
            
            <div className="flex justify-center space-x-4 mb-6">
              <WheelPicker
                items={months}
                selectedValue={months[selectedMonth]}
                onValueChange={setSelectedMonth}
                label="Month"
              />
              <WheelPicker
                items={days}
                selectedValue={selectedDay}
                onValueChange={setSelectedDay}
                label="Day"
              />
              <WheelPicker
                items={years}
                selectedValue={selectedYear}
                onValueChange={setSelectedYear}
                label="Year"
              />
            </div>
            
            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={() => setShowPicker(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                onClick={handleConfirm}
                className="flex-1 bg-brand-primary hover:bg-brand-dark"
              >
                Confirm
              </Button>
            </div>
          </div>
        )}
        
        {dateOfBirth && !showPicker && (
          <div className={`mt-4 inline-flex items-center justify-center rounded-full bg-brand-primary text-white font-medium ${isMobile ? 'px-4 py-2 text-sm' : 'px-6 py-2 text-base'}`}>
            🎂 Born {format(dateOfBirth, "MMM dd, yyyy")} • {calculateAge(dateOfBirth)}
          </div>
        )}
      </div>
    </div>
  );
};

export default PetDateOfBirthInput;
