
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { <PERSON>, <PERSON> } from "lucide-react";
import { PetSex } from "../types";
import { useIsMobile } from "@/hooks/use-mobile";

interface GenderSelectorProps {
  petName: string;
  petSex: PetSex | null;
  setPetSex: (sex: PetSex) => void;
}

const GenderSelector: React.FC<GenderSelectorProps> = ({
  petName,
  petSex,
  setPetSex
}) => {
  const isMobile = useIsMobile();
  
  return (
    <Card className="bg-white p-6">
      <div className={`space-y-${isMobile ? '4' : '5'}`}>
        <div className="text-center">
          <h3 className={`font-medium text-foreground ${isMobile ? 'text-sm' : 'text-lg'}`}>
            Is {petName} male or female?
          </h3>
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          <Button
            type="button"
            variant={petSex === "male" ? "default" : "outline"}
            className={`${isMobile ? 'py-6 min-h-[64px]' : 'py-6 min-h-[72px]'} flex flex-col items-center justify-center gap-2 transition-all touch-manipulation ${
              petSex === "male" 
                ? "bg-brand-primary border-brand-primary text-white hover:bg-brand-primary/90" 
                : "hover:bg-brand-light/50 hover:border-brand-primary/30"
            }`}
            onClick={() => setPetSex("male")}
          >
            <Dog size={isMobile ? 32 : 28} />
            <span className={`font-medium ${isMobile ? 'text-base' : 'text-sm'}`}>Male</span>
          </Button>
          
          <Button
            type="button"
            variant={petSex === "female" ? "default" : "outline"}
            className={`${isMobile ? 'py-6 min-h-[64px]' : 'py-6 min-h-[72px]'} flex flex-col items-center justify-center gap-2 transition-all touch-manipulation ${
              petSex === "female" 
                ? "bg-brand-primary border-brand-primary text-white hover:bg-brand-primary/90" 
                : "hover:bg-brand-light/50 hover:border-brand-primary/30"
            }`}
            onClick={() => setPetSex("female")}
          >
            <Cat size={isMobile ? 32 : 28} />
            <span className={`font-medium ${isMobile ? 'text-base' : 'text-sm'}`}>Female</span>
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default GenderSelector;
