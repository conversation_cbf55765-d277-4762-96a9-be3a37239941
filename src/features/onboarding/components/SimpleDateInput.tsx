
import React, { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";

interface SimpleDateInputProps {
  petName: string;
  dateOfBirth: Date | null;
  onDateChange: (date: Date | null) => void;
  className?: string;
}

const SimpleDateInput: React.FC<SimpleDateInputProps> = ({
  petName,
  dateOfBirth,
  onDateChange,
  className
}) => {
  const [inputValue, setInputValue] = useState("");
  const [error, setError] = useState("");

  // Format date to mm/dd/yy when dateOfBirth changes
  useEffect(() => {
    if (dateOfBirth) {
      const formatted = format(dateOfBirth, "MM/dd/yy");
      setInputValue(formatted);
      setError("");
    } else {
      setInputValue("");
    }
  }, [dateOfBirth]);

  const calculateAge = (birthDate: Date): string => {
    const today = new Date();
    const ageInMonths = (today.getFullYear() - birthDate.getFullYear()) * 12 + 
                       (today.getMonth() - birthDate.getMonth());
    
    if (ageInMonths < 12) {
      return `${ageInMonths} month${ageInMonths !== 1 ? 's' : ''} old`;
    } else {
      const years = Math.floor(ageInMonths / 12);
      const remainingMonths = ageInMonths % 12;
      if (remainingMonths === 0) {
        return `${years} year${years !== 1 ? 's' : ''} old`;
      } else {
        return `${years}y ${remainingMonths}m old`;
      }
    }
  };

  const formatInput = (value: string): string => {
    // Remove all non-numeric characters
    const numbers = value.replace(/\D/g, '');
    
    // Add slashes automatically
    if (numbers.length >= 2) {
      if (numbers.length >= 4) {
        return `${numbers.slice(0, 2)}/${numbers.slice(2, 4)}/${numbers.slice(4, 6)}`;
      } else {
        return `${numbers.slice(0, 2)}/${numbers.slice(2)}`;
      }
    }
    return numbers;
  };

  const parseDate = (value: string): Date | null => {
    // Expected format: MM/DD/YY
    const parts = value.split('/');
    if (parts.length !== 3) return null;

    const month = parseInt(parts[0], 10);
    const day = parseInt(parts[1], 10);
    let year = parseInt(parts[2], 10);

    // Convert 2-digit year to 4-digit year
    if (year < 50) {
      year += 2000; // 00-49 becomes 2000-2049
    } else if (year < 100) {
      year += 1900; // 50-99 becomes 1950-1999
    }

    // Basic validation
    if (month < 1 || month > 12) return null;
    if (day < 1 || day > 31) return null;
    if (year < 1900 || year > new Date().getFullYear()) return null;

    const date = new Date(year, month - 1, day);
    
    // Check if the date is valid (handles things like Feb 30)
    if (date.getMonth() !== month - 1 || date.getDate() !== day) {
      return null;
    }

    return date;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const rawValue = e.target.value;
    const formattedValue = formatInput(rawValue);
    
    // Limit to 8 characters (MM/DD/YY)
    if (formattedValue.length <= 8) {
      setInputValue(formattedValue);
      setError("");

      // Try to parse date if input is complete
      if (formattedValue.length === 8) {
        const parsedDate = parseDate(formattedValue);
        if (parsedDate) {
          onDateChange(parsedDate);
        } else {
          setError("Please enter a valid date");
          onDateChange(null);
        }
      } else {
        onDateChange(null);
      }
    }
  };

  const handleBlur = () => {
    if (inputValue.length > 0 && inputValue.length < 8) {
      setError("Please enter a complete date (MM/DD/YY)");
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="text-center">
        <h3 className="font-semibold text-lg text-foreground mb-2">
          When was {petName} born?
        </h3>
        <p className="text-sm text-muted-foreground mb-6">
          This helps us provide age-appropriate health recommendations
        </p>
      </div>

      <div className="max-w-sm mx-auto space-y-2">
        <Label htmlFor="pet-birthdate" className="text-sm font-medium">
          Date of Birth
        </Label>
        <div className="relative">
          <Input
            id="pet-birthdate"
            type="text"
            value={inputValue}
            onChange={handleInputChange}
            onBlur={handleBlur}
            placeholder="MM/DD/YY"
            className={`pl-10 text-center text-lg ${error ? 'border-red-500' : ''}`}
            maxLength={8}
          />
          <CalendarIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        </div>
        
        {error && (
          <p className="text-sm text-red-500 text-center">{error}</p>
        )}
        
        <p className="text-xs text-muted-foreground text-center">
          Format: MM/DD/YY (e.g., 06/15/22)
        </p>
      </div>

      {dateOfBirth && !error && (
        <div className="text-center mt-6">
          <div className="inline-flex items-center justify-center rounded-full bg-blue-100 text-blue-800 font-medium px-6 py-3">
            <span className="mr-2">🎂</span>
            <span>
              Born {format(dateOfBirth, "MMMM dd, yyyy")} • {calculateAge(dateOfBirth)}
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default SimpleDateInput;
