
import React from "react";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { cn } from "@/lib/utils";

type MembershipType = "annual" | "monthly";

interface MembershipToggleProps {
  selectedMembership: MembershipType;
  onMembershipChange: (value: MembershipType) => void;
}

const MembershipToggle: React.FC<MembershipToggleProps> = ({ 
  selectedMembership, 
  onMembershipChange 
}) => {
  return (
    <div className="flex justify-center mt-4">
      <ToggleGroup 
        type="single" 
        value={selectedMembership}
        onValueChange={(value) => {
          if (value) onMembershipChange(value as MembershipType);
        }}
        className="bg-white shadow-md rounded-full p-1"
      >
        <ToggleGroupItem 
          value="annual" 
          className={cn(
            "rounded-full px-6 py-2 text-sm font-medium transition-all", 
            selectedMembership === "annual" 
              ? "bg-brand-primary text-white" 
              : "bg-transparent text-gray-600 hover:bg-gray-100"
          )}
        >
          Annual
        </ToggleGroupItem>
        <ToggleGroupItem 
          value="monthly" 
          className={cn(
            "rounded-full px-6 py-2 text-sm font-medium transition-all", 
            selectedMembership === "monthly" 
              ? "bg-brand-primary text-white" 
              : "bg-transparent text-gray-600 hover:bg-gray-100"
          )}
        >
          Monthly
        </ToggleGroupItem>
      </ToggleGroup>
    </div>
  );
};

export default MembershipToggle;
