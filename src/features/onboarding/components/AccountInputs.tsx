
import React from "react";
import { FormField, FormItem, FormControl, FormMessage, FormLabel } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Control } from "react-hook-form";
import { User, Mail, Phone, CheckCircle2 } from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";
import { cn } from "@/lib/utils";

interface AccountInputsProps {
  control: Control<any>;
}

const AccountInputs: React.FC<AccountInputsProps> = ({ control }) => {
  const isMobile = useIsMobile();

  return (
    <div className="space-y-6">
      <div className="space-y-1 mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Personal Information</h3>
        <p className="text-sm text-gray-600">We'll use this to personalize your experience</p>
      </div>

      <FormField
        control={control}
        name="fullName"
        render={({ field, fieldState }) => (
          <FormItem>
            <FormLabel className="text-sm font-medium text-gray-700">Full Name</FormLabel>
            <FormControl>
              <div className="relative group">
                <Input 
                  placeholder="Enter your full name" 
                  className={cn(
                    "pl-11 pr-10 h-12 text-base transition-all duration-200",
                    "border-gray-300 focus:border-brand-primary focus:ring-2 focus:ring-brand-primary/20",
                    "group-hover:border-gray-400",
                    fieldState.error && "border-red-300 focus:border-red-500 focus:ring-red-200",
                    !fieldState.error && field.value && "border-green-300 focus:border-green-500"
                  )}
                  {...field} 
                />
                <User className={cn(
                  "absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 transition-colors duration-200",
                  fieldState.error ? "text-red-400" : 
                  !fieldState.error && field.value ? "text-green-500" : "text-gray-400"
                )} />
                {!fieldState.error && field.value && (
                  <CheckCircle2 className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-green-500" />
                )}
              </div>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={control}
        name="email"
        render={({ field, fieldState }) => (
          <FormItem>
            <FormLabel className="text-sm font-medium text-gray-700">Email Address</FormLabel>
            <FormControl>
              <div className="relative group">
                <Input 
                  type="email" 
                  placeholder="Enter your email address" 
                  className={cn(
                    "pl-11 pr-10 h-12 text-base transition-all duration-200",
                    "border-gray-300 focus:border-brand-primary focus:ring-2 focus:ring-brand-primary/20",
                    "group-hover:border-gray-400",
                    fieldState.error && "border-red-300 focus:border-red-500 focus:ring-red-200",
                    !fieldState.error && field.value && "border-green-300 focus:border-green-500"
                  )}
                  {...field} 
                />
                <Mail className={cn(
                  "absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 transition-colors duration-200",
                  fieldState.error ? "text-red-400" : 
                  !fieldState.error && field.value ? "text-green-500" : "text-gray-400"
                )} />
                {!fieldState.error && field.value && field.value.includes('@') && (
                  <CheckCircle2 className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-green-500" />
                )}
              </div>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={control}
        name="phoneNumber"
        render={({ field, fieldState }) => (
          <FormItem>
            <FormLabel className="text-sm font-medium text-gray-700">Phone Number</FormLabel>
            <FormControl>
              <div className="relative group">
                <Input 
                  type="tel" 
                  placeholder="Enter your phone number" 
                  className={cn(
                    "pl-11 pr-10 h-12 text-base transition-all duration-200",
                    "border-gray-300 focus:border-brand-primary focus:ring-2 focus:ring-brand-primary/20",
                    "group-hover:border-gray-400",
                    fieldState.error && "border-red-300 focus:border-red-500 focus:ring-red-200",
                    !fieldState.error && field.value && "border-green-300 focus:border-green-500"
                  )}
                  {...field} 
                />
                <Phone className={cn(
                  "absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 transition-colors duration-200",
                  fieldState.error ? "text-red-400" : 
                  !fieldState.error && field.value ? "text-green-500" : "text-gray-400"
                )} />
                {!fieldState.error && field.value && field.value.length >= 10 && (
                  <CheckCircle2 className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-green-500" />
                )}
              </div>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};

export default AccountInputs;
