
import React from "react";
import { FormField, FormItem, FormControl, FormMessage, FormLabel } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Control } from "react-hook-form";
import { Home, MapPin, CheckCircle2 } from "lucide-react";
import { cn } from "@/lib/utils";

interface AddressInputProps {
  control: Control<any>;
  addressLine1: string;
  setAddressLine1: (address: string) => void;
  addressLine2: string;
  setAddressLine2: (address: string) => void;
  city: string;
  setCity: (city: string) => void;
  state: string;
  setState: (state: string) => void;
  zipCode: string;
  setZipCode: (zip: string) => void;
}

const US_STATES = [
  { value: "AL", label: "Alabama" },
  { value: "AK", label: "Alaska" },
  { value: "AZ", label: "Arizona" },
  { value: "AR", label: "Arkansas" },
  { value: "CA", label: "California" },
  { value: "CO", label: "Colorado" },
  { value: "CT", label: "Connecticut" },
  { value: "DE", label: "Delaware" },
  { value: "FL", label: "Florida" },
  { value: "GA", label: "Georgia" },
  { value: "HI", label: "Hawaii" },
  { value: "ID", label: "Idaho" },
  { value: "IL", label: "Illinois" },
  { value: "IN", label: "Indiana" },
  { value: "IA", label: "Iowa" },
  { value: "KS", label: "Kansas" },
  { value: "KY", label: "Kentucky" },
  { value: "LA", label: "Louisiana" },
  { value: "ME", label: "Maine" },
  { value: "MD", label: "Maryland" },
  { value: "MA", label: "Massachusetts" },
  { value: "MI", label: "Michigan" },
  { value: "MN", label: "Minnesota" },
  { value: "MS", label: "Mississippi" },
  { value: "MO", label: "Missouri" },
  { value: "MT", label: "Montana" },
  { value: "NE", label: "Nebraska" },
  { value: "NV", label: "Nevada" },
  { value: "NH", label: "New Hampshire" },
  { value: "NJ", label: "New Jersey" },
  { value: "NM", label: "New Mexico" },
  { value: "NY", label: "New York" },
  { value: "NC", label: "North Carolina" },
  { value: "ND", label: "North Dakota" },
  { value: "OH", label: "Ohio" },
  { value: "OK", label: "Oklahoma" },
  { value: "OR", label: "Oregon" },
  { value: "PA", label: "Pennsylvania" },
  { value: "RI", label: "Rhode Island" },
  { value: "SC", label: "South Carolina" },
  { value: "SD", label: "South Dakota" },
  { value: "TN", label: "Tennessee" },
  { value: "TX", label: "Texas" },
  { value: "UT", label: "Utah" },
  { value: "VT", label: "Vermont" },
  { value: "VA", label: "Virginia" },
  { value: "WA", label: "Washington" },
  { value: "WV", label: "West Virginia" },
  { value: "WI", label: "Wisconsin" },
  { value: "WY", label: "Wyoming" }
];

const AddressInput: React.FC<AddressInputProps> = ({
  control,
  addressLine1,
  setAddressLine1,
  addressLine2,
  setAddressLine2,
  city,
  setCity,
  state,
  setState,
  zipCode,
  setZipCode
}) => {
  return (
    <div className="space-y-6">
      <div className="space-y-1 mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Shipping Address</h3>
        <p className="text-sm text-gray-600">Where should we send your test kits and supplements?</p>
      </div>

      <FormField
        control={control}
        name="addressLine1"
        render={({ field, fieldState }) => (
          <FormItem>
            <FormLabel className="text-sm font-medium text-gray-700">Street Address</FormLabel>
            <FormControl>
              <div className="relative group">
                <Input 
                  placeholder="Enter your street address" 
                  className={cn(
                    "pl-11 pr-10 h-12 text-base transition-all duration-200",
                    "border-gray-300 focus:border-brand-primary focus:ring-2 focus:ring-brand-primary/20",
                    "group-hover:border-gray-400",
                    fieldState.error && "border-red-300 focus:border-red-500 focus:ring-red-200",
                    !fieldState.error && field.value && "border-green-300 focus:border-green-500"
                  )}
                  {...field}
                  onChange={(e) => {
                    field.onChange(e);
                    setAddressLine1(e.target.value);
                  }}
                />
                <Home className={cn(
                  "absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 transition-colors duration-200",
                  fieldState.error ? "text-red-400" : 
                  !fieldState.error && field.value ? "text-green-500" : "text-gray-400"
                )} />
                {!fieldState.error && field.value && (
                  <CheckCircle2 className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-green-500" />
                )}
              </div>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={control}
        name="addressLine2"
        render={({ field, fieldState }) => (
          <FormItem>
            <FormLabel className="text-sm font-medium text-gray-500">Apartment, Suite, etc. (Optional)</FormLabel>
            <FormControl>
              <div className="relative group">
                <Input 
                  placeholder="Apartment, suite, unit, building, floor, etc." 
                  className={cn(
                    "pl-11 h-12 text-base transition-all duration-200",
                    "border-gray-300 focus:border-brand-primary focus:ring-2 focus:ring-brand-primary/20",
                    "group-hover:border-gray-400"
                  )}
                  {...field}
                  onChange={(e) => {
                    field.onChange(e);
                    setAddressLine2(e.target.value);
                  }}
                />
                <Home className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              </div>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={control}
          name="city"
          render={({ field, fieldState }) => (
            <FormItem>
              <FormLabel className="text-sm font-medium text-gray-700">City</FormLabel>
              <FormControl>
                <div className="relative group">
                  <Input 
                    placeholder="Enter city" 
                    className={cn(
                      "pl-11 pr-10 h-12 text-base transition-all duration-200",
                      "border-gray-300 focus:border-brand-primary focus:ring-2 focus:ring-brand-primary/20",
                      "group-hover:border-gray-400",
                      fieldState.error && "border-red-300 focus:border-red-500 focus:ring-red-200",
                      !fieldState.error && field.value && "border-green-300 focus:border-green-500"
                    )}
                    {...field}
                    onChange={(e) => {
                      field.onChange(e);
                      setCity(e.target.value);
                    }}
                  />
                  <MapPin className={cn(
                    "absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 transition-colors duration-200",
                    fieldState.error ? "text-red-400" : 
                    !fieldState.error && field.value ? "text-green-500" : "text-gray-400"
                  )} />
                  {!fieldState.error && field.value && (
                    <CheckCircle2 className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-green-500" />
                  )}
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="state"
          render={({ field, fieldState }) => (
            <FormItem>
              <FormLabel className="text-sm font-medium text-gray-700">State</FormLabel>
              <Select 
                onValueChange={(value) => {
                  field.onChange(value);
                  setState(value);
                }} 
                value={field.value}
              >
                <FormControl>
                  <SelectTrigger className={cn(
                    "h-12 text-base transition-all duration-200",
                    "border-gray-300 focus:border-brand-primary focus:ring-2 focus:ring-brand-primary/20",
                    "hover:border-gray-400",
                    fieldState.error && "border-red-300 focus:border-red-500 focus:ring-red-200",
                    !fieldState.error && field.value && "border-green-300 focus:border-green-500"
                  )}>
                    <SelectValue placeholder="Select state" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent className="bg-white border shadow-lg max-h-60">
                  {US_STATES.map((state) => (
                    <SelectItem key={state.value} value={state.value}>
                      {state.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <FormField
        control={control}
        name="zipCode"
        render={({ field, fieldState }) => (
          <FormItem>
            <FormLabel className="text-sm font-medium text-gray-700">ZIP Code</FormLabel>
            <FormControl>
              <div className="relative group">
                <Input 
                  placeholder="Enter ZIP code" 
                  className={cn(
                    "pl-11 pr-10 h-12 text-base transition-all duration-200 max-w-xs",
                    "border-gray-300 focus:border-brand-primary focus:ring-2 focus:ring-brand-primary/20",
                    "group-hover:border-gray-400",
                    fieldState.error && "border-red-300 focus:border-red-500 focus:ring-red-200",
                    !fieldState.error && field.value && "border-green-300 focus:border-green-500"
                  )}
                  {...field}
                  onChange={(e) => {
                    field.onChange(e);
                    setZipCode(e.target.value);
                  }}
                />
                <MapPin className={cn(
                  "absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 transition-colors duration-200",
                  fieldState.error ? "text-red-400" : 
                  !fieldState.error && field.value ? "text-green-500" : "text-gray-400"
                )} />
                {!fieldState.error && field.value && field.value.length >= 5 && (
                  <CheckCircle2 className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-green-500" />
                )}
              </div>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};

export default AddressInput;
