
import React from "react";
import { Form } from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import AccountInputs from "./AccountInputs";
import PasswordInput from "./PasswordInput";
import AddressInput from "./AddressInput";
import MarketingConsent from "./MarketingConsent";
import FormNavigation from "./FormNavigation";
import FormErrorSummary from "./FormErrorSummary";
import { accountFormSchema, AccountFormValues } from "./AccountFormSchema";

interface AccountFormProps {
  ownerName: string;
  ownerEmail: string;
  phoneNumber: string;
  addressLine1: string;
  addressLine2: string;
  city: string;
  state: string;
  zipCode: string;
  setOwnerName: (name: string) => void;
  setOwnerEmail: (email: string) => void;
  setPhoneNumber: (phone: string) => void;
  setAddressLine1: (address: string) => void;
  setAddressLine2: (address: string) => void;
  setCity: (city: string) => void;
  setState: (state: string) => void;
  setZipCode: (zip: string) => void;
  onComplete: () => void;
  onPrevious: () => void;
  isSubmitting?: boolean;
}

const AccountForm: React.FC<AccountFormProps> = ({
  ownerName,
  ownerEmail,
  phoneNumber,
  addressLine1,
  addressLine2,
  city,
  state,
  zipCode,
  setOwnerName,
  setOwnerEmail,
  setPhoneNumber,
  setAddressLine1,
  setAddressLine2,
  setCity,
  setState,
  setZipCode,
  onComplete,
  onPrevious,
  isSubmitting = false
}) => {
  const form = useForm<AccountFormValues>({
    resolver: zodResolver(accountFormSchema),
    defaultValues: {
      fullName: ownerName,
      email: ownerEmail,
      password: "",
      phoneNumber: phoneNumber,
      addressLine1: addressLine1,
      addressLine2: addressLine2,
      city: city,
      state: state,
      zipCode: zipCode,
      marketingConsent: "yes"
    },
    mode: "onChange"
  });

  const { formState: { isValid, errors } } = form;
  
  const handleSubmit = (data: AccountFormValues) => {
    console.log("Form submitted with data:", data);
    
    // Update all state values
    setOwnerName(data.fullName);
    setOwnerEmail(data.email);
    setPhoneNumber(data.phoneNumber);
    setAddressLine1(data.addressLine1);
    setAddressLine2(data.addressLine2 || "");
    setCity(data.city);
    setState(data.state);
    setZipCode(data.zipCode);
    
    // Call onComplete to proceed to next step
    onComplete();
  };

  return (
    <div className="space-y-8">
      <FormErrorSummary errors={errors} />
      
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-8">
          <AccountInputs control={form.control} />
          
          <PasswordInput control={form.control} />
          
          <AddressInput
            control={form.control}
            addressLine1={addressLine1}
            setAddressLine1={setAddressLine1}
            addressLine2={addressLine2}
            setAddressLine2={setAddressLine2}
            city={city}
            setCity={setCity}
            state={state}
            setState={setState}
            zipCode={zipCode}
            setZipCode={setZipCode}
          />
          
          <MarketingConsent control={form.control} />
          
          <FormNavigation 
            onPrevious={onPrevious}
            submitLabel="Create Account"
            isValid={isValid}
            isLastStep={true}
            isSubmitting={isSubmitting}
          />
        </form>
      </Form>
    </div>
  );
};

export default AccountForm;
