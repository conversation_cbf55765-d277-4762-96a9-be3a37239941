
import React from "react";
import EnhancedPetDateOfBirthInput from "./EnhancedPetDateOfBirthInput";
import PetWeightInput from "./PetWeightInput";
import { PetType } from "../types";

interface AgeWeightSectionProps {
  petName: string;
  petType: PetType;
  petAge: string;
  petAgeInMonths: number;
  petWeight: string;
  petDateOfBirth: Date | null;
  handleDateOfBirthChange: (date: Date | null) => void;
  setPetWeight: (weight: string) => void;
}

const AgeWeightSection: React.FC<AgeWeightSectionProps> = ({
  petName,
  petType,
  petWeight,
  petDateOfBirth,
  handleDateOfBirthChange,
  setPetWeight
}) => {
  return (
    <div className="space-y-8">
      {/* Birthday Section */}
      <div className="space-y-4">
        <EnhancedPetDateOfBirthInput
          petName={petName}
          dateOfBirth={petDateOfBirth}
          onDateChange={handleDateOfBirthChange}
        />
      </div>
      
      {/* Weight Section */}
      <div className="space-y-4">
        <PetWeightInput
          petName={petName}
          petType={petType}
          petWeight={petWeight}
          setPetWeight={setPetWeight}
        />
      </div>
    </div>
  );
};

export default AgeWeightSection;
