
import React from "react";
import { Slider } from "@/components/ui/slider";
import { useIsMobile } from "@/hooks/use-mobile";

interface PetAgeInputProps {
  petName: string;
  petAge: string;
  petAgeInMonths: number;
  handleAgeSliderChange: (value: number[]) => void;
}

const PetAgeInput: React.FC<PetAgeInputProps> = ({
  petName,
  petAge,
  petAgeInMonths,
  handleAgeSliderChange
}) => {
  const isMobile = useIsMobile();
  
  const MIN_AGE_MONTHS = 6;
  const MAX_AGE_MONTHS = 240; // 20 years * 12 months
  
  const getStep = () => {
    return petAgeInMonths < 12 ? 1 : 6;
  };
  
  return (
    <div className="space-y-4">
      <div className="text-center">
        <h3 className={`font-medium text-foreground mb-3 ${isMobile ? 'text-base' : 'text-lg'}`}>
          How old is {petName}?
        </h3>
        <div className={`inline-flex items-center justify-center rounded-full bg-brand-primary text-white font-semibold ${isMobile ? 'px-5 py-2 text-lg' : 'px-6 py-3 text-xl'}`}>
          {petAge}
        </div>
      </div>
      
      <div className={`px-2 ${isMobile ? 'py-4' : 'py-3'}`}>
        <Slider
          value={[petAgeInMonths]}
          min={MIN_AGE_MONTHS}
          max={MAX_AGE_MONTHS}
          step={getStep()}
          onValueChange={handleAgeSliderChange}
          className="w-full"
        />
      </div>
      
      <div className="flex justify-between text-xs text-muted-foreground">
        <span>6mo</span>
        <span>5yr</span>
        <span>10yr</span>
        <span>15yr</span>
        <span>20yr</span>
      </div>
    </div>
  );
};

export default PetAgeInput;
