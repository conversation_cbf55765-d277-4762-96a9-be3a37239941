
import React from "react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { FieldErrors } from "react-hook-form";

interface FormErrorSummaryProps {
  errors: FieldErrors;
}

const FormErrorSummary: React.FC<FormErrorSummaryProps> = ({ errors }) => {
  const errorEntries = Object.entries(errors);
  
  if (errorEntries.length === 0) return null;

  return (
    <Alert variant="destructive" className="mb-4">
      <AlertCircle className="h-4 w-4" />
      <AlertDescription>
        <div className="space-y-1">
          <p className="font-medium">Please fix the following errors:</p>
          <ul className="list-disc list-inside space-y-1 text-sm">
            {errorEntries.map(([field, error]) => (
              <li key={field}>
                {typeof error?.message === 'string' ? error.message : `${field} is required`}
              </li>
            ))}
          </ul>
        </div>
      </AlertDescription>
    </Alert>
  );
};

export default FormErrorSummary;
