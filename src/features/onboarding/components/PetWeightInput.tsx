
import React from "react";
import { Input } from "@/components/ui/input";
import { PetType, PET_WEIGHT_RANGES } from "../types";
import { useIsMobile } from "@/hooks/use-mobile";

interface PetWeightInputProps {
  petName: string;
  petType: PetType | null;
  petWeight: string;
  setPetWeight: (weight: string) => void;
}

const PetWeightInput: React.FC<PetWeightInputProps> = ({
  petName,
  petType,
  petWeight,
  setPetWeight
}) => {
  const isMobile = useIsMobile();

  const handleWeightChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (/^\d{0,3}$/.test(value)) {
      setPetWeight(value);
    }
  };

  return (
    <div className="space-y-4">
      <div className="text-center">
        <h3 className={`font-medium text-foreground ${isMobile ? 'text-base' : 'text-lg'}`}>
          How much does {petName} weigh?
        </h3>
      </div>
      
      <div className="flex items-center justify-center gap-3">
        <Input 
          id="petWeight" 
          type="text"
          inputMode="numeric"
          placeholder="0" 
          value={petWeight} 
          onChange={handleWeightChange}
          className={`text-center font-semibold border-2 focus:border-brand-primary ${isMobile ? 'w-20 h-10 text-lg' : 'w-16 h-9 text-base'}`}
          maxLength={3}
        />
        <span className="font-medium text-muted-foreground">lbs</span>
      </div>
    </div>
  );
};

export default PetWeightInput;
