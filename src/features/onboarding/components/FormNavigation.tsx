
import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>, ArrowLeft, Loader2, Shield, CheckCircle2 } from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";

interface FormNavigationProps {
  onPrevious: () => void;
  onNext?: () => void;
  submitLabel?: string;
  isValid?: boolean;
  isLastStep?: boolean;
  isSubmitting?: boolean;
}

const FormNavigation: React.FC<FormNavigationProps> = ({
  onPrevious,
  onNext,
  submitLabel = "Complete",
  isValid = true,
  isLastStep = false,
  isSubmitting = false
}) => {
  const isMobile = useIsMobile();
  
  const ActionButton = () => {
    const button = onNext ? (
      // Regular button when not in a form
      <Button 
        type="button"
        onClick={onNext}
        disabled={!isValid || isSubmitting}
        className={cn(
          "relative overflow-hidden transition-all duration-200",
          "bg-gradient-to-r from-brand-primary to-brand-primary/90 hover:from-brand-primary/90 hover:to-brand-primary",
          "text-white font-semibold shadow-lg hover:shadow-xl",
          "transform hover:scale-[1.02] active:scale-[0.98]",
          isMobile ? "w-full h-14 text-lg rounded-xl" : "px-8 h-12 rounded-lg",
          !isValid && "opacity-50 cursor-not-allowed",
          isSubmitting && "cursor-wait"
        )}
      >
        <div className="flex items-center justify-center gap-2">
          {isSubmitting ? (
            <>
              <Loader2 className={cn("animate-spin", isMobile ? 'h-5 w-5' : 'h-4 w-4')} />
              Processing...
            </>
          ) : (
            <>
              {isLastStep && <Shield className={cn(isMobile ? 'h-5 w-5' : 'h-4 w-4')} />}
              {submitLabel}
              <ArrowRight className={cn(isMobile ? 'h-5 w-5' : 'h-4 w-4')} />
            </>
          )}
        </div>
        {/* Subtle animation overlay */}
        {!isSubmitting && isValid && (
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full hover:translate-x-full transition-transform duration-1000" />
        )}
      </Button>
    ) : (
      // Submit button when in a form
      <Button 
        type="submit"
        disabled={!isValid || isSubmitting}
        className={cn(
          "relative overflow-hidden transition-all duration-200",
          "bg-gradient-to-r from-brand-primary to-brand-primary/90 hover:from-brand-primary/90 hover:to-brand-primary",
          "text-white font-semibold shadow-lg hover:shadow-xl",
          "transform hover:scale-[1.02] active:scale-[0.98]",
          isMobile ? "w-full h-14 text-lg rounded-xl" : "px-8 h-12 rounded-lg",
          !isValid && "opacity-50 cursor-not-allowed",
          isSubmitting && "cursor-wait"
        )}
      >
        <div className="flex items-center justify-center gap-2">
          {isSubmitting ? (
            <>
              <Loader2 className={cn("animate-spin", isMobile ? 'h-5 w-5' : 'h-4 w-4')} />
              Creating Account...
            </>
          ) : (
            <>
              {isLastStep ? (
                <>
                  <CheckCircle2 className={cn(isMobile ? 'h-5 w-5' : 'h-4 w-4')} />
                  {submitLabel}
                </>
              ) : (
                <>
                  {submitLabel}
                  <ArrowRight className={cn(isMobile ? 'h-5 w-5' : 'h-4 w-4')} />
                </>
              )}
            </>
          )}
        </div>
        {/* Subtle animation overlay */}
        {!isSubmitting && isValid && (
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full hover:translate-x-full transition-transform duration-1000" />
        )}
      </Button>
    );

    if (!isValid && isLastStep) {
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              {button}
            </TooltipTrigger>
            <TooltipContent className="bg-gray-900 text-white border-gray-700">
              <p>Please complete all required fields correctly</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    }
    
    return button;
  };
  
  return (
    <div className="pt-6 border-t border-gray-100">
      {/* Trust signals */}
      <div className="mb-6 text-center">
        <div className="flex items-center justify-center gap-4 text-xs text-gray-500">
          <div className="flex items-center gap-1">
            <Shield className="h-4 w-4 text-green-500" />
            <span>SSL Encrypted</span>
          </div>
          <div className="w-1 h-1 bg-gray-300 rounded-full" />
          <div className="flex items-center gap-1">
            <CheckCircle2 className="h-4 w-4 text-green-500" />
            <span>HIPAA Compliant</span>
          </div>
        </div>
      </div>

      <div className={cn(
        "flex gap-4 w-full",
        isMobile ? "flex-col" : "justify-between"
      )}>
        <Button 
          type="button" 
          variant="outline" 
          onClick={onPrevious} 
          className={cn(
            "transition-all duration-200 font-medium",
            "border-gray-300 hover:border-gray-400 hover:bg-gray-50",
            "transform hover:scale-[1.02] active:scale-[0.98]",
            isMobile ? "w-full h-14 text-lg rounded-xl" : "px-8 h-12 rounded-lg"
          )}
          disabled={isSubmitting}
        >
          <ArrowLeft className={cn(isMobile ? 'h-5 w-5' : 'h-4 w-4', "mr-2")} />
          Back
        </Button>
        
        <ActionButton />
      </div>
    </div>
  );
};

export default FormNavigation;
