
import React from "react";

interface FeaturesListProps {
  features: string[];
}

const FeaturesList: React.FC<FeaturesListProps> = ({ features }) => {
  return (
    <div className="space-y-3">
      {features.map((feature, idx) => (
        <div key={idx} className="flex items-start">
          <div className="rounded-full flex items-center justify-center w-5 h-5 min-w-5 min-h-5 text-white bg-brand-primary mt-0.5">
            <svg className="w-3 h-3" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M10 3L4.5 8.5L2 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
            </svg>
          </div>
          <span className="ml-3 text-sm">{feature}</span>
        </div>
      ))}
    </div>
  );
};

export default FeaturesList;
