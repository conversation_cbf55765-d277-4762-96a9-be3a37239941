
import React from "react";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { FormField, FormItem, FormControl, FormLabel } from "@/components/ui/form";
import { Control } from "react-hook-form";
import { useIsMobile } from "@/hooks/use-mobile";
import { Mail, Heart, Shield } from "lucide-react";
import { cn } from "@/lib/utils";

interface MarketingConsentProps {
  control: Control<any>;
}

const MarketingConsent: React.FC<MarketingConsentProps> = ({ control }) => {
  const isMobile = useIsMobile();
  
  return (
    <div className="space-y-6">
      <div className="space-y-1 mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Stay Connected</h3>
        <p className="text-sm text-gray-600 flex items-center gap-1">
          <Heart className="h-4 w-4 text-red-500" />
          Get personalized tips to keep your pet healthy and happy
        </p>
      </div>

      <FormField
        control={control}
        name="marketingConsent"
        render={({ field }) => (
          <FormItem>
            <FormControl>
              <div className="space-y-4">
                <div className="bg-gradient-to-r from-brand-primary/5 to-brand-secondary/5 p-4 rounded-lg border border-brand-primary/10">
                  <div className="flex items-start gap-3 mb-3">
                    <Mail className="h-5 w-5 text-brand-primary mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="text-sm font-medium text-gray-900 mb-1">
                        Receive personalized pet health insights
                      </p>
                      <p className="text-xs text-gray-600">
                        Get weekly tips, health alerts, and supplement recommendations tailored to your pet
                      </p>
                    </div>
                  </div>
                  
                  <RadioGroup 
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    className="flex flex-col gap-3"
                  >
                    <div className="flex items-center space-x-3 p-3 rounded-lg border border-gray-200 hover:border-brand-primary/30 transition-colors cursor-pointer">
                      <RadioGroupItem 
                        value="yes" 
                        id="yes" 
                        className={cn(
                          "border-2 data-[state=checked]:bg-brand-primary data-[state=checked]:border-brand-primary",
                          isMobile ? "h-5 w-5" : "h-4 w-4"
                        )}
                      />
                      <FormLabel htmlFor="yes" className="cursor-pointer flex-1">
                        <span className={cn("font-medium", isMobile ? "text-base" : "text-sm")}>
                          Yes, send me personalized health tips
                        </span>
                        <p className="text-xs text-gray-500 mt-1">
                          Recommended for the best pet care experience
                        </p>
                      </FormLabel>
                    </div>
                    
                    <div className="flex items-center space-x-3 p-3 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors cursor-pointer">
                      <RadioGroupItem 
                        value="no" 
                        id="no" 
                        className={cn(
                          "border-2",
                          isMobile ? "h-5 w-5" : "h-4 w-4"
                        )}
                      />
                      <FormLabel htmlFor="no" className="cursor-pointer">
                        <span className={cn(isMobile ? "text-base" : "text-sm")}>
                          No, I'll manage my pet's health independently
                        </span>
                      </FormLabel>
                    </div>
                  </RadioGroup>
                </div>
                
                <div className="flex items-center gap-2 text-xs text-gray-500">
                  <Shield className="h-4 w-4 text-brand-primary" />
                  <span>
                    We respect your privacy. Unsubscribe anytime. 
                    <button type="button" className="text-brand-primary hover:underline ml-1">
                      Privacy Policy
                    </button>
                  </span>
                </div>
              </div>
            </FormControl>
          </FormItem>
        )}
      />
    </div>
  );
};

export default MarketingConsent;
