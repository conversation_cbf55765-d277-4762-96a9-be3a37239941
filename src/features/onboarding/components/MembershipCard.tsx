
import React from "react";
import { Badge } from "@/components/ui/badge";
import { BadgeCheck } from "lucide-react";
import { formatCurrency } from "@/lib/utils";
import FeaturesList from "./FeaturesList";

type MembershipType = "annual" | "monthly";

interface MembershipCardProps {
  selectedMembership: MembershipType;
  annualPrice: number;
  monthlyPrice: number;
  savingsPercentage: number;
  features: string[];
}

const MembershipCard: React.FC<MembershipCardProps> = ({
  selectedMembership,
  annualPrice,
  monthlyPrice,
  savingsPercentage,
  features
}) => {
  // Filter out specific features for separate display
  const testKitFeature = features.find(feature => feature.includes("Microbiome Test Kit"));
  const assessmentCallFeature = features.find(feature => feature.includes("Initial Health Assessment"));
  const otherFeatures = features.filter(
    feature => !feature.includes("Microbiome Test Kit") && !feature.includes("Initial Health Assessment")
  );

  return (
    <div 
      className="relative rounded-xl border overflow-hidden transition-all duration-300 shadow-lg border-brand-primary max-w-lg w-full transform hover:scale-[1.01]"
    >
      {/* Best Value Banner - only show for annual */}
      {selectedMembership === "annual" && (
        <div className="bg-brand-accent text-brand-primary font-bold text-xs text-center py-1">
          <BadgeCheck className="inline-block h-4 w-4 mr-1" />
          BEST VALUE PLAN
        </div>
      )}
      
      {/* Header */}
      <div className="p-6 bg-gradient-to-b from-brand-accent/20 to-transparent border-b">
        <h3 className="text-2xl font-serif text-brand-primary">
          {selectedMembership === "annual" ? "Annual" : "Monthly"} Membership
        </h3>
        <div className="flex flex-col mt-2">
          <div className="flex items-baseline">
            <span className="text-3xl font-bold">
              {selectedMembership === "annual" 
                ? formatCurrency(annualPrice) 
                : formatCurrency(monthlyPrice)
              }
            </span>
            <span className="text-muted-foreground ml-2 text-sm">
              {selectedMembership === "annual" ? "/year" : "/month"}
            </span>
          </div>
          {selectedMembership === "annual" ? (
            <div className="mt-1 text-brand-primary font-medium">
              <span className="text-sm">{formatCurrency(annualPrice / 12)}/mo</span> 
              <span className="ml-2 inline-block bg-brand-primary/10 text-brand-primary text-xs px-2 py-0.5 rounded-full">
                Save {savingsPercentage}%
              </span>
            </div>
          ) : (
            <div className="mt-1 text-muted-foreground">
              <span className="text-sm">{formatCurrency(monthlyPrice * 12)}/year</span>
            </div>
          )}
        </div>
        <p className="text-xs text-muted-foreground mt-2">
          Billed {selectedMembership === "annual" ? "annually in one payment" : "monthly"}
        </p>
      </div>
      
      {/* Features Section */}
      <div className="p-6">
        <div className="text-sm font-medium mb-3 text-brand-primary">Expert-guided care for your pet's health</div>
        
        {/* Test Kit Feature - moved to top */}
        {testKitFeature && (
          <div className="mb-3 bg-white p-3 rounded-lg border border-brand-primary/20 shadow-sm">
            <div className="flex items-center">
              <span className="font-medium text-sm">{testKitFeature}</span>
              <Badge variant="accent" className="ml-2 text-[10px] py-0 h-4">INCLUDED</Badge>
            </div>
            <span className="text-xs font-bold text-brand-primary">$125 value</span>
          </div>
        )}
        
        {/* Assessment Call Feature - moved below test kit */}
        {assessmentCallFeature && (
          <div className="mb-4 bg-white p-3 rounded-lg border border-brand-primary/20 shadow-sm">
            <div className="flex items-center">
              <span className="font-medium text-sm">{assessmentCallFeature}</span>
              <Badge variant="accent" className="ml-2 text-[10px] py-0 h-4">INCLUDED</Badge>
            </div>
            <span className="text-xs font-bold text-brand-primary">$90 value</span>
          </div>
        )}
        
        <FeaturesList features={otherFeatures} />
      </div>
    </div>
  );
};

export default MembershipCard;
