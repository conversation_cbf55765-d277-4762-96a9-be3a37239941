
import React from "react";
import { 
  <PERSON>,
  CardContent,
  CardDescription,
  CardH<PERSON>er,
  CardTitle 
} from "@/components/ui/card";
import { useIsMobile } from "@/hooks/use-mobile";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import AccountForm from "./components/AccountForm";
import FormProgressIndicator from "./components/FormProgressIndicator";
import { accountFormSchema, AccountFormValues } from "./components/AccountFormSchema";
import { Shield, CheckCircle2, Clock } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface Step4AccountProps {
  ownerName: string;
  ownerEmail: string;
  phoneNumber: string;
  addressLine1: string;
  addressLine2: string;
  city: string;
  state: string;
  zipCode: string;
  setOwnerName: (name: string) => void;
  setOwnerEmail: (email: string) => void;
  setPhoneNumber: (phone: string) => void;
  setAddressLine1: (address: string) => void;
  setAddressLine2: (address: string) => void;
  setCity: (city: string) => void;
  setState: (state: string) => void;
  setZipCode: (zip: string) => void;
  onComplete: () => void;
  onPrevious: () => void;
  isSubmitting?: boolean;
}

const Step4Account: React.FC<Step4AccountProps> = ({
  ownerName,
  ownerEmail,
  phoneNumber,
  addressLine1,
  addressLine2,
  city,
  state,
  zipCode,
  setOwnerName,
  setOwnerEmail,
  setPhoneNumber,
  setAddressLine1,
  setAddressLine2,
  setCity,
  setState,
  setZipCode,
  onComplete,
  onPrevious,
  isSubmitting = false
}) => {
  const isMobile = useIsMobile();
  
  const form = useForm<AccountFormValues>({
    resolver: zodResolver(accountFormSchema),
    defaultValues: {
      fullName: ownerName,
      email: ownerEmail,
      password: "",
      phoneNumber: phoneNumber,
      addressLine1: addressLine1,
      addressLine2: addressLine2,
      city: city,
      state: state,
      zipCode: zipCode,
      marketingConsent: "yes"
    },
    mode: "onChange"
  });
  
  // Count completed required fields
  const requiredFields = ['fullName', 'email', 'password', 'phoneNumber', 'addressLine1', 'city', 'state', 'zipCode', 'marketingConsent'];
  const completedFields = requiredFields.filter(field => {
    const value = form.getValues(field as keyof AccountFormValues);
    return value && value.toString().trim() !== '';
  });
  
  const completionPercentage = Math.round((completedFields.length / requiredFields.length) * 100);

  return (
    <Card className="border-0 shadow-2xl bg-white/95 backdrop-blur-sm">
      <CardHeader className="pb-6 text-center relative">
        {/* Step indicator */}
        <div className="absolute top-4 left-4">
          <Badge variant="secondary" className="text-xs font-medium">
            Step 4 of 5
          </Badge>
        </div>
        
        {/* Estimated time */}
        <div className="absolute top-4 right-4">
          <div className="flex items-center gap-1 text-xs text-gray-500">
            <Clock className="h-4 w-4" />
            <span>2-3 min</span>
          </div>
        </div>

        <div className="pt-8">
          <div className="w-16 h-16 bg-gradient-to-br from-brand-primary to-brand-secondary rounded-2xl flex items-center justify-center mx-auto mb-4">
            <Shield className="h-8 w-8 text-white" />
          </div>
          
          <CardTitle className={cn(
            "text-center font-serif mb-2",
            isMobile ? 'text-2xl' : 'text-3xl'
          )}>
            Create Your Account
          </CardTitle>
          
          <CardDescription className="text-center text-base text-gray-600 max-w-md mx-auto">
            Almost there! Complete your secure account to access personalized health insights for your pet.
          </CardDescription>
        </div>
        
        <FormProgressIndicator completionPercentage={completionPercentage} />

        {/* Trust signals */}
        <div className="flex items-center justify-center gap-6 mt-4 text-xs text-gray-500">
          <div className="flex items-center gap-1">
            <Shield className="h-4 w-4 text-brand-primary" />
            <span>Bank-level Security</span>
          </div>
          <div className="flex items-center gap-1">
            <CheckCircle2 className="h-4 w-4 text-green-500" />
            <span>HIPAA Compliant</span>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="px-6 pb-8">
        <AccountForm
          ownerName={ownerName}
          ownerEmail={ownerEmail}
          phoneNumber={phoneNumber}
          addressLine1={addressLine1}
          addressLine2={addressLine2}
          city={city}
          state={state}
          zipCode={zipCode}
          setOwnerName={setOwnerName}
          setOwnerEmail={setOwnerEmail}
          setPhoneNumber={setPhoneNumber}
          setAddressLine1={setAddressLine1}
          setAddressLine2={setAddressLine2}
          setCity={setCity}
          setState={setState}
          setZipCode={setZipCode}
          onComplete={onComplete}
          onPrevious={onPrevious}
          isSubmitting={isSubmitting}
        />
      </CardContent>
    </Card>
  );
};

export default Step4Account;
