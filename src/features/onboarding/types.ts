
export type PetType = "dog" | "cat";
export type PetSex = "male" | "female";
export type Step = 1 | 2 | 3 | 4 | 5;

export interface HealthCategory {
  category: string;
  symptoms: string[];
}

export interface OnboardingData {
  petType: PetType | null;
  petName: string;
  petBreed: string;
  petAge: string;
  petAgeInMonths: number;
  petDateOfBirth: Date | null;
  petSex: PetSex | null;
  petWeight: string;
  healthIssues: string[];
  ownerName: string;
  ownerEmail: string;
  phoneNumber: string;
  addressLine1: string;
  addressLine2: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
}

export const HEALTH_CATEGORIES: HealthCategory[] = [
  {
    category: "Digestive",
    symptoms: [
      "Digestive issues",
      "Diarrhea",
      "Vomiting",
      "Constipation",
      "Loose stool",
      "Gas or bloating",
      "Bad breath",
      "Poor appetite",
      "Overeating or always hungry",
      "Scooting or licking rear"
    ]
  },
  {
    category: "Skin & Coat",
    symptoms: [
      "Itchy skin",
      "Excessive shedding",
      "Dull coat",
      "Hot spots or redness",
      "Frequent licking or chewing paws",
      "Skin allergies or sensitivities"
    ]
  },
  {
    category: "Behavior",
    symptoms: [
      "Anxiety",
      "Restlessness or pacing",
      "Destructive behavior when left alone",
      "Hiding or withdrawal",
      "Behavioral changes",
      "Trouble sleeping or waking at night"
    ]
  },
  {
    category: "Mobility & Joints",
    symptoms: [
      "Low energy",
      "Joint pain or stiffness",
      "Limping or difficulty moving",
      "Senior health concerns"
    ]
  },
  {
    category: "Weight & Metabolism",
    symptoms: [
      "Weight gain",
      "Weight loss",
      "Excessive thirst or urination",
      "Thyroid or metabolic concerns"
    ]
  },
  {
    category: "Eyes, Ears & Respiratory",
    symptoms: [
      "Eye discharge or redness",
      "Ear infections or head shaking",
      "Difficulty breathing or coughing"
    ]
  },
  {
    category: "Other",
    symptoms: [
      "Other (please describe)"
    ]
  }
];

// Pet weight ranges by type and size category
export const PET_WEIGHT_RANGES = {
  dog: {
    toy: { min: 2, max: 12, label: "Toy breeds (2-12 lbs)" },
    small: { min: 12, max: 25, label: "Small breeds (12-25 lbs)" },
    medium: { min: 25, max: 60, label: "Medium breeds (25-60 lbs)" },
    large: { min: 60, max: 90, label: "Large breeds (60-90 lbs)" },
    giant: { min: 90, max: 200, label: "Giant breeds (90+ lbs)" }
  },
  cat: {
    small: { min: 5, max: 10, label: "Small cats (5-10 lbs)" },
    medium: { min: 10, max: 15, label: "Medium cats (10-15 lbs)" },
    large: { min: 15, max: 25, label: "Large cats (15+ lbs)" }
  }
};
