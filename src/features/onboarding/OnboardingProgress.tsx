
import React from "react";
import { Progress } from "@/components/ui/progress";
import { Step } from "./types";
import { Check } from "lucide-react";
import { cn } from "@/lib/utils";

interface OnboardingProgressProps {
  currentStep: Step;
}

const OnboardingProgress: React.FC<OnboardingProgressProps> = ({ currentStep }) => {
  const progress = Math.min((currentStep - 1) * 25, 100);
  const steps = [
    { label: "Pet Details", step: 1 },
    { label: "Health Profile", step: 2 },
    { label: "Membership", step: 3 },
    { label: "Account", step: 4 },
    { label: "Success", step: 5 },
  ];
  
  return (
    <div className="w-full mb-6">
      <Progress value={progress} className="h-2 mb-3" />
      <div className="flex justify-between text-sm">
        {steps.map((step) => (
          <div key={step.step} className="flex flex-col items-center">
            <div 
              className={cn(
                "w-6 h-6 rounded-full flex items-center justify-center text-xs mb-1",
                currentStep === step.step 
                  ? "bg-primary text-white" 
                  : currentStep > step.step 
                    ? "bg-primary/80 text-white" 
                    : "bg-secondary text-muted-foreground"
              )}
            >
              {currentStep > step.step ? <Check size={14} /> : step.step}
            </div>
            <span 
              className={cn(
                "text-xs hidden sm:block",
                currentStep === step.step 
                  ? "text-primary font-medium" 
                  : "text-muted-foreground"
              )}
            >
              {step.label}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default OnboardingProgress;
