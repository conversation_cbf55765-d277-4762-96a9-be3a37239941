
import React from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle 
} from "@/components/ui/card";
import { Dog, Cat, ArrowRight } from "lucide-react";
import { PetType } from "./types";
import { useIsMobile } from "@/hooks/use-mobile";
import BreedSearch from "./BreedSearch";

interface Step1PetTypeProps {
  petType: PetType | null;
  petName: string;
  petBreed: string;
  setPetType: (type: PetType) => void;
  setPetName: (name: string) => void;
  setPetBreed: (breed: string) => void;
  onNext: () => void;
}

const Step1PetType: React.FC<Step1PetTypeProps> = ({
  petType,
  petName,
  petBreed,
  setPetType,
  setPetName,
  setPetBreed,
  onNext
}) => {
  const isMobile = useIsMobile();
  
  return (
    <Card className="border-brand-primary/20 shadow-lg">
      <CardHeader>
        <CardTitle className={`text-center ${isMobile ? 'text-xl' : 'text-2xl'}`}>What type of pet do you have?</CardTitle>
        <CardDescription className="text-center">
          We'll tailor our recommendations based on your pet type.
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-6">
        <div className="grid grid-cols-2 gap-4 sm:gap-6">
          <Button
            variant={petType === "dog" ? "default" : "outline"}
            className={`h-24 md:h-32 flex flex-col gap-2 ${
              petType === "dog" ? "border-2 border-brand-primary" : ""
            }`}
            onClick={() => setPetType("dog")}
          >
            <Dog size={isMobile ? 24 : 32} />
            <span className="font-serif text-lg md:text-xl">Dog</span>
          </Button>
          <Button
            variant={petType === "cat" ? "default" : "outline"}
            className={`h-24 md:h-32 flex flex-col gap-2 ${
              petType === "cat" ? "border-2 border-brand-primary" : ""
            }`}
            onClick={() => setPetType("cat")}
          >
            <Cat size={isMobile ? 24 : 32} />
            <span className="font-serif text-lg md:text-xl">Cat</span>
          </Button>
        </div>

        <div className="mt-8 space-y-4">
          <div className="space-y-2">
            <Input
              id="petName"
              placeholder="Your pet's name"
              value={petName}
              onChange={(e) => setPetName(e.target.value)}
              className={isMobile ? 'h-14 text-base rounded-xl' : ''}
            />
          </div>
          <div className="space-y-2">
            <BreedSearch
              petType={petType}
              value={petBreed}
              onChange={setPetBreed}
              placeholder={petType === "dog" ? "Your dog's breed (e.g., Golden Retriever)" : "Your cat's breed (e.g., Siamese)"}
            />
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-end">
        <Button 
          onClick={onNext}
          disabled={!petType || !petName}
          className="bg-brand-primary hover:bg-brand-dark"
        >
          Next
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </CardFooter>
    </Card>
  );
};

export default Step1PetType;
