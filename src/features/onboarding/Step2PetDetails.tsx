
import React from "react";
import { Button } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import AgeWeightSection from "./components/AgeWeightSection";
import GenderSelector from "./components/GenderSelector";
import HealthIssuesSection from "./components/HealthIssuesSection";

interface Step2PetDetailsProps {
  petName: string;
  petType: string;
  petAge: number;
  petAgeInMonths: number;
  petDateOfBirth: Date | null;
  petSex: string;
  petWeight: number;
  healthIssues: string[];
  handleAgeSliderChange: (value: number[]) => void;
  handleDateOfBirthChange: (date: Date | null) => void;
  setPetSex: (sex: string) => void;
  setPetWeight: (weight: number) => void;
  handleCheckboxChange: (issue: string, checked: boolean) => void;
  onNext: () => void;
  onPrevious: () => void;
}

export const Step2PetDetails: React.FC<Step2PetDetailsProps> = ({
  petName,
  petType,
  petAge,
  petAgeInMonths,
  petDateOfBirth,
  petSex,
  petWeight,
  healthIssues,
  handleAgeSliderChange,
  handleDateOfBirthChange,
  setPetSex,
  setPetWeight,
  handleCheckboxChange,
  onNext,
  onPrevious
}) => {
  const isValid = petDateOfBirth && petSex && petWeight > 0;

  return (
    <div className="max-w-2xl mx-auto">
      <Card>
        <CardHeader className="text-center pb-6">
          <CardTitle className="text-2xl font-bold">
            Tell us more about {petName}
          </CardTitle>
          <p className="text-muted-foreground">
            Help us personalize {petName}'s health journey
          </p>
        </CardHeader>
        
        <CardContent className="space-y-8">
          <AgeWeightSection
            petName={petName}
            petType={petType as any}
            petAge={petAge.toString()}
            petAgeInMonths={petAgeInMonths}
            petWeight={petWeight.toString()}
            petDateOfBirth={petDateOfBirth}
            handleDateOfBirthChange={handleDateOfBirthChange}
            setPetWeight={(weight: string) => setPetWeight(parseInt(weight) || 0)}
          />
          
          <GenderSelector
            petName={petName}
            petSex={petSex as any}
            setPetSex={setPetSex}
          />
          
          <HealthIssuesSection
            petName={petName}
            healthIssues={healthIssues}
            handleCheckboxChange={(issue: string) => handleCheckboxChange(issue, !healthIssues.includes(issue))}
          />
          
          <div className="flex justify-between pt-6">
            <Button variant="outline" onClick={onPrevious}>
              Previous
            </Button>
            <Button 
              onClick={onNext}
              disabled={!isValid}
              className="bg-brand-primary hover:bg-brand-dark"
            >
              Continue
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Step2PetDetails;
