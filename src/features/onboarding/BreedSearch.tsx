
import React, { useState, useEffect } from "react";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";
import { BreedResult, debounce, searchBreeds } from "./utils/breedSearch";
import { PetType } from "./types";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { Check, ChevronsUpDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { useIsMobile } from "@/hooks/use-mobile";

interface BreedSearchProps {
  petType: PetType | null;
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
}

const BreedSearch: React.FC<BreedSearchProps> = ({
  petType,
  value,
  onChange,
  placeholder = "Search for a breed...",
  className
}) => {
  const [open, setOpen] = useState(false);
  const [inputValue, setInputValue] = useState(value);
  const [results, setResults] = useState<BreedResult[]>([]);
  const isMobile = useIsMobile();
  
  // Perform search when input changes
  const performSearch = (query: string) => {
    if (!query.trim()) {
      setResults([]);
      return;
    }
    
    const searchResults = searchBreeds(query, petType);
    
    // Deduplicate results by breed name
    const uniqueResults = searchResults.reduce<BreedResult[]>((acc, current) => {
      const isDuplicate = acc.find(item => item.name === current.name);
      if (!isDuplicate) {
        acc.push(current);
      }
      return acc;
    }, []);
    
    setResults(uniqueResults);
  };

  // Debounced search function
  const debouncedSearch = debounce(performSearch, 200);

  // Update input value when external value changes
  useEffect(() => {
    setInputValue(value);
  }, [value]);

  // Handle input change
  const handleInputChange = (newValue: string) => {
    setInputValue(newValue);
    debouncedSearch(newValue);
  };

  // Handle selection
  const handleSelect = (selectedValue: string) => {
    onChange(selectedValue);
    setOpen(false);
  };

  // Close popover on blur
  const handleBlur = () => {
    setTimeout(() => {
      if (document.activeElement?.id !== 'breed-search-input') {
        setOpen(false);
      }
    }, 200);
  };

  // Add common options if needed
  const addCommonOptions = () => {
    if (!inputValue) return [];
    
    const commonOptions = [];
    if (results.length === 0 || !results.some(r => r.name === "Mixed Breed")) {
      commonOptions.push({ name: "Mixed Breed", type: "custom" as const });
    }
    
    if (results.length === 0 || !results.some(r => r.name === "Other")) {
      commonOptions.push({ name: "Other", type: "custom" as const });
    }
    
    return commonOptions;
  };

  // Get all results including common options
  const allResults = [...results, ...addCommonOptions()];

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "w-full justify-between text-left font-normal",
            !value && "text-muted-foreground",
            className
          )}
        >
          <span className="truncate">{value || placeholder}</span>
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent 
        className="w-full p-0" 
        align="start"
        sideOffset={5}
        style={{ width: isMobile ? "calc(100vw - 32px)" : "auto", maxWidth: "350px" }}
      >
        <Command>
          <CommandInput 
            id="breed-search-input"
            placeholder={placeholder} 
            value={inputValue}
            onValueChange={handleInputChange}
            onBlur={handleBlur}
            className="text-base md:text-sm"
          />
          <CommandList>
            <CommandEmpty>No breed found. You can select "Other" or "Mixed Breed".</CommandEmpty>
            <CommandGroup>
              {allResults.map((item) => (
                <CommandItem
                  key={item.name}
                  value={item.name}
                  onSelect={handleSelect}
                  className="text-base md:text-sm py-2 md:py-1.5"
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      value === item.name ? "opacity-100" : "opacity-0"
                    )}
                  />
                  <span className="truncate">{item.name}</span>
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
};

export default BreedSearch;
