import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { 
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle 
} from "@/components/ui/card";
import { ArrowRight, ArrowLeft } from "lucide-react";
import MembershipToggle from "./components/MembershipToggle";
import MembershipCard from "./components/MembershipCard";
import { useIsMobile } from "@/hooks/use-mobile";

interface Step3MembershipProps {
  onNext: () => void;
  onPrevious: () => void;
}

// Add MembershipType type
type MembershipType = "annual" | "monthly";

const MEMBERSHIP_FEATURES = [
  "Initial Health Assessment Call",
  "1 Microbiome Test Kit",
  "SENSE Score",
  "Pet Health Portal",
  "Personalized Health Protocol",
  "Lifestyle coaching",
  "Monthly health advisor calls",
  "Daily check-ins and streaks",
  "Daily reminders and recommendations",
  "24/7 Smart Health Coach Access", 
  "Quarterly Health Reviews",
  "Free Shipping on all AutoShip shipments",
  "20% member only discount on all products",
  "Priority Customer Support"
];

const Step3Membership: React.FC<Step3MembershipProps> = ({ onNext, onPrevious }) => {
  // Add state with annual as default selected
  const [selectedMembership, setSelectedMembership] = useState<MembershipType>("annual");

  // Calculate monthly price with 25% savings for annual
  const annualPrice = 299;
  const monthlyBase = annualPrice / 9; // Annual price / 9 months (giving 3 months free)
  const monthlyPrice = Math.round(monthlyBase); // Round to whole dollar amount

  // Annual savings calculation
  const annualSavings = (monthlyPrice * 12) - annualPrice;
  const savingsPercentage = Math.round((annualSavings / (monthlyPrice * 12)) * 100);

  // Handler for continuing with selected membership
  const handleContinue = () => {
    // Could pass selectedMembership to parent component if needed
    onNext();
  };

  // Handler for membership type change
  const handleMembershipChange = (value: MembershipType) => {
    setSelectedMembership(value);
  };

  const isMobile = useIsMobile();

  return (
    <Card className="border-brand-primary/20 shadow-lg overflow-hidden">
      <CardHeader className="bg-gradient-to-r from-brand-light to-brand-accent/30 pb-6">
        <CardTitle className="text-center text-2xl">Choose Your Membership</CardTitle>
        <CardDescription className="text-center">
          Select the plan that best suits your pet's needs.
        </CardDescription>
        
        {/* Toggle Group for Selection */}
        <MembershipToggle 
          selectedMembership={selectedMembership} 
          onMembershipChange={handleMembershipChange} 
        />
      </CardHeader>
      
      <CardContent className="pt-6">
        <div className="flex justify-center">
          {/* Membership Card */}
          <MembershipCard
            selectedMembership={selectedMembership}
            annualPrice={annualPrice}
            monthlyPrice={monthlyPrice}
            savingsPercentage={savingsPercentage}
            features={MEMBERSHIP_FEATURES}
          />
        </div>
        
        {/* Satisfaction guarantee notice */}
        <div className="mt-6 text-center text-sm text-muted-foreground">
          Both plans include a 30-day satisfaction guarantee. Cancel anytime.
        </div>
      </CardContent>
      
      <CardFooter className={`flex ${isMobile ? "flex-col" : "justify-between"} gap-4 bg-gray-50 border-t p-4`}>
        <Button 
          variant="outline" 
          onClick={onPrevious}
          className={isMobile ? "w-full" : ""}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <Button 
          onClick={handleContinue} 
          className={`bg-brand-primary hover:bg-brand-dark ${isMobile ? "w-full" : ""}`}
        >
          Continue with {selectedMembership === "annual" ? "Annual" : "Monthly"} Plan
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </CardFooter>
    </Card>
  );
};

export default Step3Membership;
