
import React, { useState } from "react";
import { Textarea } from "@/components/ui/textarea";
import { HEALTH_CATEGORIES } from "./types";
import { cn } from "@/lib/utils";
import { useIsMobile } from "@/hooks/use-mobile";
import { useToast } from "@/hooks/use-toast";

interface HealthIssuesSelectorProps {
  petName: string;
  healthIssues: string[];
  handleCheckboxChange: (issue: string) => void;
  className?: string;
}

const HealthIssuesSelector: React.FC<HealthIssuesSelectorProps> = ({
  petName,
  healthIssues,
  handleCheckboxChange,
  className
}) => {
  const isMobile = useIsMobile();
  const { toast } = useToast();
  const [otherIssue, setOtherIssue] = useState("");
  const [otherIssueError, setOtherIssueError] = useState<string | null>(null);
  
  // Extract all unique symptoms from health categories
  const allSymptoms = HEALTH_CATEGORIES.flatMap(category => 
    category.category !== "Other" ? category.symptoms : []
  );
  
  const handleOtherIssueChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    setOtherIssue(newValue);
    setOtherIssueError(null);
    
    try {
      // Remove any previous "Other:" entries
      const filteredIssues = healthIssues.filter(issue => !issue.startsWith("Other:"));
      
      if (newValue.trim()) {
        if (newValue.length > 200) {
          setOtherIssueError("Description is too long (max 200 characters)");
          return;
        }
        handleCheckboxChange(`Other: ${newValue.trim()}`);
      } else if (healthIssues.some(issue => issue.startsWith("Other:"))) {
        handleCheckboxChange(`Other: ${healthIssues.find(issue => issue.startsWith("Other:"))?.substring(7) || ""}`);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update health issue. Please try again.",
        variant: "destructive",
      });
      console.error("Error handling other issue change:", error);
    }
  };

  const handleSymptomSelection = (symptom: string) => {
    try {
      handleCheckboxChange(symptom);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update health issue. Please try again.",
        variant: "destructive",
      });
      console.error("Error handling symptom selection:", error);
    }
  };

  return (
    <div className={cn("space-y-5", className)}>
      <div className="text-center">
        <h3 className={`font-medium text-foreground mb-2 ${isMobile ? 'text-base' : 'text-lg'}`}>
          Any health concerns with {petName}?
        </h3>
        <p className={`text-muted-foreground ${isMobile ? 'text-sm' : 'text-sm'}`}>
          Select all that apply, or skip if none
        </p>
      </div>
      
      <div className="grid gap-2 grid-cols-1">
        {allSymptoms.map((symptom) => (
          <button
            key={symptom}
            type="button"
            onClick={() => handleSymptomSelection(symptom)}
            className={cn(
              `px-4 py-3 text-sm rounded-lg font-medium transition-all text-left`,
              healthIssues.includes(symptom)
                ? "bg-brand-primary text-white"
                : "bg-white hover:bg-brand-light/50 border border-gray-200 hover:border-brand-primary/30 text-foreground"
            )}
          >
            {symptom}
          </button>
        ))}
      </div>
      
      {/* Other option */}
      <div className="space-y-3">
        <button
          type="button"
          onClick={() => {
            if (!healthIssues.some(issue => issue.startsWith("Other:"))) {
              handleCheckboxChange(`Other: ${otherIssue}`);
            } else {
              handleCheckboxChange(`Other: ${otherIssue}`);
            }
          }}
          className={cn(
            `w-full px-4 py-3 text-sm rounded-lg font-medium transition-all text-left`,
            healthIssues.some(issue => issue.startsWith("Other:"))
              ? "bg-brand-primary text-white"
              : "bg-white hover:bg-brand-light/50 border border-gray-200 hover:border-brand-primary/30 text-foreground"
          )}
        >
          Something else not listed here
        </button>
        
        {healthIssues.some(issue => issue.startsWith("Other:")) && (
          <div className="space-y-2">
            <Textarea 
              placeholder="Please describe the health concern..."
              value={otherIssue}
              onChange={handleOtherIssueChange}
              className={cn(
                "min-h-[80px] border-2 focus:border-brand-primary",
                otherIssueError ? "border-destructive" : ""
              )}
            />
            {otherIssueError && (
              <p className="text-sm text-destructive">{otherIssueError}</p>
            )}
          </div>
        )}
      </div>
      
      {healthIssues.length > 0 && (
        <div className="text-center">
          <p className="text-sm text-muted-foreground">
            {healthIssues.length} issue{healthIssues.length !== 1 ? "s" : ""} selected
          </p>
        </div>
      )}
    </div>
  );
};

export default HealthIssuesSelector;
