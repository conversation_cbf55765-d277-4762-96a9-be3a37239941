
import React, { useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import Logo from "@/components/ui/logo";
import { useIsMobile } from "@/hooks/use-mobile";
import { Step } from "./types";

interface OnboardingLayoutProps {
  children: React.ReactNode;
  currentStep?: Step;
}

const OnboardingLayout: React.FC<OnboardingLayoutProps> = ({ children, currentStep }) => {
  const navigate = useNavigate();
  const isMobile = useIsMobile();

  // Scroll to top when the component mounts
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const getHeadingText = () => {
    if (currentStep === 5) {
      return "Welcome to AnimalBiome!";
    }
    return "Let's get to know your furry friend";
  };

  return (
    <div className="min-h-screen flex flex-col bg-brand-light">
      {/* Header */}
      <header className="py-3 sm:py-4 px-3 sm:px-6 border-b border-brand-beige/50 bg-white/95 backdrop-blur-sm">
        <div className="container flex items-center justify-between">
          <div className="flex items-center">
            <Logo size="small" responsive={true} />
          </div>
          {currentStep !== 5 && (
            <Button variant="outline" size={isMobile ? "sm" : "default"} onClick={() => navigate("/login")}>
              Login
            </Button>
          )}
        </div>
      </header>

      {/* Main content */}
      <main className="flex-1 container max-w-3xl mx-auto py-5 px-4 sm:py-6 md:py-10 flex flex-col">
        <div className="text-center mb-4 sm:mb-6">
          <h1 className={`${isMobile ? "text-xl" : "text-3xl"} font-serif mb-1 sm:mb-2`}>
            {getHeadingText()}
          </h1>
        </div>
        
        {children}
      </main>

      {/* Footer */}
      <footer className="py-3 sm:py-4 border-t border-brand-beige/50 mt-auto bg-white">
        <div className="container text-center text-xs text-muted-foreground px-4">
          <p>&copy; {new Date().getFullYear()} SENSE x AnimalBiome. All rights reserved.</p>
        </div>
      </footer>
    </div>
  );
};

export default OnboardingLayout;
