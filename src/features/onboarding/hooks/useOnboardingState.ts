
import { useState } from "react";
import { PetType, PetSex, Step } from "../types";

export function useOnboardingState() {
  const [currentStep, setCurrentStep] = useState<Step>(1);
  const [petType, setPetType] = useState<PetType | null>(null);
  const [petName, setPetName] = useState("");
  const [petBreed, setPetBreed] = useState("");
  const [petAge, setPetAge] = useState("");
  const [petAgeInMonths, setPetAgeInMonths] = useState<number>(6);
  const [petDateOfBirth, setPetDateOfBirth] = useState<Date | null>(null);
  const [petSex, setPetSex] = useState<PetSex | null>(null);
  const [petWeight, setPetWeight] = useState("");
  const [healthIssues, setHealthIssues] = useState<string[]>([]);
  const [ownerName, setOwnerName] = useState("");
  const [ownerEmail, setOwnerEmail] = useState("");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [addressLine1, setAddressLine1] = useState("");
  const [addressLine2, setAddressLine2] = useState("");
  const [city, setCity] = useState("");
  const [state, setState] = useState("");
  const [zipCode, setZipCode] = useState("");
  const [country, setCountry] = useState("US");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleCheckboxChange = (issue: string) => {
    setHealthIssues(prev => 
      prev.includes(issue)
        ? prev.filter(i => i !== issue)
        : [...prev, issue]
    );
  };

  // Convert months to formatted age string with decimal years
  const formatAgeDisplay = (ageInMonths: number): string => {
    if (ageInMonths < 12) {
      return `${ageInMonths} month${ageInMonths !== 1 ? 's' : ''} old`;
    } else {
      const years = ageInMonths / 12;
      const roundedYears = Math.round(years * 10) / 10; // Round to 1 decimal place
      return `${roundedYears} year${roundedYears !== 1 ? 's' : ''} old`;
    }
  };

  // Update both petAge string and petAgeInMonths when slider changes
  const handleAgeSliderChange = (value: number[]) => {
    const ageMonths = value[0];
    setPetAgeInMonths(ageMonths);
    setPetAge(formatAgeDisplay(ageMonths));
  };

  // Calculate age in months from date of birth
  const calculateAgeFromBirthDate = (birthDate: Date): number => {
    const today = new Date();
    const ageInMonths = (today.getFullYear() - birthDate.getFullYear()) * 12 + 
                       (today.getMonth() - birthDate.getMonth());
    return Math.max(1, ageInMonths); // Ensure minimum age of 1 month
  };

  // Handle date of birth change
  const handleDateOfBirthChange = (date: Date | null) => {
    setPetDateOfBirth(date);
    if (date) {
      const ageMonths = calculateAgeFromBirthDate(date);
      setPetAgeInMonths(ageMonths);
      setPetAge(formatAgeDisplay(ageMonths));
    }
  };

  return {
    currentStep,
    setCurrentStep,
    petType,
    setPetType,
    petName,
    setPetName,
    petBreed,
    setPetBreed,
    petAge,
    setPetAge,
    petAgeInMonths,
    setPetAgeInMonths,
    petDateOfBirth,
    setPetDateOfBirth,
    formatAgeDisplay,
    handleAgeSliderChange,
    handleDateOfBirthChange,
    calculateAgeFromBirthDate,
    petSex,
    setPetSex,
    petWeight,
    setPetWeight,
    healthIssues,
    setHealthIssues,
    ownerName,
    setOwnerName,
    ownerEmail,
    setOwnerEmail,
    phoneNumber,
    setPhoneNumber,
    addressLine1,
    setAddressLine1,
    addressLine2,
    setAddressLine2,
    city,
    setCity,
    state,
    setState,
    zipCode,
    setZipCode,
    country,
    setCountry,
    isSubmitting,
    setIsSubmitting,
    handleCheckboxChange
  };
}
