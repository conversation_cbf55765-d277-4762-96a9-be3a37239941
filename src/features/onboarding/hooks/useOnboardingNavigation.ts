
import { Step } from "../types";
import { validateStep } from "../utils/validation";

interface UseOnboardingNavigationProps {
  currentStep: Step;
  setCurrentStep: (step: Step) => void;
  petType: any;
  petName: string;
  petAge: string;
  petSex: any;
  setIsSubmitting: (isSubmitting: boolean) => void;
  ownerName: string;
}

export const useOnboardingNavigation = ({
  currentStep,
  setCurrentStep,
  petType,
  petName,
  petAge,
  petSex,
  setIsSubmitting,
  ownerName
}: UseOnboardingNavigationProps) => {
  // Helper function to scroll to top of the page
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth"
    });
  };
  
  const handleNextStep = () => {
    if (validateStep(currentStep, { petType, petName, petAge, petSex })) {
      // Move to next step
      const nextStep = (currentStep + 1) as Step;
      setCurrentStep(nextStep);
      
      // Scroll to top of page
      scrollToTop();
    }
  };

  const handlePreviousStep = () => {
    const prevStep = (currentStep - 1) as Step;
    setCurrentStep(prevStep);
    
    // Scroll to top of page
    scrollToTop();
  };

  return {
    handleNextStep,
    handlePreviousStep
  };
};
