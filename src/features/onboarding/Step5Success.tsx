
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { ArrowRight, Package, MessageSquare, Star, Calendar, Phone, UserCheck } from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";
import { PetType } from "./types";
import confetti from "canvas-confetti";

interface Step5SuccessProps {
  petName: string;
  petType: PetType | null;
  healthIssues: string[];
}

const Step5Success: React.FC<Step5SuccessProps> = ({ 
  petName, 
  petType,
  healthIssues 
}) => {
  const navigate = useNavigate();
  const isMobile = useIsMobile();

  // Trigger confetti when component mounts
  React.useEffect(() => {
    confetti({
      particleCount: 100,
      spread: 70,
      origin: { y: 0.6 }
    });
  }, []);

  const renderActionCard = (
    icon: React.ReactNode,
    title: string,
    description: string,
    actionText: string,
    onClick: () => void,
    variant: "default" | "outline" = "default",
    priority: boolean = false
  ) => (
    <div className={`border rounded-lg p-4 shadow-sm ${priority ? 'bg-brand-light border-brand-primary' : 'bg-white'}`}>
      <div className="flex justify-between items-start mb-2">
        <div className="flex gap-3 items-center mb-2">
          {icon}
          <h3 className={`font-medium ${priority ? 'text-brand-primary' : ''}`}>{title}</h3>
          {priority && <Badge variant="accent" className="ml-2 text-xs">Recommended</Badge>}
        </div>
      </div>
      <p className="text-sm text-muted-foreground mb-3">{description}</p>
      <div className="flex justify-end">
        <Button 
          variant={priority ? "default" : variant} 
          onClick={onClick} 
          className={priority ? "bg-brand-primary hover:bg-brand-dark" : variant === "outline" ? "text-brand-primary" : ""}
        >
          {actionText}
          <ArrowRight className="ml-1 h-4 w-4" />
        </Button>
      </div>
    </div>
  );

  // Get sample recommended supplements based on health issues
  const getRecommendedSupplements = () => {
    if (healthIssues.includes("Digestive issues")) {
      return petType === "dog" ? "Probiotic Digestive Support" : "Feline Digestive Health Formula";
    } else if (healthIssues.includes("Itchy skin")) {
      return petType === "dog" ? "Canine Skin & Coat Support" : "Feline Dermal Health Blend";
    } else if (healthIssues.includes("Joint pain or stiffness")) {
      return "Joint & Mobility Support";
    } else {
      return petType === "dog" ? "Canine Daily Wellness" : "Feline Complete";
    }
  };

  return (
    <div className="space-y-6">
      <Card className="border-brand-primary/20 shadow-lg">
        <CardHeader className="pb-4">
          <div className="flex justify-center mb-2">
            <Badge variant="accent" className="font-semibold px-3 py-1 text-sm">
              Welcome to the Family!
            </Badge>
          </div>
          <CardTitle className={`text-center ${isMobile ? 'text-xl' : 'text-2xl'}`}>
            Congratulations! You're all set up 🎉
          </CardTitle>
          <CardDescription className="text-center text-base">
            {petName}'s journey to better health has officially begun.
          </CardDescription>
        </CardHeader>
        
        <CardContent>
          <div className="bg-brand-light p-4 rounded-lg mb-6">
            <div className="flex items-start gap-3">
              <Package className="h-5 w-5 text-brand-primary mt-1 flex-shrink-0" />
              <div>
                <h3 className="font-medium text-brand-primary">Your Microbiome Kit is on the way!</h3>
                <p className="text-sm text-muted-foreground">
                  Expected to arrive within 3-5 business days. We'll send you an email with tracking information.
                </p>
              </div>
            </div>
          </div>
          
          <h3 className="font-medium text-lg mb-4">Complete your pet's health profile</h3>
          
          <div className="space-y-4">
            {renderActionCard(
              <Calendar className="h-5 w-5 text-brand-primary" />,
              "Schedule Initial Assessment",
              `Book a 15-minute consultation with our veterinary team to review ${petName}'s health profile and create a personalized plan.`,
              "Schedule Assessment",
              () => navigate("/dashboard?tab=schedule"),
              "default",
              true
            )}

            {renderActionCard(
              <Phone className="h-5 w-5 text-green-600" />,
              "Enable Daily SMS Reminders",
              "Get daily text reminders for supplements, health tips, and progress tracking to keep your pet on track.",
              "Setup SMS Alerts",
              () => navigate("/settings?tab=notifications"),
              "outline"
            )}
            
            {renderActionCard(
              <MessageSquare className="h-5 w-5 text-blue-600" />,
              "Get AI Health Guidance",
              `Receive immediate AI-powered advice for ${petName}'s specific health concerns.`,
              "Try AI Coach",
              () => navigate("/coach"),
              "outline"
            )}
            
            {renderActionCard(
              <Star className="h-5 w-5 text-amber-500" />,
              "Recommended Supplements",
              `Based on ${petName}'s profile, we recommend trying: ${getRecommendedSupplements()}`,
              "View Supplements",
              () => navigate("/supplements"),
              "outline"
            )}

            {renderActionCard(
              <UserCheck className="h-5 w-5 text-purple-600" />,
              "Complete Profile Details",
              "Add additional information about your pet's diet, activity level, and preferences for better recommendations.",
              "Complete Profile",
              () => navigate("/settings?tab=pet"),
              "outline"
            )}
          </div>
          
          <div className="mt-8 flex justify-center">
            <Button
              onClick={() => navigate("/dashboard")}
              variant="outline"
              className="border-brand-primary text-brand-primary hover:bg-brand-primary hover:text-white"
              size={isMobile ? "lg" : "default"}
            >
              Go to Dashboard
              <ArrowRight className="ml-1 h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Step5Success;
