
export interface RiskFactor {
  id: string;
  name: string;
  description: string;
  riskScore: number;
  severity: "low" | "moderate" | "high";
  category: "genetic" | "dietary" | "environmental";
  recommendations: string[];
}

export interface PreventativeIntervention {
  type: "supplement" | "nutrition" | "exercise" | "environment";
  name: string;
  description: string;
  frequency: string;
  duration: string;
}

export interface PreventativePlan {
  id: string;
  name: string;
  description: string;
  riskFactors: string[];
  interventions: PreventativeIntervention[];
  monitoringSchedule: string;
  expectedOutcomes: string[];
}
