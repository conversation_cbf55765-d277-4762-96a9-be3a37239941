
export interface ShapeCategory {
  id: string;
  name: string;
  icon: string;
  description: string;
  value?: number; // 1-5 rating
  notes?: string;
  lastUpdated?: Date;
}

export interface DailyCheckIn {
  id: string;
  date: string;
  petId: string;
  sleep: ShapeCategory;
  hydration: ShapeCategory;
  activity: ShapeCategory;
  poop: ShapeCategory;
  eating: ShapeCategory;
  supplementAdherence: {
    supplements: string[];
    adherenceRate: number; // percentage
    missedDoses: string[];
  };
  completed: boolean;
  overallScore?: number;
}

export interface PersonalizedRecommendation {
  id: string;
  category: string;
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high';
  action: string;
  dueDate?: Date;
  confidenceScore: number;
  targetedIssues: string[];
  expectedOutcome: string;
  timeToEffect: string;
  supportingEvidence: string;
}
