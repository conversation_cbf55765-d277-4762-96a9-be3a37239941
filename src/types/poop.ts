
export interface PoopLog {
  id: string;
  date: string;
  dogName: string;
  type: number; // 1-7 Bristol Stool Chart
  easeOfPickup: 'easy' | 'sticky' | 'mushy';
  color: 'brown' | 'black' | 'red' | 'pale' | 'greenish';
  odor: 'normal' | 'strong' | 'sour';
  frequency: boolean; // did they poop today
  straining: 'none' | 'mild' | 'a-lot';
  photoUrl?: string;
  score: number; // 1-100
  feedback: string;
  completed: boolean;
}

export interface PoopType {
  id: number;
  name: string;
  description: string;
  imageUrl: string;
  healthScore: number;
}

export const POOP_TYPES: PoopType[] = [
  { 
    id: 1, 
    name: "Hard Pellets", 
    description: "Separate hard lumps", 
    imageUrl: "/lovable-uploads/d588e0ea-20df-4dd6-99e6-bf2a4221cada.png", 
    healthScore: 30 
  },
  { 
    id: 2, 
    name: "Lumpy & Firm", 
    description: "Sausage-shaped but lumpy", 
    imageUrl: "/lovable-uploads/97d2625f-a22a-4d61-82b7-fc5b06a1a5ff.png", 
    healthScore: 50 
  },
  { 
    id: 3, 
    name: "Firm with Cracks", 
    description: "Like a sausage with cracks", 
    imageUrl: "/lovable-uploads/ee40cd7c-9863-44f5-bc2b-bd8e27932ce0.png", 
    healthScore: 85 
  },
  { 
    id: 4, 
    name: "Smooth & Soft", 
    description: "Smooth and soft sausage", 
    imageUrl: "/lovable-uploads/a84ac689-8025-4952-b843-5dbcd655cbb9.png", 
    healthScore: 100 
  },
  { 
    id: 5, 
    name: "Soft Blobs", 
    description: "Soft blobs with clear edges", 
    imageUrl: "/lovable-uploads/383dc8c9-999a-4293-9037-257f7badd78d.png", 
    healthScore: 70 
  },
  { 
    id: 6, 
    name: "Mushy", 
    description: "Fluffy pieces with ragged edges", 
    imageUrl: "/lovable-uploads/35483b31-9c22-4eaa-ade4-7722afc26b78.png", 
    healthScore: 40 
  },
  { 
    id: 7, 
    name: "Watery", 
    description: "Watery, no solid pieces", 
    imageUrl: "/lovable-uploads/617f972f-f06f-46db-8db7-1dd483d8fd47.png", 
    healthScore: 20 
  }
];

export interface CheckInStep {
  step: number;
  title: string;
  completed: boolean;
}
