export interface Message {
  id: number;
  sender: "user" | "ai";
  text: string;
  timestamp: Date;
  threadId?: string;
  error?: boolean;
  citations?: Citation[];
  sources?: Source[];
}

export interface Citation {
  id: string;
  text: string;
  sourceId: string;
  position: number;
}

export interface Source {
  id: string;
  title: string;
  url?: string;
  type: 'document' | 'web' | 'research' | 'knowledge';
  description?: string;
  relevance?: number;
}

export interface CommunityPost {
  id: string;
  authorId: string;
  authorName: string;
  petName: string;
  petType: "dog" | "cat";
  title: string;
  content: string;
  tags: string[];
  likes: number;
  comments: number;
  timestamp: Date;
  isPrivate: boolean;
}

export interface Comment {
  id: string;
  postId: string;
  authorId: string;
  authorName: string;
  content: string;
  timestamp: Date;
}
