
import React, { createContext, useContext, ReactNode, useState } from "react";
import { supabase } from "../integrations/supabase/client";

interface PetData {
  id: string;
  name: string;
  type: string;
  breed?: string;
  age?: number;
  sex?: string;
  weight?: number;
  healthIssues?: string[];
  goals?: string[];
  photo_url?: string;
  tenant_id: string;
}

interface PetContextType {
  petData: PetData;
  setPetData: (data: PetData) => void;
  fetchPetData: () => Promise<void>;
}

const defaultPetData: PetData = {
  id: "temp-pet-id", 
  name: "<PERSON>",
  type: "dog",
  breed: "Golden Retriever",
  age: 36,
  sex: "female",
  weight: 65,
  healthIssues: ["Sensitive stomach", "Seasonal allergies"],
  goals: ["Improve gut health", "Better stool consistency"],
  photo_url: "",
  tenant_id: "temp-tenant-id"
};

const PetContext = createContext<PetContextType>({ 
  petData: defaultPetData,
  setPetData: () => {},
  fetchPetData: async () => {}
});

export const usePetData = () => useContext(PetContext);

export const PetDataProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [petData, setPetData] = useState<PetData>(defaultPetData);

  const fetchPetData = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const { data: pets, error } = await supabase
        .from('pets')
        .select('*')
        .eq('owner_id', user.id)
        .single();

      if (error) {
        console.log('No pet data found, using default');
        return;
      }

      if (pets) {
        setPetData({
          id: pets.id,
          name: pets.name,
          type: pets.type,
          breed: pets.breed,
          age: pets.age_months,
          sex: pets.sex,
          weight: pets.weight_lbs,
          healthIssues: pets.health_issues || [],
          goals: pets.goals || [],
          photo_url: pets.photo_url,
          tenant_id: pets.tenant_id
        });
      }
    } catch (error) {
      console.error('Error fetching pet data:', error);
    }
  };

  return (
    <PetContext.Provider value={{ petData, setPetData, fetchPetData }}>
      {children}
    </PetContext.Provider>
  );
};
