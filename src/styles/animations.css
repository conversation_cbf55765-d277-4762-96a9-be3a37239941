
/* Custom animations */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes scale-in {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-element {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.8s ease, transform 0.8s ease;
  transition-delay: calc(var(--delay, 0) * 100ms);
}

.animate-element:nth-child(1) { --delay: 1; }
.animate-element:nth-child(2) { --delay: 2; }
.animate-element:nth-child(3) { --delay: 3; }
.animate-element:nth-child(4) { --delay: 4; }
.animate-element:nth-child(5) { --delay: 5; }

.animate-in {
  opacity: 1;
  transform: translateY(0);
}

.animate-fade-in {
  animation: fade-in 0.8s forwards;
}

.animate-scale-in {
  animation: scale-in 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse 6s ease-in-out infinite;
}
