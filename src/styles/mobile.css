
/* Safe area support for mobile devices */
@supports (padding: max(0px)) {
  .safe-area-top {
    padding-top: max(1rem, env(safe-area-inset-top));
  }
  .safe-area-bottom {
    padding-bottom: max(1rem, env(safe-area-inset-bottom));
  }
  .pb-safe {
    padding-bottom: max(1rem, env(safe-area-inset-bottom));
  }
}

/* Fixed mobile scrolling issues */
@media (max-width: 768px) {
  html {
    height: 100%;
    height: 100dvh;
    overflow: auto;
  }
  
  body {
    height: 100%;
    height: 100dvh;
    overflow: auto;
    position: relative;
    width: 100%;
  }
  
  /* Enhanced touch scrolling for all scrollable containers */
  *[class*="overflow-y-auto"],
  *[class*="overflow-auto"],
  .scroll-container {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
    overscroll-behavior-y: contain;
  }
  
  /* CRITICAL: Prevent zoom on input focus - Enhanced specificity */
  input[type="text"],
  input[type="email"],
  input[type="password"],
  textarea,
  select,
  .chat-input-mobile,
  input.chat-input-mobile,
  textarea.chat-input-mobile {
    font-size: 16px !important;
    -webkit-text-size-adjust: 100% !important;
    text-size-adjust: 100% !important;
    transform-origin: left top;
    transition: none !important;
  }

  /* Specific chat input styling to prevent zoom */
  [data-chat-input] input,
  [data-chat-input] textarea,
  .chat-input-mobile {
    font-size: 16px !important;
    -webkit-appearance: none !important;
    appearance: none !important;
  }
  
  /* Enhanced touch targets - Uber/Airbnb standard (44px minimum) */
  button, a, [role="button"], .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Enhanced card touch targets */
  .cursor-pointer {
    min-height: 44px;
    display: flex;
    align-items: center;
  }

  /* Enhanced star rating touch targets */
  .star-rating-button {
    min-height: 50px;
    min-width: 50px;
    border-radius: 50%;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .star-rating-button:active {
    transform: scale(0.95);
    background-color: rgba(59, 130, 246, 0.1);
  }
  
  /* Improved modal and dialog scrolling */
  [role="dialog"],
  .modal-content,
  .sheet-content {
    overscroll-behavior: contain;
    -webkit-overflow-scrolling: touch;
  }

  /* Dashboard specific mobile improvements */
  .dashboard-content {
    height: auto;
    min-height: 100dvh;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }

  /* Enhanced tabs scrolling on mobile */
  [role="tablist"] {
    flex-wrap: nowrap;
    gap: 0.5rem;
    padding-bottom: 0.5rem;
    overflow-x: auto;
    scrollbar-width: none;
    -webkit-overflow-scrolling: touch;
    overscroll-behavior-x: contain;
  }
  
  [role="tablist"]::-webkit-scrollbar {
    display: none;
  }
  
  [role="tab"] {
    white-space: nowrap;
    min-height: 44px;
    display: flex;
    align-items: center;
    font-size: 0.875rem;
    padding-left: 0.75rem;
    padding-right: 0.75rem;
    flex-shrink: 0;
  }

  /* Enhanced keyboard-aware styling */
  .chat-input-mobile:focus,
  .chat-input-mobile:active {
    font-size: 16px !important;
    zoom: 1 !important;
    -webkit-user-zoom: disabled !important;
    user-zoom: disabled !important;
  }

  /* Keyboard-aware layout adjustments */
  @supports (height: env(keyboard-inset-height)) {
    .keyboard-aware-layout {
      height: calc(100vh - env(keyboard-inset-height));
    }
  }

  /* Smooth transitions for keyboard appearance */
  .chat-modal-container {
    transition: height 0.2s ease-in-out;
  }

  /* Welcome message visibility improvements */
  .welcome-message-container {
    scroll-margin-top: 1rem;
    scroll-snap-align: start;
  }

  /* Enhanced button animations - Uber style */
  .hover\:scale-\[1\.02\]:hover {
    transform: scale(1.02);
    transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .active\:scale-\[0\.98\]:active {
    transform: scale(0.98);
    transition: transform 0.1s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Enhanced shadow animations */
  .hover\:shadow-lg:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transition: box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Progress bar animations */
  .progress-bar-animated {
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }

  @keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
  }

  /* Enhanced loading states */
  .skeleton-pulse {
    animation: skeleton-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes skeleton-pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
  }

  /* Status indicator animations */
  .status-indicator {
    position: relative;
  }

  .status-indicator::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: inherit;
    animation: status-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes status-pulse {
    0%, 100% { 
      transform: scale(1);
      opacity: 0.5;
    }
    50% { 
      transform: scale(1.2);
      opacity: 0;
    }
  }

  /* Enhanced modal animations */
  .modal-slide-up {
    animation: modal-slide-up 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  @keyframes modal-slide-up {
    0% {
      transform: translateY(100%);
      opacity: 0;
    }
    100% {
      transform: translateY(0);
      opacity: 1;
    }
  }

  /* Star rating enhanced animations */
  .star-hover-effect {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .star-hover-effect:hover {
    transform: scale(1.1) rotate(5deg);
    filter: drop-shadow(0 4px 8px rgba(251, 191, 36, 0.3));
  }

  .star-selected {
    animation: star-selected 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }

  @keyframes star-selected {
    0% { transform: scale(1); }
    50% { transform: scale(1.3) rotate(10deg); }
    100% { transform: scale(1.1); }
  }

  /* Rating completion celebration */
  .rating-celebration {
    animation: rating-celebration 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }

  @keyframes rating-celebration {
    0% { 
      transform: scale(0.8) rotate(-5deg);
      opacity: 0;
    }
    50% { 
      transform: scale(1.1) rotate(5deg);
      opacity: 1;
    }
    100% { 
      transform: scale(1) rotate(0deg);
      opacity: 1;
    }
  }
}

@media (max-width: 640px) {
  .test-chart-wrapper {
    @apply h-[200px];
  }

  /* Enhanced small screen optimizations */
  [role="tablist"] {
    flex-wrap: nowrap;
    gap: 0.5rem;
    padding-bottom: 0.5rem;
    overflow-x: auto;
    scrollbar-width: none;
    -webkit-overflow-scrolling: touch;
  }
  
  [role="tablist"]::-webkit-scrollbar {
    display: none;
  }
  
  [role="tab"] {
    white-space: normal;
    min-height: 44px;
    display: flex;
    align-items: center;
    word-break: break-word;
    hyphens: auto;
    font-size: 0.75rem;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  /* Enhanced card layouts for small screens */
  .grid-cols-1 {
    gap: 0.75rem;
  }

  /* Better spacing for small screens */
  .space-y-3 > :not([hidden]) ~ :not([hidden]) {
    margin-top: 0.75rem;
  }

  /* Modal enhancements for small screens */
  .modal-content-small {
    margin: 1rem;
    max-height: calc(100vh - 2rem);
    border-radius: 1rem;
  }
}

.overscroll-contain {
  overscroll-behavior: contain;
  -webkit-overflow-scrolling: touch;
}

.overscroll-none {
  overscroll-behavior: none;
  -webkit-overflow-scrolling: touch;
}

/* Enhanced haptic feedback simulation */
.haptic-light {
  animation: haptic-light 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes haptic-light {
  0% { transform: scale(1); }
  50% { transform: scale(0.96); }
  100% { transform: scale(1); }
}

.haptic-medium {
  animation: haptic-medium 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes haptic-medium {
  0% { transform: scale(1); }
  25% { transform: scale(0.94); }
  75% { transform: scale(1.02); }
  100% { transform: scale(1); }
}

/* Enhanced visual feedback */
.completion-celebration {
  animation: completion-celebration 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes completion-celebration {
  0% { 
    transform: scale(1) rotate(0deg);
    filter: brightness(1);
  }
  25% { 
    transform: scale(1.15) rotate(8deg);
    filter: brightness(1.3);
  }
  50% { 
    transform: scale(1.1) rotate(-5deg);
    filter: brightness(1.2);
  }
  75% { 
    transform: scale(1.05) rotate(2deg);
    filter: brightness(1.1);
  }
  100% { 
    transform: scale(1) rotate(0deg);
    filter: brightness(1);
  }
}

/* Progress milestone animation */
.milestone-achieved {
  animation: milestone-achieved 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes milestone-achieved {
  0% { 
    transform: scale(0.8);
    opacity: 0;
  }
  50% { 
    transform: scale(1.2);
    opacity: 0.8;
  }
  100% { 
    transform: scale(1);
    opacity: 1;
  }
}

/* Streak fire animation */
.streak-fire {
  animation: streak-fire 1s ease-in-out infinite alternate;
}

@keyframes streak-fire {
  0% { 
    transform: scale(1) rotate(-2deg);
    filter: hue-rotate(0deg);
  }
  100% { 
    transform: scale(1.05) rotate(2deg);
    filter: hue-rotate(10deg);
  }
}
