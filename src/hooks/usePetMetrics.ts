
import { useMemo } from "react";

type ShapeRatings = {
  sleep: number;
  hydration: number;
  activity: number;
  poop: number;
  eating: number;
};

export const usePetMetrics = (shapeRatings: ShapeRatings) => {
  // Calculate overall score
  const overallScore = useMemo(() => {
    const ratings = Object.values(shapeRatings);
    return ratings.some(rating => rating > 0) 
      ? ratings.reduce((a, b) => a + b, 0) / ratings.length 
      : 0;
  }, [shapeRatings]);

  // Calculate streak (simplified)
  const streak = useMemo(() => {
    const completedCategories = Object.values(shapeRatings).filter(rating => rating > 0).length;
    const isFullyCompleted = completedCategories === 5;
    return isFullyCompleted ? 3 : 0; // Mock streak calculation
  }, [shapeRatings]);

  // Helper functions
  const hasAnyEngagement = () => {
    return Object.values(shapeRatings).some(rating => rating > 0);
  };

  const getMotivationalMessage = (completedCount: number, totalCount: number) => {
    const percentage = Math.round((completedCount / totalCount) * 100);
    if (percentage >= 100) return "Amazing! You've completed everything today! 🎉";
    if (percentage >= 80) return `You're ${percentage}% there! Just one more to go! 💪`;
    if (percentage >= 60) return `Great job! You're ${percentage}% there! 🎯`;
    if (percentage >= 40) return `Keep going! You're ${percentage}% done! 🚀`;
    if (percentage >= 20) return `Good start! You're ${percentage}% of the way! ⭐`;
    return null;
  };

  return {
    overallScore,
    streak,
    hasAnyEngagement,
    getMotivationalMessage
  };
};
