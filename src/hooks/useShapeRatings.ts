
import { useState, useEffect } from "react";
import { useDateUtils } from "./useDateUtils";

// Define the type for shape ratings
type ShapeRatings = {
  sleep: number;
  hydration: number;
  activity: number;
  poop: number;
  eating: number;
};

interface ShapeRatingsState {
  shapeRatings: ShapeRatings;
  lastResetDate: string;
}

export const useShapeRatings = (petId: string) => {
  const { getTodayDateString } = useDateUtils();

  // Initialize default ratings
  const getDefaultRatings = (): ShapeRatings => ({
    sleep: 0,
    hydration: 0,
    activity: 0,
    poop: 0,
    eating: 0
  });

  const [ratingsState, setRatingsState] = useState<Record<string, ShapeRatingsState>>({});

  // Get current pet's ratings
  const getCurrentRatings = (): ShapeRatings => {
    return ratingsState[petId]?.shapeRatings || getDefaultRatings();
  };

  // Initialize pet ratings if they don't exist
  useEffect(() => {
    if (!ratingsState[petId]) {
      setRatingsState(prev => ({
        ...prev,
        [petId]: {
          shapeRatings: getDefaultRatings(),
          lastResetDate: getTodayDateString()
        }
      }));
    }
  }, [petId, getTodayDateString]);

  // Check if we need to reset ratings for a new day
  useEffect(() => {
    const today = getTodayDateString();
    const petState = ratingsState[petId];
    
    if (petState && petState.lastResetDate !== today) {
      console.log('Resetting SHAPE ratings for new day:', today, 'for pet:', petId);
      setRatingsState(prev => ({
        ...prev,
        [petId]: {
          shapeRatings: getDefaultRatings(),
          lastResetDate: today
        }
      }));
    }
  }, [petId, getTodayDateString, ratingsState]);

  const handleRating = (category: keyof ShapeRatings, rating: number) => {
    console.log('Setting rating for', category, ':', rating, 'for pet:', petId);
    
    setRatingsState(prev => ({
      ...prev,
      [petId]: {
        ...prev[petId],
        shapeRatings: { 
          ...prev[petId]?.shapeRatings, 
          [category]: rating 
        }
      }
    }));
  };

  return {
    shapeRatings: getCurrentRatings(),
    handleRating
  };
};
