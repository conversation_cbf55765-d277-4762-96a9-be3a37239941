
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { usePetData } from "@/context/PetContext";

interface SupplementRecommendation {
  id: string;
  name: string;
  reason: string;
  description: string;
  dosage: string;
  benefits: string[];
  urgency: 'low' | 'medium' | 'high';
  basedOn: string[];
}

export const useSupplementRecommendations = () => {
  const { petData } = usePetData();
  const [recommendations, setRecommendations] = useState<SupplementRecommendation[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const analyzeHealthData = async () => {
    setIsLoading(true);
    try {
      // Fetch recent health check-ins (last 7 days)
      const { data: recentCheckIns, error } = await supabase
        .from('daily_health_checkins')
        .select('*')
        .eq('pet_id', petData.id)
        .gte('date', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0])
        .order('date', { ascending: false });

      if (error) throw error;

      const mockRecommendations: SupplementRecommendation[] = [];

      // Analyze stool patterns
      const softStoolCount = recentCheckIns?.filter(check => 
        check.stool_type && check.stool_type >= 6
      ).length || 0;

      if (softStoolCount >= 3) {
        mockRecommendations.push({
          id: 'gut-restore-1',
          name: 'Gut Restore+ Probiotic',
          reason: `${petData.name} has had ${softStoolCount} days of soft stool in the past week. A probiotic can help restore healthy gut bacteria balance.`,
          description: 'Advanced probiotic blend specifically formulated for digestive health support in pets.',
          dosage: '1 chew daily with food',
          benefits: [
            'Supports healthy digestion',
            'Promotes beneficial gut bacteria',
            'May improve stool consistency',
            'Supports immune system'
          ],
          urgency: 'medium',
          basedOn: [`${softStoolCount} days of soft stool`, 'Digestive concerns']
        });
      }

      // Analyze scratching patterns
      const highScratchingCount = recentCheckIns?.filter(check => 
        check.scratching_frequency && check.scratching_frequency >= 4
      ).length || 0;

      if (highScratchingCount >= 2) {
        mockRecommendations.push({
          id: 'skin-coat-1',
          name: 'Skin & Coat Defense',
          reason: `${petData.name} has shown increased scratching/itching patterns. Omega-3 supplements can help support skin health.`,
          description: 'Omega-3 rich supplement designed to support healthy skin and reduce inflammation.',
          dosage: '1 capsule daily with largest meal',
          benefits: [
            'Supports healthy skin barrier',
            'Reduces inflammation',
            'Promotes shiny coat',
            'May reduce excessive scratching'
          ],
          urgency: 'medium',
          basedOn: [`${highScratchingCount} days of increased scratching`, 'Skin concerns']
        });
      }

      // Analyze appetite patterns
      const poorAppetiteCount = recentCheckIns?.filter(check => 
        check.appetite_level && check.appetite_level <= 2
      ).length || 0;

      if (poorAppetiteCount >= 2) {
        mockRecommendations.push({
          id: 'appetite-boost-1',
          name: 'Digestive Enzymes',
          reason: `${petData.name} has shown reduced appetite lately. Digestive enzymes can help improve nutrient absorption and appetite.`,
          description: 'Natural digestive enzyme blend to support healthy digestion and nutrient absorption.',
          dosage: '1/2 scoop mixed with food twice daily',
          benefits: [
            'Improves nutrient absorption',
            'Supports healthy digestion',
            'May increase appetite',
            'Reduces digestive discomfort'
          ],
          urgency: 'low',
          basedOn: [`${poorAppetiteCount} days of poor appetite`, 'Digestive support']
        });
      }

      setRecommendations(mockRecommendations);
    } catch (error) {
      console.error('Error analyzing health data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const startSupplement = async (supplementName: string) => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { error } = await supabase
        .from('supplement_events')
        .insert({
          pet_id: petData.id,
          user_id: user.id,
          tenant_id: petData.tenant_id,
          supplement_name: supplementName,
          event_type: 'started',
          event_date: new Date().toISOString().split('T')[0]
        });

      if (error) throw error;

      // Remove recommendation after starting
      setRecommendations(prev => 
        prev.filter(rec => rec.name !== supplementName)
      );

      return true;
    } catch (error) {
      console.error('Error starting supplement:', error);
      return false;
    }
  };

  const dismissRecommendation = (recommendationId: string) => {
    setRecommendations(prev =>
      prev.filter(rec => rec.id !== recommendationId)
    );
  };

  useEffect(() => {
    if (petData.id) {
      analyzeHealthData();
    }
  }, [petData.id]);

  return {
    recommendations,
    isLoading,
    refreshRecommendations: analyzeHealthData,
    startSupplement,
    dismissRecommendation
  };
};
