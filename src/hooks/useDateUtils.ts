
import { useState, useEffect } from "react";

export const useDateUtils = () => {
  // Get today's date as YYYY-MM-DD string (local timezone)
  const getTodayDateString = () => {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  const [today] = useState(getTodayDateString());

  return {
    getTodayDateString,
    today
  };
};
