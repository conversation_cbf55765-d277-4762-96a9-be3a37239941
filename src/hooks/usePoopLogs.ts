
import { useState } from "react";
import { PoopLog } from "@/types/poop";

export const usePoopLogs = (petId: string) => {
  const [poopLogsState, setPoopLogsState] = useState<Record<string, PoopLog[]>>({});

  // Get current pet's poop logs
  const getCurrentPoopLogs = (): PoopLog[] => {
    return poopLogsState[petId] || [];
  };

  const handleAddPoopLog = (log: PoopLog) => {
    console.log('Adding poop log for pet:', petId, log);
    
    setPoopLogsState(prev => {
      const currentLogs = prev[petId] || [];
      const filtered = currentLogs.filter(existingLog => existingLog.date !== log.date);
      const newLogs = [...filtered, log].sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
      
      return {
        ...prev,
        [petId]: newLogs
      };
    });
  };

  return {
    poopLogs: getCurrentPoopLogs(),
    handleAddPoopLog
  };
};
