
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useDateUtils } from "./useDateUtils";

interface Supplement {
  id: string;
  name: string;
  dosage: string;
  timing: string;
  type: 'supplement' | 'medication';
  given: boolean;
  givenAt?: string;
  notes?: string;
}

interface SupplementAdherence {
  id?: string;
  supplementName: string;
  supplementType: 'supplement' | 'medication';
  dosage?: string;
  timing?: string;
  given: boolean;
  givenAt?: string;
  notes?: string;
}

export const useSupplementAdherence = (petId: string) => {
  const { getTodayDateString } = useDateUtils();
  const [supplements, setSupplements] = useState<Supplement[]>([]);
  const [adherenceRate, setAdherenceRate] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  // Load pet's supplements and medications from profile
  const loadPetSupplements = async () => {
    try {
      setIsLoading(true);
      
      // Get medications from pet_medical_records
      const { data: medications, error: medError } = await supabase
        .from('pet_medical_records')
        .select('*')
        .eq('pet_id', petId)
        .in('record_type', ['medication', 'supplement'])
        .eq('status', 'active');

      if (medError) throw medError;

      // Get today's adherence records
      const today = getTodayDateString();
      const { data: todayAdherence, error: adhError } = await supabase
        .from('daily_supplement_adherence')
        .select('*')
        .eq('pet_id', petId)
        .eq('date', today);

      if (adhError) throw adhError;

      // Combine medications with today's adherence status
      const supplementsList: Supplement[] = [];
      
      // Add medications/supplements from medical records
      medications?.forEach((med) => {
        const todayRecord = todayAdherence?.find(
          (adh) => adh.supplement_name === med.name
        );
        
        supplementsList.push({
          id: med.id,
          name: med.name,
          dosage: med.dosage || '',
          timing: med.frequency || 'As needed',
          type: med.record_type as 'supplement' | 'medication',
          given: todayRecord?.given || false,
          givenAt: todayRecord?.given_at,
          notes: todayRecord?.notes
        });
      });

      // Add some default supplements if none exist (for demo purposes)
      if (supplementsList.length === 0) {
        supplementsList.push(
          {
            id: 'default-1',
            name: 'Gut Restore Probiotic',
            dosage: '1 capsule',
            timing: 'Morning with food',
            type: 'supplement',
            given: false
          },
          {
            id: 'default-2',
            name: 'Omega-3 Support',
            dosage: '1 pump',
            timing: 'Evening',
            type: 'supplement',
            given: false
          }
        );
      }

      setSupplements(supplementsList);
      
      // Calculate adherence rate
      const totalSupplements = supplementsList.length;
      const givenCount = supplementsList.filter(s => s.given).length;
      setAdherenceRate(totalSupplements > 0 ? Math.round((givenCount / totalSupplements) * 100) : 0);
      
    } catch (error) {
      console.error('Error loading supplements:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Toggle supplement adherence
  const toggleSupplement = async (supplementId: string) => {
    const supplement = supplements.find(s => s.id === supplementId);
    if (!supplement) return;

    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const today = getTodayDateString();
      const newGivenStatus = !supplement.given;

      // Update or insert adherence record
      const { error } = await supabase
        .from('daily_supplement_adherence')
        .upsert({
          pet_id: petId,
          user_id: user.id,
          tenant_id: user.user_metadata?.tenant_id || 'temp-tenant-id',
          date: today,
          supplement_name: supplement.name,
          supplement_type: supplement.type,
          dosage: supplement.dosage,
          timing: supplement.timing,
          given: newGivenStatus,
          given_at: newGivenStatus ? new Date().toISOString() : null
        }, {
          onConflict: 'pet_id,date,supplement_name'
        });

      if (error) throw error;

      // Update local state
      setSupplements(prev => prev.map(s => 
        s.id === supplementId 
          ? { ...s, given: newGivenStatus, givenAt: newGivenStatus ? new Date().toISOString() : undefined }
          : s
      ));

      // Recalculate adherence rate
      const updatedSupplements = supplements.map(s => 
        s.id === supplementId ? { ...s, given: newGivenStatus } : s
      );
      const totalSupplements = updatedSupplements.length;
      const givenCount = updatedSupplements.filter(s => s.given).length;
      setAdherenceRate(totalSupplements > 0 ? Math.round((givenCount / totalSupplements) * 100) : 0);

    } catch (error) {
      console.error('Error toggling supplement:', error);
    }
  };

  // Load supplements on component mount and when petId changes
  useEffect(() => {
    if (petId) {
      loadPetSupplements();
    }
  }, [petId]);

  return {
    supplements,
    adherenceRate,
    isLoading,
    toggleSupplement,
    refreshSupplements: loadPetSupplements
  };
};
