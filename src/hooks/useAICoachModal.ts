
import { useState, useEffect } from "react";
import { Message } from "@/types/chat";
import { chatApi } from "@/services/chatApi";
import { usePetData } from "@/context/PetContext";

export const useAICoachModal = () => {
  const { petData } = usePetData();
  const [isOpen, setIsOpen] = useState(false);
  const [currentThreadId, setCurrentThreadId] = useState<string | undefined>();
  const [messages, setMessages] = useState<Message[]>([]);
  const [isTyping, setIsTyping] = useState(false);

  // Initialize welcome message when modal opens for the first time
  useEffect(() => {
    if (isOpen && messages.length === 0) {
      setMessages([
        {
          id: 1,
          sender: "ai",
          text: `Hey, I'm ${petData.name}'s AI coach! I'm here to help you with any questions about ${petData.name}'s health, nutrition, behavior, and wellness. What would you like to know today?`,
          timestamp: new Date(),
        },
      ]);
    }
  }, [isOpen, petData.name, messages.length]);

  const handleSendMessage = async (text: string) => {
    if (text.trim() === "") return;

    // Add user message
    const userMessage: Message = {
      id: messages.length + 1,
      sender: "user",
      text: text,
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setIsTyping(true);

    try {
      const response = await chatApi.sendMessage(text, currentThreadId);
      
      if (response.success && response.message) {
        if (response.threadId && !currentThreadId) {
          setCurrentThreadId(response.threadId);
        }

        const aiMessage: Message = {
          id: messages.length + 2,
          sender: "ai",
          text: response.message,
          timestamp: new Date(),
          threadId: response.threadId,
          citations: response.citations,
          sources: response.sources,
        };

        setMessages((prev) => [...prev, aiMessage]);
      } else {
        const errorMessage: Message = {
          id: messages.length + 2,
          sender: "ai",
          text: response.error || "I'm sorry, I encountered an error. Please try again.",
          timestamp: new Date(),
          error: true,
        };

        setMessages((prev) => [...prev, errorMessage]);
      }
    } catch (error) {
      console.error('Error sending message:', error);
      const errorMessage: Message = {
        id: messages.length + 2,
        sender: "ai",
        text: "I'm sorry, I'm having trouble connecting right now. Please try again.",
        timestamp: new Date(),
        error: true,
      };

      setMessages((prev) => [...prev, errorMessage]);
    } finally {
      setIsTyping(false);
    }
  };

  return {
    isOpen,
    setIsOpen,
    messages,
    isTyping,
    handleSendMessage,
  };
};
