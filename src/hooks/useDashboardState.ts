
import { useState, useEffect } from "react";
import { PersonalizedRecommendation } from "@/types/shape";
import { PoopLog } from "@/types/poop";
import { usePetData } from "@/context/PetContext";

export const useDashboardState = () => {
  const { petData } = usePetData();
  
  // Modal state - Updated to use letter instead of icon
  const [modalOpen, setModalOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<{
    key: keyof typeof shapeRatings;
    title: string;
    letter: string;
    description: string;
  } | null>(null);
  
  // Get today's date as YYYY-MM-DD string (local timezone)
  const getTodayDateString = () => {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };
  
  // Initialize SHAPE ratings - always start with 0 (uncompleted) values
  const getInitialShapeRatings = () => ({
    sleep: 0,
    hydration: 0,
    activity: 0,
    poop: 0,
    eating: 0
  });
  
  // SHAPE ratings state with daily reset functionality
  const [shapeRatings, setShapeRatings] = useState(getInitialShapeRatings);
  const [lastResetDate, setLastResetDate] = useState(getTodayDateString());
  
  // Check if we need to reset ratings for a new day
  useEffect(() => {
    const today = getTodayDateString();
    if (lastResetDate !== today) {
      // Reset all ratings to 0 for the new day
      setShapeRatings(getInitialShapeRatings());
      setLastResetDate(today);
      console.log('SHAPE ratings reset for new day:', today);
    }
  }, [lastResetDate]);
  
  // Add poop logging state
  const [poopLogs, setPoopLogs] = useState<PoopLog[]>([
    // Sample data - in real app would come from database
    {
      id: "log-1",
      date: "2025-06-08", // Yesterday's date in YYYY-MM-DD format
      dogName: petData.name,
      type: 4,
      easeOfPickup: 'easy',
      color: 'brown',
      odor: 'normal',
      frequency: true,
      straining: 'none',
      score: 95,
      feedback: "Excellent! That's a healthy, well-formed stool 👏",
      completed: true
    }
  ]);
  
  // Auto-calculate poop rating from logs
  useEffect(() => {
    const today = getTodayDateString();
    const todayLog = poopLogs.find(log => log.date === today);
    
    if (todayLog && todayLog.completed) {
      // Convert score (0-100) to rating (1-5)
      const rating = todayLog.score >= 90 ? 5 :
                    todayLog.score >= 80 ? 4 :
                    todayLog.score >= 60 ? 3 :
                    todayLog.score >= 40 ? 2 : 1;
      
      setShapeRatings(prev => ({ ...prev, poop: rating }));
      console.log('Updated poop rating to:', rating, 'based on score:', todayLog.score);
    } else {
      setShapeRatings(prev => ({ ...prev, poop: 0 }));
    }
  }, [poopLogs]);
  
  const [supplements, setSupplements] = useState([
    { name: "Gut Restore", dosage: "1 capsule", timing: "Morning", given: true },
    { name: "Digestive Enzymes", dosage: "1/2 scoop", timing: "With meals", given: false },
    { name: "Omega Support", dosage: "1 pump", timing: "Evening", given: false }
  ]);
  
  const recommendations: PersonalizedRecommendation[] = [
    {
      id: "1",
      category: "coaching",
      title: "Discuss Sleep Concerns",
      description: `${petData.name}'s sleep quality has been inconsistent. Let's explore potential causes.`,
      priority: "medium",
      action: "Chat with AI Coach",
      confidenceScore: 85,
      targetedIssues: ["Inconsistent sleep patterns", "Restlessness at night"],
      expectedOutcome: "Better sleep quality and more consistent rest schedule",
      timeToEffect: "1-2 weeks",
      supportingEvidence: "Behavioral interventions show 78% improvement in sleep quality"
    },
    {
      id: "2",
      category: "activity",
      title: "Increase Daily Exercise",
      description: "Adding 15 minutes to daily walks could improve energy and digestion.",
      priority: "low",
      action: "Update Exercise Plan",
      confidenceScore: 72,
      targetedIssues: ["Low energy levels", "Digestive sluggishness"],
      expectedOutcome: "Increased energy and improved digestive health",
      timeToEffect: "2-3 weeks",
      supportingEvidence: "Exercise increases show 65% improvement in overall wellness metrics"
    },
    {
      id: "3",
      category: "health",
      title: "Schedule Next Test",
      description: "Your next microbiome test is due in 2 weeks.",
      priority: "medium",
      action: "Order Test",
      confidenceScore: 95,
      targetedIssues: ["Monitor progress", "Track microbiome changes"],
      expectedOutcome: "Updated health insights and adjusted recommendations",
      timeToEffect: "Immediate data",
      supportingEvidence: "Regular testing improves health outcomes by 40%"
    }
  ];
  
  const adherenceRate = Math.round((supplements.filter(s => s.given).length / supplements.length) * 100);
  const overallScore = Object.values(shapeRatings).filter(rating => rating > 0).length > 0 
    ? Object.values(shapeRatings).reduce((a, b) => a + b, 0) / Object.values(shapeRatings).length 
    : 0;
  const todayCompleted = Object.values(shapeRatings).every(rating => rating > 0);
  
  // Helper function to check if user has any engagement
  const hasAnyEngagement = () => {
    return Object.values(shapeRatings).some(rating => rating > 0);
  };
  
  // Helper function to get motivational message
  const getMotivationalMessage = (completedCount: number, totalCount: number) => {
    const percentage = Math.round((completedCount / totalCount) * 100);
    if (percentage >= 100) return "Amazing! You've completed everything today! 🎉";
    if (percentage >= 80) return `You're ${percentage}% there! Just one more to go! 💪`;
    if (percentage >= 60) return `Great job! You're ${percentage}% there! 🎯`;
    if (percentage >= 40) return `Keep going! You're ${percentage}% done! 🚀`;
    if (percentage >= 20) return `Good start! You're ${percentage}% of the way! ⭐`;
    return null;
  };
  
  const handleRating = (category: keyof typeof shapeRatings, rating: number) => {
    // Don't allow manual poop rating - it's auto-calculated
    if (category === 'poop') return;
    setShapeRatings(prev => ({ ...prev, [category]: rating }));
  };
  
  const handleToggleSupplement = (index: number) => {
    setSupplements(prev => 
      prev.map((supplement, i) => 
        i === index ? { ...supplement, given: !supplement.given } : supplement
      )
    );
  };

  // Updated to use letter instead of icon
  const handleCardClick = (category: keyof typeof shapeRatings, title: string, letter: string, description: string) => {
    setSelectedCategory({ key: category, title, letter, description });
    setModalOpen(true);
  };

  const handleModalRate = (rating: number) => {
    if (selectedCategory) {
      handleRating(selectedCategory.key, rating);
    }
  };
  
  const handleAddPoopLog = (log: PoopLog) => {
    console.log('Adding poop log:', log);
    setPoopLogs(prev => {
      const filtered = prev.filter(existingLog => existingLog.date !== log.date);
      const newLogs = [...filtered, log].sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
      console.log('Updated poop logs:', newLogs);
      return newLogs;
    });
  };

  return {
    modalOpen,
    setModalOpen,
    selectedCategory,
    shapeRatings,
    supplements,
    recommendations,
    adherenceRate,
    overallScore,
    todayCompleted,
    hasAnyEngagement,
    getMotivationalMessage,
    handleRating,
    handleToggleSupplement,
    handleCardClick,
    handleModalRate,
    poopLogs,
    handleAddPoopLog
  };
};
