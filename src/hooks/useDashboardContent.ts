
import { useState } from "react";
import { useDashboardManager } from "@/hooks/useDashboardManager";
import { usePetSpecificDashboardState } from "@/hooks/usePetSpecificDashboardState";

export const useDashboardContent = () => {
  const {
    dashboardState,
    pets,
    activePet,
    recommendations,
    isAICoachOpen,
    messages,
    isTyping,
    handleSelectPet,
    handleAddPet,
    startSupplement,
    dismissRecommendation,
    setIsAICoachOpen,
    handleSendMessage
  } = useDashboardManager();

  const {
    shapeRatings,
    poopLogs,
    streak,
    overallScore,
    hasAnyEngagement,
    getMotivationalMessage,
    handleRating,
    handleAddPoopLog
  } = usePetSpecificDashboardState(activePet);

  const [checkInModalOpen, setCheckInModalOpen] = useState(false);

  // Calculate completion metrics
  const completedCategories = Object.values(shapeRatings).filter(rating => rating > 0).length;
  const totalCategories = Object.keys(shapeRatings).length;
  const completionRate = hasAnyEngagement() ? (completedCategories / totalCategories) * 100 : 0;
  const isFullyCompleted = completedCategories === totalCategories && hasAnyEngagement();
  
  // Get motivational message
  const motivationalMessage = hasAnyEngagement() ? getMotivationalMessage(completedCategories, totalCategories) : null;

  // Calculate SHAPE score (0-100)
  const shapeScore = overallScore > 0 ? Math.round(overallScore * 20) : 0;

  // Get today's poop log
  const getTodayDateString = () => {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  const today = getTodayDateString();
  const todayLog = poopLogs.find(log => log.date === today);

  const handleCardClick = (category: keyof typeof shapeRatings, title: string, letter: string, description: string) => {
    console.log('Card clicked:', category, 'Opening unified modal');
    setCheckInModalOpen(true);
  };

  const handleQuickAction = () => {
    console.log('Quick action clicked, opening unified modal');
    setCheckInModalOpen(true);
  };

  return {
    // Dashboard Manager State
    dashboardState,
    pets,
    activePet,
    recommendations,
    isAICoachOpen,
    messages,
    isTyping,
    handleSelectPet,
    handleAddPet,
    startSupplement,
    dismissRecommendation,
    setIsAICoachOpen,
    handleSendMessage,
    
    // Pet Specific State
    shapeRatings,
    poopLogs,
    streak,
    overallScore,
    hasAnyEngagement,
    getMotivationalMessage,
    handleRating,
    handleAddPoopLog,
    
    // Computed Values
    completedCategories,
    totalCategories,
    completionRate,
    isFullyCompleted,
    motivationalMessage,
    shapeScore,
    todayLog,
    
    // Modal State
    checkInModalOpen,
    setCheckInModalOpen,
    
    // Handlers
    handleCardClick,
    handleQuickAction
  };
};
