
import { useEffect } from "react";
import { PoopLog } from "@/types/poop";
import { useShapeRatings } from "./useShapeRatings";
import { usePoopLogs } from "./usePoopLogs";
import { usePetMetrics } from "./usePetMetrics";
import { useSupplementAdherence } from "./useSupplementAdherence";
import { useDateUtils } from "./useDateUtils";

interface Pet {
  id: string;
  name: string;
  breed: string;
  age: number;
  avatar?: string;
}

export const usePetSpecificDashboardState = (activePet: Pet) => {
  const { getTodayDateString } = useDateUtils();
  const { shapeRatings, handleRating } = useShapeRatings(activePet.id);
  const { poopLogs, handleAddPoopLog: addPoopLog } = usePoopLogs(activePet.id);
  const { supplements, adherenceRate, toggleSupplement } = useSupplementAdherence(activePet.id);
  const { overallScore, streak, hasAnyEngagement, getMotivationalMessage } = usePetMetrics(shapeRatings);

  // Enhanced poop log handler that also updates poop rating
  const handleAddPoopLog = (log: PoopLog) => {
    // Add the log
    addPoopLog(log);
    
    // Calculate poop rating from the new log
    const rating = log.score >= 90 ? 5 :
                  log.score >= 80 ? 4 :
                  log.score >= 60 ? 3 :
                  log.score >= 40 ? 2 : 1;
    
    // Update poop rating
    handleRating('poop', rating);
  };

  // Enhanced engagement check that includes supplement adherence
  const hasAnyEngagementEnhanced = () => {
    const hasShapeEngagement = Object.values(shapeRatings).some(rating => rating > 0);
    const hasSupplementEngagement = supplements.length > 0 && supplements.some(s => s.given);
    return hasShapeEngagement || hasSupplementEngagement;
  };

  // Enhanced overall score calculation including supplement adherence
  const calculateEnhancedOverallScore = () => {
    const shapeScore = overallScore;
    const supplementScore = adherenceRate / 100; // Convert percentage to 0-1 scale
    
    // Weight: 80% SHAPE, 20% supplements
    if (shapeScore > 0 || supplementScore > 0) {
      return (shapeScore * 0.8) + (supplementScore * 0.2);
    }
    return 0;
  };

  return {
    shapeRatings,
    poopLogs,
    supplements,
    adherenceRate,
    streak,
    overallScore: calculateEnhancedOverallScore(),
    hasAnyEngagement: hasAnyEngagementEnhanced,
    getMotivationalMessage,
    handleRating,
    handleAddPoopLog,
    toggleSupplement
  };
};
