
import { useState, useEffect } from 'react';

export const useKeyboardDetection = () => {
  const [isKeyboardOpen, setIsKeyboardOpen] = useState(false);
  const [viewportHeight, setViewportHeight] = useState(0);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Initial viewport height
    const initialHeight = window.visualViewport?.height || window.innerHeight;
    setViewportHeight(initialHeight);

    const handleVisualViewportChange = () => {
      if (window.visualViewport) {
        const currentHeight = window.visualViewport.height;
        const heightDifference = initialHeight - currentHeight;
        
        // Consider keyboard open if viewport height reduced by more than 150px
        const keyboardThreshold = 150;
        setIsKeyboardOpen(heightDifference > keyboardThreshold);
        setViewportHeight(currentHeight);
      }
    };

    // Fallback for older browsers without Visual Viewport API
    const handleResize = () => {
      if (!window.visualViewport) {
        const currentHeight = window.innerHeight;
        const heightDifference = initialHeight - currentHeight;
        const keyboardThreshold = 150;
        setIsKeyboardOpen(heightDifference > keyboardThreshold);
        setViewportHeight(currentHeight);
      }
    };

    // Use Visual Viewport API if available, otherwise fall back to resize
    if (window.visualViewport) {
      window.visualViewport.addEventListener('resize', handleVisualViewportChange);
    } else {
      window.addEventListener('resize', handleResize);
    }

    return () => {
      if (window.visualViewport) {
        window.visualViewport.removeEventListener('resize', handleVisualViewportChange);
      } else {
        window.removeEventListener('resize', handleResize);
      }
    };
  }, []);

  return { isKeyboardOpen, viewportHeight };
};
