
import * as React from "react"

const MOBILE_BREAKPOINT = 768

export function useIsMobile() {
  const [isMobile, setIsMobile] = React.useState<boolean>(false)

  React.useEffect(() => {
    if (typeof window === "undefined") return
    
    // Function to check if the device is mobile
    const checkIfMobile = () => {
      const width = window.innerWidth
      const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0
      setIsMobile(width < MOBILE_BREAKPOINT || (width < 1024 && isTouchDevice))
    }
    
    // Initial check
    checkIfMobile()
    
    // Set up event listener with debounce
    let timeoutId: ReturnType<typeof setTimeout>
    const handleResize = () => {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(checkIfMobile, 150)
    }
    
    window.addEventListener("resize", handleResize)
    window.addEventListener("orientationchange", handleResize)
    
    // Clean up
    return () => {
      window.removeEventListener("resize", handleResize)
      window.removeEventListener("orientationchange", handleResize)
      clearTimeout(timeoutId)
    }
  }, [])

  return isMobile
}
