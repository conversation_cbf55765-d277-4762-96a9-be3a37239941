
import { useState } from "react";
import { usePetData } from "@/context/PetContext";
import { useSupplementRecommendations } from "@/hooks/useSupplementRecommendations";
import { useAICoachModal } from "@/hooks/useAICoachModal";
import { usePetSpecificDashboardState } from "@/hooks/usePetSpecificDashboardState";
import { useToast } from "@/hooks/use-toast";

interface Pet {
  id: string;
  name: string;
  breed: string;
  age: number;
  avatar?: string;
}

export const useDashboardManager = () => {
  const { petData } = usePetData();
  const { toast } = useToast();
  const { recommendations, startSupplement, dismissRecommendation } = useSupplementRecommendations();
  
  const {
    isOpen: isAICoachOpen,
    setIsOpen: setIsAICoachOpen,
    messages,
    isTyping,
    handleSendMessage
  } = useAICoachModal();
  
  const [dashboardState] = useState({
    lastCheckIn: null as string | null,
    weeklyGoal: 7,
    completedDays: 4,
    currentStreak: 3,
    totalLogs: 127,
    avgScore: 4.2,
    improvement: 12
  });

  // Mock data for multi-pet support (in real app would come from context/database)
  const [pets] = useState<Pet[]>([
    {
      id: petData.id,
      name: petData.name,
      breed: petData.breed || "Golden Retriever",
      age: petData.age || 36,
      avatar: petData.photo_url
    },
    {
      id: "pet-2",
      name: "Max",
      breed: "German Shepherd",
      age: 48,
      avatar: undefined
    },
    {
      id: "pet-3", 
      name: "Bella",
      breed: "Labrador",
      age: 24,
      avatar: undefined
    }
  ]);

  const [activePet, setActivePet] = useState(pets[0]);

  const handleSelectPet = (pet: Pet) => {
    setActivePet(pet);
    toast({
      title: `Switched to ${pet.name}`,
      description: `Now viewing ${pet.name}'s dashboard data.`,
    });
  };

  const handleAddPet = () => {
    console.log('Add new pet');
  };

  return {
    // State
    dashboardState,
    pets,
    activePet,
    recommendations,
    isAICoachOpen,
    messages,
    isTyping,
    
    // Actions
    setActivePet,
    handleSelectPet,
    handleAddPet,
    startSupplement,
    dismissRecommendation,
    setIsAICoachOpen,
    handleSendMessage
  };
};
