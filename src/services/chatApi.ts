
import { supabase } from '@/integrations/supabase/client';

interface Citation {
  id: string;
  text: string;
  sourceId: string;
  position: number;
}

interface Source {
  id: string;
  title: string;
  url?: string;
  type: 'document' | 'web' | 'research' | 'knowledge';
  description?: string;
  relevance?: number;
}

interface ChatResponse {
  success: boolean;
  message?: string;
  error?: string;
  threadId?: string;
  citations?: Citation[];
  sources?: Source[];
}

class ChatApi {
  async sendMessage(content: string, threadId?: string): Promise<ChatResponse> {
    try {
      const { data, error } = await supabase.functions.invoke('chat-with-ai', {
        body: {
          content,
          threadId,
        },
      });

      if (error) {
        console.error('Supabase function error:', error);
        return {
          success: false,
          error: error.message || 'Failed to connect to AI service',
        };
      }

      return data as ChatResponse;
    } catch (error: any) {
      console.error('Chat API error:', error);
      return {
        success: false,
        error: error.message || 'Network error occurred',
      };
    }
  }

  // Keep this method for backward compatibility, but it always returns true now
  hasApiKey(): boolean {
    return true;
  }

  // These methods are no longer needed but kept for backward compatibility
  setApiKey(apiKey: string) {
    // No-op - API key is managed server-side
  }

  setAssistantId(id: string) {
    // No-op - Assistant ID is managed server-side
  }
}

export const chatApi = new ChatApi();
