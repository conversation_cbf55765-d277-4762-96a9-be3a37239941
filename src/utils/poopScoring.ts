
import { PoopLog, POOP_TYPES } from "@/types/poop";

export const calculatePoopScore = (log: Partial<PoopLog>): { score: number; feedback: string } => {
  let baseScore = 0;
  let feedback = "";

  // Base score from poop type
  if (log.type) {
    const poopType = POOP_TYPES.find(t => t.id === log.type);
    baseScore = poopType?.healthScore || 50;
  }

  // Adjust for ease of pickup
  if (log.easeOfPickup === 'mushy') baseScore -= 20;
  else if (log.easeOfPickup === 'sticky') baseScore -= 10;

  // Color flags
  if (log.color === 'red' || log.color === 'black') {
    baseScore = Math.min(baseScore, 30);
    feedback = "Abnormal color detected. Consider consulting your vet if this continues.";
  } else if (log.color === 'pale') {
    baseScore -= 15;
    feedback = "Pale stool may indicate digestive issues. Monitor closely.";
  }

  // Straining penalty
  if (log.straining === 'a-lot') baseScore -= 25;
  else if (log.straining === 'mild') baseScore -= 10;

  // Odor adjustment
  if (log.odor === 'sour') baseScore -= 15;
  else if (log.odor === 'strong') baseScore -= 5;

  // Frequency check
  if (log.frequency === false) {
    baseScore = Math.min(baseScore, 40);
    feedback = "No bowel movement today. Ensure adequate water and fiber intake.";
  }

  // Generate positive feedback for good scores
  if (baseScore >= 85 && !feedback) {
    feedback = "Excellent! That's a healthy, well-formed stool 👏";
  } else if (baseScore >= 70 && !feedback) {
    feedback = "Good digestive health! Keep up the great work.";
  } else if (baseScore >= 50 && !feedback) {
    feedback = "Acceptable range, but there's room for improvement.";
  } else if (!feedback) {
    feedback = "Signs of digestive upset. Consider dietary adjustments or consult your vet.";
  }

  return {
    score: Math.max(0, Math.min(100, baseScore)),
    feedback
  };
};

export const getHealthTrend = (logs: PoopLog[], days: number = 7): string => {
  const recentLogs = logs.slice(-days);
  const avgScore = recentLogs.reduce((sum, log) => sum + log.score, 0) / recentLogs.length;
  
  if (avgScore >= 80) return "Excellent digestive health!";
  if (avgScore >= 65) return "Good digestive health";
  if (avgScore >= 50) return "Fair digestive health";
  return "Needs attention";
};

export const checkForAlerts = (logs: PoopLog[]): string[] => {
  const alerts: string[] = [];
  const recent = logs.slice(-3);
  
  // Check for consecutive loose stools
  const looseCount = recent.filter(log => log.type >= 6).length;
  if (looseCount >= 2) {
    alerts.push("Multiple days of loose stools detected");
  }
  
  // Check for no poop days
  const noPoop = recent.filter(log => !log.frequency).length;
  if (noPoop >= 2) {
    alerts.push("Missed bowel movements - possible constipation");
  }
  
  return alerts;
};
