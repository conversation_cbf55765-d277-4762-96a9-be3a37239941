
import { toast } from "@/hooks/use-toast";

export interface ShareData {
  title: string;
  text: string;
  url?: string;
  image?: Blob;
}

export interface ShareOptions {
  petName: string;
  shapeScore: number;
  streak: number;
}

// Check if Web Share API is supported
export const isWebShareSupported = (): boolean => {
  return typeof navigator !== 'undefined' && 'share' in navigator;
};

// Check if Web Share API supports files
export const isWebShareFilesSupported = (): boolean => {
  return isWebShareSupported() && 'canShare' in navigator && navigator.canShare({ files: [] });
};

// Generate share content
export const generateShareContent = (options: ShareOptions): ShareData => {
  const { petName, shapeScore, streak } = options;
  
  const title = `${petName}'s Health Achievement! 🎉`;
  const streakText = streak > 0 ? ` We're on a ${streak} day streak!` : '';
  const text = `${petName} completed today's SHAPE check-in with a score of ${shapeScore}!${streakText} #PetHealth #SHAPE`;
  
  return {
    title,
    text,
    url: window.location.origin
  };
};

// Create achievement image as blob
export const createAchievementImage = async (options: ShareOptions): Promise<Blob | null> => {
  try {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) return null;

    // Set canvas dimensions
    canvas.width = 800;
    canvas.height = 400;

    // Create gradient background
    const gradient = ctx.createLinearGradient(0, 0, 800, 400);
    gradient.addColorStop(0, '#10b981');
    gradient.addColorStop(1, '#059669');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, 800, 400);

    // Add text content
    ctx.fillStyle = 'white';
    ctx.font = 'bold 48px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(`${options.petName}'s Achievement! 🎉`, 400, 120);

    ctx.font = '32px Arial';
    ctx.fillText(`SHAPE Score: ${options.shapeScore}`, 400, 200);

    if (options.streak > 0) {
      ctx.fillText(`${options.streak} Day Streak!`, 400, 260);
    }

    ctx.font = '24px Arial';
    ctx.fillText('Powered by Pet Health Tracker', 400, 340);

    // Convert canvas to blob
    return new Promise((resolve) => {
      canvas.toBlob(resolve, 'image/png');
    });
  } catch (error) {
    console.error('Error creating achievement image:', error);
    return null;
  }
};

// Main share function
export const shareAchievement = async (options: ShareOptions): Promise<void> => {
  const shareData = generateShareContent(options);

  try {
    // Try Web Share API first (best for mobile)
    if (isWebShareSupported()) {
      // Try to share with image if supported
      if (isWebShareFilesSupported()) {
        const imageBlob = await createAchievementImage(options);
        if (imageBlob) {
          const file = new File([imageBlob], 'achievement.png', { type: 'image/png' });
          if (navigator.canShare({ files: [file] })) {
            await navigator.share({
              title: shareData.title,
              text: shareData.text,
              files: [file]
            });
            return;
          }
        }
      }

      // Fallback to text-only Web Share
      await navigator.share({
        title: shareData.title,
        text: shareData.text,
        url: shareData.url
      });
      return;
    }

    // Fallback: Copy to clipboard and show social options
    await copyToClipboard(shareData.text);
    toast({
      title: "Achievement copied to clipboard! 📋",
      description: "Share it on your favorite social platform",
    });

  } catch (error) {
    if (error instanceof Error && error.name === 'AbortError') {
      // User cancelled the share - don't show error
      return;
    }
    
    console.error('Share failed:', error);
    
    // Final fallback: copy to clipboard
    try {
      await copyToClipboard(shareData.text);
      toast({
        title: "Share ready! 📋",
        description: "Text copied to clipboard - paste it anywhere to share",
      });
    } catch (clipboardError) {
      toast({
        title: "Share not available",
        description: "Sharing is not supported on this device",
        variant: "destructive"
      });
    }
  }
};

// Copy text to clipboard
const copyToClipboard = async (text: string): Promise<void> => {
  if (navigator.clipboard && navigator.clipboard.writeText) {
    await navigator.clipboard.writeText(text);
  } else {
    // Fallback for older browsers
    const textarea = document.createElement('textarea');
    textarea.value = text;
    document.body.appendChild(textarea);
    textarea.select();
    document.execCommand('copy');
    document.body.removeChild(textarea);
  }
};

// Add haptic feedback (if available)
export const triggerHapticFeedback = (): void => {
  if ('vibrate' in navigator) {
    navigator.vibrate(50); // Short vibration
  }
};
