// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://yqtcurkwaaurfrqzujog.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlxdGN1cmt3YWF1cmZycXp1am9nIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgyNjc5NzEsImV4cCI6MjA2Mzg0Mzk3MX0._-RAKCxvFJ-tDZd4LwtW5Gu5ImZKGXa31swiSJHbffI";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);