export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      appointments: {
        Row: {
          appointment_type: string
          created_at: string
          id: string
          notes: string | null
          scheduled_date: string
          status: string
          updated_at: string
          user_id: string
        }
        Insert: {
          appointment_type?: string
          created_at?: string
          id?: string
          notes?: string | null
          scheduled_date: string
          status?: string
          updated_at?: string
          user_id: string
        }
        Update: {
          appointment_type?: string
          created_at?: string
          id?: string
          notes?: string | null
          scheduled_date?: string
          status?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      daily_health_checkins: {
        Row: {
          ai_response: string | null
          ai_thread_id: string | null
          appetite_level: number | null
          created_at: string
          date: string
          energy_level: number | null
          id: string
          notes: string | null
          pet_id: string
          scratching_frequency: number | null
          stool_color: string | null
          stool_consistency: string | null
          stool_type: number | null
          tenant_id: string
          updated_at: string
          user_id: string
          vomiting: boolean | null
        }
        Insert: {
          ai_response?: string | null
          ai_thread_id?: string | null
          appetite_level?: number | null
          created_at?: string
          date: string
          energy_level?: number | null
          id?: string
          notes?: string | null
          pet_id: string
          scratching_frequency?: number | null
          stool_color?: string | null
          stool_consistency?: string | null
          stool_type?: number | null
          tenant_id: string
          updated_at?: string
          user_id: string
          vomiting?: boolean | null
        }
        Update: {
          ai_response?: string | null
          ai_thread_id?: string | null
          appetite_level?: number | null
          created_at?: string
          date?: string
          energy_level?: number | null
          id?: string
          notes?: string | null
          pet_id?: string
          scratching_frequency?: number | null
          stool_color?: string | null
          stool_consistency?: string | null
          stool_type?: number | null
          tenant_id?: string
          updated_at?: string
          user_id?: string
          vomiting?: boolean | null
        }
        Relationships: [
          {
            foreignKeyName: "daily_health_checkins_pet_id_fkey"
            columns: ["pet_id"]
            isOneToOne: false
            referencedRelation: "pets"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "daily_health_checkins_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      daily_supplement_adherence: {
        Row: {
          created_at: string
          date: string
          dosage: string | null
          given: boolean
          given_at: string | null
          id: string
          notes: string | null
          pet_id: string
          supplement_name: string
          supplement_type: string
          tenant_id: string
          timing: string | null
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          date: string
          dosage?: string | null
          given?: boolean
          given_at?: string | null
          id?: string
          notes?: string | null
          pet_id: string
          supplement_name: string
          supplement_type: string
          tenant_id: string
          timing?: string | null
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          date?: string
          dosage?: string | null
          given?: boolean
          given_at?: string | null
          id?: string
          notes?: string | null
          pet_id?: string
          supplement_name?: string
          supplement_type?: string
          tenant_id?: string
          timing?: string | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "daily_supplement_adherence_pet_id_fkey"
            columns: ["pet_id"]
            isOneToOne: false
            referencedRelation: "pets"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "daily_supplement_adherence_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      pet_documents: {
        Row: {
          created_at: string
          document_category: string
          file_name: string
          file_type: string
          file_url: string
          id: string
          pet_id: string
          service_date: string | null
          tags: string[] | null
          tenant_id: string
          user_id: string
          vet_name: string | null
        }
        Insert: {
          created_at?: string
          document_category: string
          file_name: string
          file_type: string
          file_url: string
          id?: string
          pet_id: string
          service_date?: string | null
          tags?: string[] | null
          tenant_id: string
          user_id: string
          vet_name?: string | null
        }
        Update: {
          created_at?: string
          document_category?: string
          file_name?: string
          file_type?: string
          file_url?: string
          id?: string
          pet_id?: string
          service_date?: string | null
          tags?: string[] | null
          tenant_id?: string
          user_id?: string
          vet_name?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "pet_documents_pet_id_fkey"
            columns: ["pet_id"]
            isOneToOne: false
            referencedRelation: "pets"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pet_documents_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      pet_medical_records: {
        Row: {
          created_at: string
          dosage: string | null
          due_date: string | null
          end_date: string | null
          frequency: string | null
          id: string
          name: string
          notes: string | null
          pet_id: string
          record_type: string
          start_date: string | null
          status: string | null
          tenant_id: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          dosage?: string | null
          due_date?: string | null
          end_date?: string | null
          frequency?: string | null
          id?: string
          name: string
          notes?: string | null
          pet_id: string
          record_type: string
          start_date?: string | null
          status?: string | null
          tenant_id: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          dosage?: string | null
          due_date?: string | null
          end_date?: string | null
          frequency?: string | null
          id?: string
          name?: string
          notes?: string | null
          pet_id?: string
          record_type?: string
          start_date?: string | null
          status?: string | null
          tenant_id?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "pet_medical_records_pet_id_fkey"
            columns: ["pet_id"]
            isOneToOne: false
            referencedRelation: "pets"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pet_medical_records_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      pet_weight_history: {
        Row: {
          created_at: string
          id: string
          notes: string | null
          pet_id: string
          recorded_date: string
          tenant_id: string
          user_id: string
          weight_lbs: number
        }
        Insert: {
          created_at?: string
          id?: string
          notes?: string | null
          pet_id: string
          recorded_date: string
          tenant_id: string
          user_id: string
          weight_lbs: number
        }
        Update: {
          created_at?: string
          id?: string
          notes?: string | null
          pet_id?: string
          recorded_date?: string
          tenant_id?: string
          user_id?: string
          weight_lbs?: number
        }
        Relationships: [
          {
            foreignKeyName: "pet_weight_history_pet_id_fkey"
            columns: ["pet_id"]
            isOneToOne: false
            referencedRelation: "pets"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pet_weight_history_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      pets: {
        Row: {
          age_months: number | null
          body_condition_score: number | null
          breed: string | null
          created_at: string
          current_health_status: string | null
          dietary_preferences: string[] | null
          goals: string[] | null
          health_issues: string[] | null
          id: string
          microchip_id: string | null
          name: string
          owner_id: string
          photo_url: string | null
          sex: string | null
          tenant_id: string
          type: string
          updated_at: string
          vet_clinic_id: string | null
          weight_lbs: number | null
        }
        Insert: {
          age_months?: number | null
          body_condition_score?: number | null
          breed?: string | null
          created_at?: string
          current_health_status?: string | null
          dietary_preferences?: string[] | null
          goals?: string[] | null
          health_issues?: string[] | null
          id?: string
          microchip_id?: string | null
          name: string
          owner_id: string
          photo_url?: string | null
          sex?: string | null
          tenant_id: string
          type: string
          updated_at?: string
          vet_clinic_id?: string | null
          weight_lbs?: number | null
        }
        Update: {
          age_months?: number | null
          body_condition_score?: number | null
          breed?: string | null
          created_at?: string
          current_health_status?: string | null
          dietary_preferences?: string[] | null
          goals?: string[] | null
          health_issues?: string[] | null
          id?: string
          microchip_id?: string | null
          name?: string
          owner_id?: string
          photo_url?: string | null
          sex?: string | null
          tenant_id?: string
          type?: string
          updated_at?: string
          vet_clinic_id?: string | null
          weight_lbs?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "pets_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      poop_logs: {
        Row: {
          color: string | null
          consistency: string | null
          created_at: string
          date: string
          ease_of_pickup: string | null
          id: string
          notes: string | null
          pet_id: string
          photo_url: string | null
          score: number
          tenant_id: string
          type_id: number
          updated_at: string
          user_id: string
        }
        Insert: {
          color?: string | null
          consistency?: string | null
          created_at?: string
          date: string
          ease_of_pickup?: string | null
          id?: string
          notes?: string | null
          pet_id: string
          photo_url?: string | null
          score: number
          tenant_id: string
          type_id: number
          updated_at?: string
          user_id: string
        }
        Update: {
          color?: string | null
          consistency?: string | null
          created_at?: string
          date?: string
          ease_of_pickup?: string | null
          id?: string
          notes?: string | null
          pet_id?: string
          photo_url?: string | null
          score?: number
          tenant_id?: string
          type_id?: number
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "poop_logs_pet_id_fkey"
            columns: ["pet_id"]
            isOneToOne: false
            referencedRelation: "pets"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "poop_logs_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          address_line1: string | null
          address_line2: string | null
          city: string | null
          country: string | null
          created_at: string
          current_tenant_id: string | null
          full_name: string | null
          health_issues: string[] | null
          id: string
          onboarding_completed_at: string | null
          owner_email: string | null
          pet_age_months: number | null
          pet_breed: string | null
          pet_name: string | null
          pet_sex: string | null
          pet_type: string | null
          pet_weight_lbs: number | null
          phone_number: string | null
          sms_notifications_enabled: boolean | null
          state: string | null
          tenant_id: string | null
          updated_at: string
          zip_code: string | null
        }
        Insert: {
          address_line1?: string | null
          address_line2?: string | null
          city?: string | null
          country?: string | null
          created_at?: string
          current_tenant_id?: string | null
          full_name?: string | null
          health_issues?: string[] | null
          id: string
          onboarding_completed_at?: string | null
          owner_email?: string | null
          pet_age_months?: number | null
          pet_breed?: string | null
          pet_name?: string | null
          pet_sex?: string | null
          pet_type?: string | null
          pet_weight_lbs?: number | null
          phone_number?: string | null
          sms_notifications_enabled?: boolean | null
          state?: string | null
          tenant_id?: string | null
          updated_at?: string
          zip_code?: string | null
        }
        Update: {
          address_line1?: string | null
          address_line2?: string | null
          city?: string | null
          country?: string | null
          created_at?: string
          current_tenant_id?: string | null
          full_name?: string | null
          health_issues?: string[] | null
          id?: string
          onboarding_completed_at?: string | null
          owner_email?: string | null
          pet_age_months?: number | null
          pet_breed?: string | null
          pet_name?: string | null
          pet_sex?: string | null
          pet_type?: string | null
          pet_weight_lbs?: number | null
          phone_number?: string | null
          sms_notifications_enabled?: boolean | null
          state?: string | null
          tenant_id?: string | null
          updated_at?: string
          zip_code?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "profiles_current_tenant_id_fkey"
            columns: ["current_tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "profiles_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      sms_preferences: {
        Row: {
          assessment_reminders: boolean | null
          created_at: string
          daily_reminders: boolean | null
          id: string
          preferred_time: string | null
          progress_updates: boolean | null
          timezone: string | null
          updated_at: string
          user_id: string
        }
        Insert: {
          assessment_reminders?: boolean | null
          created_at?: string
          daily_reminders?: boolean | null
          id?: string
          preferred_time?: string | null
          progress_updates?: boolean | null
          timezone?: string | null
          updated_at?: string
          user_id: string
        }
        Update: {
          assessment_reminders?: boolean | null
          created_at?: string
          daily_reminders?: boolean | null
          id?: string
          preferred_time?: string | null
          progress_updates?: boolean | null
          timezone?: string | null
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      subscribers: {
        Row: {
          billing_cycle: string | null
          created_at: string
          email: string
          id: string
          stripe_customer_id: string | null
          subscribed: boolean
          subscription_end: string | null
          subscription_tier: string | null
          updated_at: string
          user_id: string | null
        }
        Insert: {
          billing_cycle?: string | null
          created_at?: string
          email: string
          id?: string
          stripe_customer_id?: string | null
          subscribed?: boolean
          subscription_end?: string | null
          subscription_tier?: string | null
          updated_at?: string
          user_id?: string | null
        }
        Update: {
          billing_cycle?: string | null
          created_at?: string
          email?: string
          id?: string
          stripe_customer_id?: string | null
          subscribed?: boolean
          subscription_end?: string | null
          subscription_tier?: string | null
          updated_at?: string
          user_id?: string | null
        }
        Relationships: []
      }
      supplement_events: {
        Row: {
          created_at: string
          dosage: string | null
          event_date: string
          event_type: string
          frequency: string | null
          id: string
          notes: string | null
          pet_id: string
          supplement_name: string
          tenant_id: string
          user_id: string
        }
        Insert: {
          created_at?: string
          dosage?: string | null
          event_date?: string
          event_type: string
          frequency?: string | null
          id?: string
          notes?: string | null
          pet_id: string
          supplement_name: string
          tenant_id: string
          user_id: string
        }
        Update: {
          created_at?: string
          dosage?: string | null
          event_date?: string
          event_type?: string
          frequency?: string | null
          id?: string
          notes?: string | null
          pet_id?: string
          supplement_name?: string
          tenant_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "supplement_events_pet_id_fkey"
            columns: ["pet_id"]
            isOneToOne: false
            referencedRelation: "pets"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "supplement_events_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      tenant_users: {
        Row: {
          accepted_at: string | null
          created_at: string
          id: string
          invited_at: string | null
          invited_by: string | null
          role: Database["public"]["Enums"]["tenant_role"]
          tenant_id: string
          updated_at: string
          user_id: string
        }
        Insert: {
          accepted_at?: string | null
          created_at?: string
          id?: string
          invited_at?: string | null
          invited_by?: string | null
          role?: Database["public"]["Enums"]["tenant_role"]
          tenant_id: string
          updated_at?: string
          user_id: string
        }
        Update: {
          accepted_at?: string | null
          created_at?: string
          id?: string
          invited_at?: string | null
          invited_by?: string | null
          role?: Database["public"]["Enums"]["tenant_role"]
          tenant_id?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "tenant_users_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      tenants: {
        Row: {
          accent_color: string | null
          address: Json | null
          brand_logo_url: string | null
          brand_name: string | null
          contact_email: string | null
          contact_phone: string | null
          created_at: string
          custom_domain: string | null
          features: Json | null
          id: string
          max_pets: number | null
          max_users: number | null
          name: string
          primary_color: string | null
          secondary_color: string | null
          slug: string
          status: Database["public"]["Enums"]["tenant_status"]
          subscription_ends_at: string | null
          updated_at: string
        }
        Insert: {
          accent_color?: string | null
          address?: Json | null
          brand_logo_url?: string | null
          brand_name?: string | null
          contact_email?: string | null
          contact_phone?: string | null
          created_at?: string
          custom_domain?: string | null
          features?: Json | null
          id?: string
          max_pets?: number | null
          max_users?: number | null
          name: string
          primary_color?: string | null
          secondary_color?: string | null
          slug: string
          status?: Database["public"]["Enums"]["tenant_status"]
          subscription_ends_at?: string | null
          updated_at?: string
        }
        Update: {
          accent_color?: string | null
          address?: Json | null
          brand_logo_url?: string | null
          brand_name?: string | null
          contact_email?: string | null
          contact_phone?: string | null
          created_at?: string
          custom_domain?: string | null
          features?: Json | null
          id?: string
          max_pets?: number | null
          max_users?: number | null
          name?: string
          primary_color?: string | null
          secondary_color?: string | null
          slug?: string
          status?: Database["public"]["Enums"]["tenant_status"]
          subscription_ends_at?: string | null
          updated_at?: string
        }
        Relationships: []
      }
      vet_clinics: {
        Row: {
          address: string | null
          created_at: string
          email: string | null
          id: string
          name: string
          phone: string | null
          tenant_id: string
          updated_at: string
        }
        Insert: {
          address?: string | null
          created_at?: string
          email?: string | null
          id?: string
          name: string
          phone?: string | null
          tenant_id: string
          updated_at?: string
        }
        Update: {
          address?: string | null
          created_at?: string
          email?: string | null
          id?: string
          name?: string
          phone?: string | null
          tenant_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "vet_clinics_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      get_user_tenant: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      user_has_tenant_access: {
        Args: { tenant_id: string }
        Returns: boolean
      }
    }
    Enums: {
      tenant_role: "owner" | "admin" | "staff" | "viewer"
      tenant_status: "active" | "suspended" | "trial" | "cancelled"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      tenant_role: ["owner", "admin", "staff", "viewer"],
      tenant_status: ["active", "suspended", "trial", "cancelled"],
    },
  },
} as const
