# Mac Development Cache Cleaner Scripts

This repository contains two powerful scripts to help you reclaim disk space on your Mac by cleaning development-related caches and system data.

## 🚀 Quick Start

### Basic Cache Cleaning (Safe)
```bash
./clean_dev_caches.sh
```
**Expected space freed: 20-40GB**

### Advanced System Cleaning (Use with caution)
```bash
./clean_system_advanced.sh
```
**Expected space freed: 50-100GB+**

## 📋 Scripts Overview

### 1. `clean_dev_caches.sh` - Safe Development Cache Cleaner

**What it cleans:**
- ✅ Google Chrome caches
- ✅ Android Studio caches (all versions)
- ✅ Xcode build caches
- ✅ CocoaPods cache
- ✅ pip (Python) cache
- ✅ Homebrew cache
- ✅ Poetry (Python) cache
- ✅ Composer (PHP) cache
- ✅ TypeScript cache
- ✅ Visual Studio cache
- ✅ Unity Editor cache
- ✅ Node.js gyp cache

**Features:**
- 🔍 Shows before/after sizes for each cache
- 📊 Detailed impact measurement
- 🎨 Colored output for easy reading
- ⚠️ Safely closes applications before cleaning
- 📈 Progress tracking and summary report

**Safety Level:** ⭐⭐⭐⭐⭐ (Very Safe)
- Only cleans cache files
- Applications will regenerate caches as needed
- No risk of data loss

### 2. `clean_system_advanced.sh` - Advanced System Cleaner

**What it can clean:**
- 🔧 Android SDK (selective or complete)
- 📱 iOS Simulators (all versions)
- 🏗️ Xcode Archives
- 📦 Xcode Derived Data
- 🎮 Unity cache and temporary files

**Features:**
- 🤔 Interactive prompts for each component
- ⚠️ Clear warnings for destructive actions
- 📋 Shows component sizes before cleaning
- 🎯 Selective cleaning options
- 🔐 Requires confirmation for major deletions

**Safety Level:** ⭐⭐⭐ (Use with caution)
- Removes development tools and simulators
- Can be reinstalled but takes time
- Make sure you have backups

## 📊 Expected Results

### Before Running Scripts
```
System Data: ~224GB
├── Android SDK: 67GB
├── iOS Simulators: 53GB
├── Development Caches: 39GB
├── Application Support: 21GB
└── Other: 44GB
```

### After Basic Cleaning (`clean_dev_caches.sh`)
```
System Data: ~188GB (-36GB)
├── Android SDK: 67GB (unchanged)
├── iOS Simulators: 53GB (unchanged)
├── Development Caches: 3GB (-36GB) ✅
├── Application Support: 21GB (unchanged)
└── Other: 44GB (unchanged)
```

### After Advanced Cleaning (both scripts)
```
System Data: ~50-80GB (-144-174GB)
├── Android SDK: 0-20GB (-47-67GB) ✅
├── iOS Simulators: 0GB (-53GB) ✅
├── Development Caches: 3GB (-36GB) ✅
├── Application Support: 21GB (unchanged)
└── Other: 26-36GB (-8-18GB) ✅
```

## 🛠️ Usage Instructions

### Running the Basic Cleaner

1. **Open Terminal**
2. **Navigate to the script directory:**
   ```bash
   cd /path/to/scripts
   ```
3. **Run the script:**
   ```bash
   ./clean_dev_caches.sh
   ```
4. **Follow the prompts:**
   - Confirm you want to proceed
   - Wait for completion
   - Review the summary

### Running the Advanced Cleaner

1. **⚠️ IMPORTANT: Backup your work first**
2. **Close all development applications**
3. **Run the script:**
   ```bash
   ./clean_system_advanced.sh
   ```
4. **Choose what to clean:**
   - iOS Simulators (can be reinstalled)
   - Android SDK (selective or complete)
   - Xcode data (can be regenerated)

## 🔧 What Each Cache Does

| Cache Type | Purpose | Safe to Delete? | Regeneration |
|------------|---------|-----------------|--------------|
| Chrome | Web browsing data | ✅ Yes | Automatic |
| Android Studio | IDE caches | ✅ Yes | On next launch |
| Xcode | Build artifacts | ✅ Yes | On next build |
| CocoaPods | iOS dependency cache | ✅ Yes | On next install |
| pip | Python package cache | ✅ Yes | On next install |
| Homebrew | Package cache | ✅ Yes | On next install |
| iOS Simulators | Device emulation | ⚠️ Caution | Manual reinstall |
| Android SDK | Development tools | ⚠️ Caution | Manual reinstall |

## 🚨 Troubleshooting

### Script Won't Run
```bash
# Make sure script is executable
chmod +x clean_dev_caches.sh
chmod +x clean_system_advanced.sh
```

### Permission Denied
```bash
# Some caches require sudo (script will prompt)
# Make sure you have admin privileges
```

### Applications Won't Close
- Manually close Chrome, Android Studio, and Xcode
- Press Enter when prompted to continue

### Want to Restore Something?
- **Caches:** Will regenerate automatically
- **iOS Simulators:** Reinstall via Xcode → Window → Devices and Simulators
- **Android SDK:** Reinstall via Android Studio → SDK Manager

## 📈 Monitoring Results

### Check Storage After Cleaning
1. **Apple Menu → About This Mac → Storage**
2. **Or use Terminal:**
   ```bash
   df -h
   du -sh ~/Library/Caches
   ```

### Verify Cache Sizes
```bash
# Check remaining cache sizes
du -sh ~/Library/Caches/* | sort -hr | head -10

# Check total Library size
du -sh ~/Library
```

## 🔄 Regular Maintenance

### Weekly (Automated)
```bash
# Add to crontab for weekly cleaning
0 2 * * 0 /path/to/clean_dev_caches.sh
```

### Monthly
- Run advanced cleaner if needed
- Check for new cache directories
- Update scripts for new development tools

## ⚠️ Important Notes

1. **Always backup important work before running advanced cleaner**
2. **Close development applications before running scripts**
3. **Scripts are designed for macOS development environments**
4. **Some operations require admin privileges (sudo)**
5. **Regenerating caches may slow down applications initially**

## 🆘 Support

If you encounter issues:
1. Check the colored output for specific error messages
2. Ensure you have proper permissions
3. Try running individual commands manually
4. Close all applications and try again

---

**Created by:** Augment Agent  
**Last Updated:** $(date)  
**Tested on:** macOS with development tools installed
