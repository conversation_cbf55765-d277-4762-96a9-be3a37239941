#!/bin/bash

# =============================================================================
# Advanced System Cleaner Script
# =============================================================================
# This script cleans larger system components like Android SDK and iOS Simulators
# WARNING: This script removes development tools and simulators
# Created: $(date)
# Author: Augment Agent
# =============================================================================

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_header() {
    echo -e "${PURPLE}============================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}============================================${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_size() {
    echo -e "${CYAN}📊 $1${NC}"
}

# Function to get directory size in human readable format
get_size_human() {
    local dir="$1"
    if [ -d "$dir" ]; then
        du -sh "$dir" 2>/dev/null | cut -f1
    else
        echo "0B"
    fi
}

# Function to clean with confirmation
clean_with_confirmation() {
    local name="$1"
    local path="$2"
    local description="$3"
    local warning="$4"
    
    if [ ! -d "$path" ]; then
        print_warning "$name not found at $path"
        return 0
    fi
    
    local size=$(get_size_human "$path")
    
    print_info "$name found: $size"
    print_info "Description: $description"
    if [ -n "$warning" ]; then
        print_warning "WARNING: $warning"
    fi
    
    read -p "Do you want to clean $name? (y/N): " -n 1 -r
    echo ""
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_info "Cleaning $name..."
        rm -rf "$path"
        print_success "$name cleaned! Freed: $size"
        return 1
    else
        print_info "Skipped $name"
        return 0
    fi
}

# Function to clean iOS simulators
clean_ios_simulators() {
    print_header "iOS Simulator Cleanup"
    
    print_info "Checking iOS Simulators..."
    
    # Check user simulators
    local user_sim_size=$(get_size_human "$HOME/Library/Developer/CoreSimulator")
    print_size "User iOS Simulators: $user_sim_size"
    
    # Check system simulators
    local system_sim_size=$(get_size_human "/Library/Developer/CoreSimulator")
    print_size "System iOS Simulators: $system_sim_size"
    
    print_warning "This will remove ALL iOS simulators and their data"
    print_info "You can reinstall them later through Xcode"
    
    read -p "Clean iOS Simulators? (y/N): " -n 1 -r
    echo ""
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_info "Cleaning iOS Simulators..."
        
        # Clean using Xcode tools first
        print_info "Using Xcode tools to clean simulators..."
        xcrun simctl delete unavailable 2>/dev/null
        xcrun simctl erase all 2>/dev/null
        
        # Clean user simulator data
        if [ -d "$HOME/Library/Developer/CoreSimulator" ]; then
            rm -rf "$HOME/Library/Developer/CoreSimulator"/*
            print_success "User simulator data cleaned"
        fi
        
        # Clean system simulator data (requires sudo)
        if [ -d "/Library/Developer/CoreSimulator" ]; then
            print_info "Cleaning system simulator data (requires sudo)..."
            sudo rm -rf /Library/Developer/CoreSimulator/*
            print_success "System simulator data cleaned"
        fi
        
        print_success "iOS Simulators cleaned! Freed: ~$(echo "$user_sim_size + $system_sim_size" | bc 2>/dev/null || echo "50GB+")"
    else
        print_info "Skipped iOS Simulators"
    fi
    
    echo ""
}

# Function to clean Android SDK selectively
clean_android_sdk() {
    print_header "Android SDK Cleanup"
    
    local android_sdk_path="$HOME/Library/Android/sdk"
    
    if [ ! -d "$android_sdk_path" ]; then
        print_warning "Android SDK not found"
        return 0
    fi
    
    local total_size=$(get_size_human "$android_sdk_path")
    print_size "Total Android SDK size: $total_size"
    
    print_info "Android SDK components:"
    
    # Show platform versions
    if [ -d "$android_sdk_path/platforms" ]; then
        print_info "Platform versions:"
        ls "$android_sdk_path/platforms" 2>/dev/null | while read platform; do
            local size=$(get_size_human "$android_sdk_path/platforms/$platform")
            echo -e "${CYAN}  $platform: $size${NC}"
        done
    fi
    
    # Show system images
    if [ -d "$android_sdk_path/system-images" ]; then
        print_info "System images:"
        find "$android_sdk_path/system-images" -maxdepth 3 -type d -name "*x86*" -o -name "*arm*" 2>/dev/null | while read img; do
            local size=$(get_size_human "$img")
            echo -e "${CYAN}  $(basename "$img"): $size${NC}"
        done
    fi
    
    echo ""
    print_warning "You can clean specific components or the entire SDK"
    print_info "Options:"
    echo "  1. Clean old platform versions (keep latest 2-3)"
    echo "  2. Clean system images (emulator images)"
    echo "  3. Clean entire Android SDK"
    echo "  4. Skip Android SDK cleanup"
    
    read -p "Choose option (1-4): " -n 1 -r
    echo ""
    
    case $REPLY in
        1)
            print_info "Cleaning old Android platforms..."
            # Keep only the latest 3 platforms
            if [ -d "$android_sdk_path/platforms" ]; then
                cd "$android_sdk_path/platforms"
                ls -1 | sort -V | head -n -3 | while read old_platform; do
                    local size=$(get_size_human "$old_platform")
                    rm -rf "$old_platform"
                    print_success "Removed $old_platform ($size)"
                done
            fi
            ;;
        2)
            print_info "Cleaning Android system images..."
            if [ -d "$android_sdk_path/system-images" ]; then
                rm -rf "$android_sdk_path/system-images"/*
                print_success "System images cleaned"
            fi
            ;;
        3)
            print_warning "This will remove the ENTIRE Android SDK!"
            read -p "Are you sure? (y/N): " -n 1 -r
            echo ""
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                rm -rf "$android_sdk_path"
                print_success "Entire Android SDK removed! Freed: $total_size"
            fi
            ;;
        4)
            print_info "Skipped Android SDK cleanup"
            ;;
        *)
            print_warning "Invalid option, skipping Android SDK cleanup"
            ;;
    esac
    
    echo ""
}

# Main script
print_header "Advanced System Cleaner"
print_warning "This script can free 50-100GB+ but removes development tools"
print_info "Make sure you have backups and can reinstall tools if needed"
echo ""

read -p "Do you want to proceed with advanced cleaning? (y/N): " -n 1 -r
echo ""
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_warning "Operation cancelled by user"
    exit 0
fi

echo ""

# Show current large directories
print_header "Current Large Directories"
print_info "Largest directories in ~/Library:"
du -sh "$HOME/Library"/* 2>/dev/null | sort -hr | head -10 | while read size dir; do
    echo -e "${CYAN}  $size${NC} - $(basename "$dir")"
done
echo ""

# Clean iOS Simulators
clean_ios_simulators

# Clean Android SDK
clean_android_sdk

# Additional cleanups
print_header "Additional Cleanup Options"

# Unity cache
clean_with_confirmation "Unity Cache" "$HOME/Library/Unity" "Unity editor cache and temporary files" "Will not affect Unity projects"

# Xcode archives
clean_with_confirmation "Xcode Archives" "$HOME/Library/Developer/Xcode/Archives" "Old app archives and builds" "You can regenerate these"

# Derived data
clean_with_confirmation "Xcode Derived Data" "$HOME/Library/Developer/Xcode/DerivedData" "Build artifacts and indexes" "Xcode will regenerate these"

print_header "Advanced Cleaning Complete"
print_success "Advanced system cleaning finished!"
print_info "Check your storage in System Preferences to see the impact"
echo ""
